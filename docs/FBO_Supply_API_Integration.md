# Ozon FBO 供应管理 API 集成

本文档描述了在测试中集成的 Ozon FBO 供应管理 API 接口，特别是时间段选择和供应单创建功能。

## 新增的 API 接口

### 1. `/v1/draft/timeslot/info` - 获取可用时间段

**功能**: 获取指定草稿和仓库的可用时间段信息

**请求结构**:
```go
type DraftTimeslotInfoRequest struct {
    DateFrom     time.Time `json:"date_from"`     // 查询开始日期
    DateTo       time.Time `json:"date_to"`       // 查询结束日期
    DraftID      int64     `json:"draft_id"`      // 草稿ID
    WarehouseIDs []int64   `json:"warehouse_ids"` // 仓库ID列表
}
```

**响应结构**:
```go
type DraftTimeslotInfoResponse struct {
    DropOffWarehouseTimeslots []WarehouseTimeslot `json:"drop_off_warehouse_timeslots"`
    RequestedDateFrom         time.Time           `json:"requested_date_from"`
    RequestedDateTo           time.Time           `json:"requested_date_to"`
}

type WarehouseTimeslot struct {
    DropOffWarehouseID int64 `json:"drop_off_warehouse_id"`
    Days               []struct {
        DateInTimezone time.Time  `json:"date_in_timezone"`
        Timeslots      []Timeslot `json:"timeslots"`
    } `json:"days"`
}

type Timeslot struct {
    FromInTimezone time.Time `json:"from_in_timezone"`
    ToInTimezone   time.Time `json:"to_in_timezone"`
}
```

### 2. `/v1/draft/supply/create` - 从草稿创建供应单

**功能**: 根据草稿、选定的仓库和时间段创建供应单

**请求结构**:
```go
type DraftSupplyCreateRequest struct {
    DraftID     int64     `json:"draft_id"`      // 草稿ID
    Timeslot    *Timeslot `json:"timeslot"`      // 选择的时间段
    WarehouseID int64     `json:"warehouse_id"`  // 仓库ID
}
```

**响应**: 返回操作ID字符串，用于后续查询状态

### 3. `/v1/draft/supply/create/status` - 获取供应单创建状态

**功能**: 查询供应单创建操作的状态

**响应结构**:
```go
type DraftSupplyCreateStatusResponse struct {
    ErrorMessages []string `json:"error_messages"`
    Result        struct {
        OrderIDs []int64 `json:"order_ids"`
    } `json:"result"`
    Status string `json:"status"` // 状态值
}
```

## 业务逻辑实现

### 时间段选择规则

1. **最少提前天数**: 选择的时间段必须至少在当前时间的4天后
2. **优先级**: 选择最早符合条件的时间段
3. **仓库选择**: 从有可用时间段的仓库中选择第一个

### 核心算法

```go
func selectValidTimeslot(timeslotInfo *DraftTimeslotInfoResponse, minDaysFromNow int) (int64, *Timeslot) {
    now := time.Now()
    
    for _, warehouseTimeslot := range timeslotInfo.DropOffWarehouseTimeslots {
        for _, day := range warehouseTimeslot.Days {
            daysFromNow := int(day.DateInTimezone.Sub(now).Hours() / 24)
            
            // 检查是否满足最少天数要求
            if daysFromNow < minDaysFromNow {
                continue
            }
            
            // 选择第一个可用时间段
            if len(day.Timeslots) > 0 {
                return warehouseTimeslot.DropOffWarehouseID, &day.Timeslots[0]
            }
        }
    }
    
    return 0, nil
}
```

## 完整的工作流程

### 1. 创建供应草稿
```go
draftReq := &ozonapi.DraftCreateRequest{
    ClusterIDs:              []int64{6},
    DropOffPointWarehouseID: 1020002201523000,
    Items: []struct {
        Quantity int   `json:"quantity"`
        SKU      int64 `json:"sku"`
    }{
        {Quantity: 10, SKU: 123456789},
    },
    Type: "CREATE_TYPE_CROSSDOCK",
}

operationID, err := client.FBO.CreateSupplyDraft(draftReq)
```

### 2. 获取草稿信息
```go
draftInfo, err := client.FBO.GetSupplyDraftInfo(operationID)
```

### 3. 获取可用时间段
```go
timeslotReq := &ozonapi.DraftTimeslotInfoRequest{
    DateFrom:     time.Now().AddDate(0, 0, 1), // 明天
    DateTo:       time.Now().AddDate(0, 0, 15), // 15天后
    DraftID:      draftInfo.DraftID,
    WarehouseIDs: warehouseIDs,
}

timeslotInfo, err := client.FBO.GetDraftTimeslotInfo(timeslotReq)
```

### 4. 选择合适的时间段
```go
selectedWarehouseID, selectedTimeslot := selectValidTimeslot(timeslotInfo, 4)
```

### 5. 创建供应单
```go
supplyCreateReq := &ozonapi.DraftSupplyCreateRequest{
    DraftID:     draftInfo.DraftID,
    Timeslot:    selectedTimeslot,
    WarehouseID: selectedWarehouseID,
}

supplyOperationID, err := client.FBO.CreateSupplyFromDraft(supplyCreateReq)
```

### 6. 检查创建状态
```go
supplyStatus, err := client.FBO.GetDraftSupplyCreateStatus(supplyOperationID)
```

## 测试函数

### 主要测试函数
- `TestCreateSupplyDraftFromCluster`: 完整的端到端测试
- `TestGetDraftTimeslotInfo`: 专门测试时间段获取
- `TestCreateSupplyFromDraftWithTimeslot`: 专门测试供应单创建

### 辅助函数
- `selectValidTimeslot`: 选择符合条件的时间段
- `printTimeslotInfo`: 打印时间段信息用于调试

## 错误处理

1. **时间段不足**: 如果没有符合最少天数要求的时间段，会返回相应错误
2. **API 错误**: 所有 API 调用都包含错误处理和状态检查
3. **数据验证**: 在每个步骤都验证返回的数据完整性

## 使用注意事项

1. **API 密钥**: 需要替换测试代码中的实际 API 密钥和客户端ID
2. **时间限制**: 时间段选择必须遵守4天最少提前期的业务规则
3. **仓库可用性**: 不是所有仓库在所有时间都有可用时间段
4. **状态轮询**: 供应单创建可能需要时间，建议实现状态轮询机制

## 示例文件

- `internal/service/test/fbo_test.go`: 主要测试文件
- `internal/service/test/fbo_supply_workflow_example.go`: 完整工作流程示例
