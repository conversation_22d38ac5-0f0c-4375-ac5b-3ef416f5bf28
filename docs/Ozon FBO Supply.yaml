openapi: 3.0.3
info:
  title: Ozon Seller API - FBO Supply Drafts & Cargoes Management
  description: A comprehensive API definition for creating and managing FBO supply requests, from drafts to cargoes and labels. Based on the provided HTML documentation.
  version: "v1"
servers:
  - url: https://api-seller.ozon.ru
    description: <PERSON>on Seller API Server
security:
  - OzonAuth: []
    OzonClientId: []
tags:
  - name: Supply-Preparation
    description: "Подготовка: Получение информации о кластерах и складах (Preparation: Getting info on clusters and warehouses)"
  - name: Supply-Draft
    description: "Черновики заявок: Создание и управление черновиками заявок на поставку (Supply Drafts: Creating and managing supply request drafts)"
  - name: Supply-Order
    description: "Заявки на поставку: Управление созданными заявками (Supply Orders: Managing created supply orders)"
  - name: Cargoes
    description: "Грузоместа: Управление грузоместами и этикетками (Cargoes: Managing cargo units and labels)"

paths:
  # Tag: Supply-Preparation
  /v1/cluster/list:
    post:
      tags:
        - Supply-Preparation
      summary: Информация о кластерах и их складах (Get info about clusters and their warehouses)
      operationId: getClusterAndWarehouseList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClusterListRequest'
      responses:
        '200':
          description: Successful response with cluster information.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClusterListResponse'
        default:
          $ref: '#/components/responses/ErrorResponse'

  /v1/warehouse/fbo/list:
    post:
      tags:
        - Supply-Preparation
      summary: Поиск точек для отгрузки поставки (Search for supply drop-off points)
      operationId: searchFboWarehouses
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WarehouseFboListRequest'
      responses:
        '200':
          description: Successful response with a list of found warehouses.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WarehouseFboListResponse'
        default:
          $ref: '#/components/responses/ErrorResponse'

  # Tag: Supply-Draft
  /v1/draft/create:
    post:
      tags:
        - Supply-Draft
      summary: Создать черновик заявки на поставку (Create a draft supply request)
      operationId: createSupplyDraft
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DraftCreateRequest'
      responses:
        '200':
          description: Draft creation initiated. Returns an operation ID.
          content:
            application/json:
              schema:
                type: object
                properties:
                  operation_id:
                    type: string
                    description: Identifier of the draft creation operation.
        default:
          $ref: '#/components/responses/ErrorResponse'

  /v1/draft/create/info:
    post:
      tags:
        - Supply-Draft
      summary: Информация о черновике заявки на поставку (Get information about a supply request draft)
      operationId: getSupplyDraftInfo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - operation_id
              properties:
                operation_id:
                  type: string
                  description: The operation ID from the draft creation request.
      responses:
        '200':
          description: Detailed information about the draft creation status.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DraftCreateInfoResponse'
        default:
          $ref: '#/components/responses/ErrorResponse'

  /v1/draft/timeslot/info:
    post:
      tags:
        - Supply-Draft
      summary: Доступные таймслоты (Get available timeslots)
      operationId: getDraftTimeslotInfo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DraftTimeslotInfoRequest'
      responses:
        '200':
          description: List of available timeslots for the given warehouses.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DraftTimeslotInfoResponse'
        default:
          $ref: '#/components/responses/ErrorResponse'

  /v1/draft/supply/create:
    post:
      tags:
        - Supply-Draft
      summary: Создать заявку на поставку по черновику (Create supply order from a draft)
      operationId: createSupplyFromDraft
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DraftSupplyCreateRequest'
      responses:
        '200':
          description: Supply order creation initiated. Returns an operation ID.
          content:
            application/json:
              schema:
                type: object
                properties:
                  operation_id:
                    type: string
                    description: Identifier of the supply order creation operation.
        default:
          $ref: '#/components/responses/ErrorResponse'

  /v1/draft/supply/create/status:
    post:
      tags:
        - Supply-Draft
      summary: Информация о создании заявки на поставку (Get status of supply order creation)
      operationId: getDraftSupplyCreateStatus
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - operation_id
              properties:
                operation_id:
                  type: string
                  description: The operation ID from the supply creation request.
      responses:
        '200':
          description: Status of the supply order creation.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DraftSupplyCreateStatusResponse'
        default:
          $ref: '#/components/responses/ErrorResponse'

  # Tag: Cargoes
  /v1/cargoes/create:
    post:
      tags:
        - Cargoes
      summary: Установка грузомест (Set cargo units)
      operationId: createOrUpdateCargoes
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CargoesCreateRequest'
      responses:
        '200':
          description: Cargo creation initiated. Returns an operation ID and any immediate errors.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CargoesCreateResponse'
        default:
          $ref: '#/components/responses/ErrorResponse'

  /v1/cargoes/create/info:
    post:
      tags:
        - Cargoes
      summary: Получить информацию по установке грузомест (Get info on cargo unit setup status)
      operationId: getCargoesCreateInfo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [operation_id]
              properties:
                operation_id:
                  type: string
      responses:
        '200':
          description: Status of the cargo creation process.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CargoesCreateInfoResponse'
        default:
          $ref: '#/components/responses/ErrorResponse'

  /v1/cargoes/delete:
    post:
      tags:
        - Cargoes
      summary: Удалить грузоместо в заявке на поставку (Delete cargo unit in a supply request)
      operationId: deleteCargoes
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CargoesDeleteRequest'
      responses:
        '200':
          description: Cargo deletion initiated. Returns an operation ID and any immediate errors.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CargoesDeleteResponse'
        default:
          $ref: '#/components/responses/ErrorResponse'

  /v1/cargoes/delete/status:
    post:
      tags:
        - Cargoes
      summary: Информация о статусе удаления грузоместа (Get status of cargo unit deletion)
      operationId: getCargoesDeleteStatus
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [operation_id]
              properties:
                operation_id:
                  type: string
      responses:
        '200':
          description: Status of the cargo deletion process.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CargoesDeleteStatusResponse'
        default:
          $ref: '#/components/responses/ErrorResponse'

  /v1/cargoes/rules/get:
    post:
      tags:
        - Cargoes
      summary: Чек-лист по установке грузомест FBO (Get FBO cargo setup checklist)
      operationId: getCargoesRules
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                supply_ids:
                  type: array
                  items:
                    type: string
                    format: int64
      responses:
        '200':
          description: Checklist rules for the given supplies.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CargoesRulesResponse'
        default:
          $ref: '#/components/responses/ErrorResponse'

  /v1/cargoes-label/create:
    post:
      tags:
        - Cargoes
      summary: Сгенерировать этикетки для грузомест (Generate labels for cargo units)
      operationId: createCargoesLabel
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CargoesLabelCreateRequest'
      responses:
        '200':
          description: Label generation initiated. Returns an operation ID.
          content:
            application/json:
              schema:
                type: object
                properties:
                  operation_id:
                    type: string
        default:
          $ref: '#/components/responses/ErrorResponse'

  /v1/cargoes-label/get:
    post:
      tags:
        - Cargoes
      summary: Получить идентификатор этикетки для грузомест (Get label identifier for cargo units)
      operationId: getCargoesLabelInfo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [operation_id]
              properties:
                operation_id:
                  type: string
      responses:
        '200':
          description: Status of label generation and file GUID.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CargoesLabelGetResponse'
        default:
          $ref: '#/components/responses/ErrorResponse'

  /v1/cargoes-label/file/{file_guid}:
    get:
      tags:
        - Cargoes
      summary: Получить PDF с этикетками грузовых мест (Get PDF with cargo unit labels)
      operationId: getCargoesLabelFile
      parameters:
        - name: file_guid
          in: path
          required: true
          schema:
            type: string
          description: The file GUID obtained from the /v1/cargoes-label/get endpoint.
      responses:
        '200':
          description: PDF file with labels.
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        default:
          $ref: '#/components/responses/ErrorResponse'
          
  # Tag: Supply-Order
  /v1/supply-order/cancel:
    post:
      tags:
        - Supply-Order
      summary: Отменить заявку на поставку (Cancel a supply order)
      operationId: cancelSupplyOrder
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [order_id]
              properties:
                order_id:
                  type: integer
                  format: int64
      responses:
        '200':
          description: Cancellation initiated. Returns an operation ID.
          content:
            application/json:
              schema:
                type: object
                properties:
                  operation_id:
                    type: string
        default:
          $ref: '#/components/responses/ErrorResponse'

  /v1/supply-order/cancel/status:
    post:
      tags:
        - Supply-Order
      summary: Получить статус отмены заявки на поставку (Get status of a supply order cancellation)
      operationId: getSupplyOrderCancelStatus
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [operation_id]
              properties:
                operation_id:
                  type: string
      responses:
        '200':
          description: Status of the cancellation process.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SupplyOrderCancelStatusResponse'
        default:
          $ref: '#/components/responses/ErrorResponse'
          
  /v1/supply-order/content/update:
    post:
      tags:
        - Supply-Order
      summary: Редактирование товарного состава (Update supply order content)
      operationId: updateSupplyOrderContent
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SupplyOrderContentUpdateRequest'
      responses:
        '200':
          description: Content update initiated. Returns an operation ID and any immediate errors.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SupplyOrderContentUpdateResponse'
        default:
          $ref: '#/components/responses/ErrorResponse'
          
  /v1/supply-order/content/update/status:
    post:
      tags:
        - Supply-Order
      summary: Информация о статусе редактирования товарного состава (Get status of supply order content update)
      operationId: getSupplyOrderContentUpdateStatus
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [operation_id]
              properties:
                operation_id:
                  type: string
      responses:
        '200':
          description: Status of the content update process.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SupplyOrderContentUpdateStatusResponse'
        default:
          $ref: '#/components/responses/ErrorResponse'


components:
  securitySchemes:
    OzonAuth:
      type: apiKey
      in: header
      name: Api-Key
    OzonClientId:
      type: apiKey
      in: header
      name: Client-Id
  responses:
    ErrorResponse:
      description: Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
  schemas:
    # Common
    Error:
      type: object
      properties:
        code:
          type: integer
        message:
          type: string
        details:
          type: array
          items:
            type: object

    # Supply-Preparation Schemas
    ClusterListRequest:
      type: object
      properties:
        cluster_ids:
          type: array
          items:
            type: string
            format: int64
        cluster_type:
          type: string
          required: true
          enum: ["CLUSTER_TYPE_OZON", "CLUSTER_TYPE_CIS"]
    ClusterListResponse:
      type: object
      properties:
        clusters:
          type: array
          items:
            $ref: '#/components/schemas/DraftCreateInfoCluster'
            
    WarehouseFboListRequest:
      type: object
      required:
        - filter_by_supply_type
        - search
      properties:
        filter_by_supply_type:
          type: array
          items:
            type: string
            enum: ["CREATE_TYPE_CROSSDOCK", "CREATE_TYPE_DIRECT"]
        search:
          type: string
          minLength: 4
    WarehouseFboListResponse:
      type: object
      properties:
        search:
          type: array
          items:
            type: object
            properties:
              address:
                type: string
              coordinates:
                type: object
                properties:
                  latitude:
                    type: number
                  longitude:
                    type: number
              name:
                type: string
              warehouse_id:
                type: integer
                format: int64
              warehouse_type:
                type: string
                enum: ["WAREHOUSE_TYPE_DELIVERY_POINT", "WAREHOUSE_TYPE_ORDERS_RECEIVING_POINT", "WAREHOUSE_TYPE_SORTING_CENTER", "WAREHOUSE_TYPE_FULL_FILLMENT", "WAREHOUSE_TYPE_CROSS_DOCK"]
    
    # Supply-Draft Schemas
    DraftCreateRequest:
      type: object
      required:
        - items
        - type
      properties:
        cluster_ids:
          type: array
          items:
            type: string
            format: int64
        drop_off_point_warehouse_id:
          type: integer
          format: int64
        items:
          type: array
          maxItems: 5000
          items:
            type: object
            properties:
              quantity:
                type: integer
              sku:
                type: integer
                format: int64
        type:
          type: string
          enum: ["CREATE_TYPE_CROSSDOCK", "CREATE_TYPE_DIRECT"]
          
    DraftCreateInfoResponse:
      type: object
      properties:
        clusters:
          type: array
          items:
            $ref: '#/components/schemas/DraftCreateInfoCluster'
        draft_id:
          type: integer
          format: int64
        errors:
          type: array
          items:
            type: object
        status:
          type: string
          enum: ["CALCULATION_STATUS_FAILED", "CALCULATION_STATUS_SUCCESS", "CALCULATION_STATUS_IN_PROGRESS", "CALCULATION_STATUS_EXPIRED"]

    DraftCreateInfoCluster:
      type: object
      properties:
        cluster_id:
          type: integer
          format: int64
        cluster_name:
          type: string
        warehouses:
          type: array
          items:
            $ref: '#/components/schemas/DraftCreateInfoWarehouse'

    DraftCreateInfoWarehouse:
      type: object
      properties:
        supply_warehouse:
          type: object
          properties:
            name:
              type: string
            warehouse_id:
              type: integer
              format: int64
    
    DraftTimeslotInfoRequest:
      type: object
      required:
        - date_from
        - date_to
        - draft_id
        - warehouse_ids
      properties:
        date_from:
          type: string
          format: date-time
        date_to:
          type: string
          format: date-time
        draft_id:
          type: integer
          format: int64
        warehouse_ids:
          type: array
          maxItems: 10
          items:
            type: string
            format: int64

    DraftTimeslotInfoResponse:
      type: object
      properties:
        drop_off_warehouse_timeslots:
          type: array
          items:
            $ref: '#/components/schemas/WarehouseTimeslot'
        requested_date_from:
          type: string
          format: date-time
        requested_date_to:
          type: string
          format: date-time

    WarehouseTimeslot:
      type: object
      properties:
        drop_off_warehouse_id:
          type: integer
          format: int64
        days:
          type: array
          items:
            type: object
            properties:
              date_in_timezone:
                type: string
                format: date-time
              timeslots:
                type: array
                items:
                  $ref: '#/components/schemas/Timeslot'

    Timeslot:
      type: object
      properties:
        from_in_timezone:
          type: string
          format: date-time
        to_in_timezone:
          type: string
          format: date-time
          
    DraftSupplyCreateRequest:
      type: object
      required:
        - draft_id
        - warehouse_id
      properties:
        draft_id:
          type: integer
          format: int64
        timeslot:
          $ref: '#/components/schemas/Timeslot'
        warehouse_id:
          type: integer
          format: int64
          
    DraftSupplyCreateStatusResponse:
      type: object
      properties:
        error_messages:
          type: array
          items:
            type: string
        result:
          type: object
          properties:
            order_ids:
              type: array
              items:
                type: string
                format: int64
        status:
          type: string
          enum: ["DraftSupplyCreateStatusUnknown", "DraftSupplyCreateStatusSuccess", "DraftSupplyCreateStatusFailed", "DraftSupplyCreateStatusInProgress"]

    # Cargoes Schemas
    CargoesCreateRequest:
      type: object
      required:
        - cargoes
        - supply_id
      properties:
        cargoes:
          type: array
          items:
            type: object
            properties:
              key:
                type: string
              value:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      type: object
                      properties:
                        barcode:
                          type: string
                        expires_at:
                          type: string
                          format: date-time
                        quant:
                          type: integer
                        quantity:
                          type: integer
                  type:
                    type: string
                    enum: ["BOX", "PALLET"]
        delete_current_version:
          type: boolean
        supply_id:
          type: integer
          format: int64
          
    CargoesCreateResponse:
      type: object
      properties:
        operation_id:
          type: string
        errors:
          type: object # Complex error object
          
    CargoesCreateInfoResponse:
      type: object
      properties:
        result:
          type: object
          properties:
            cargoes:
              type: array
              items:
                type: object
                properties:
                  key:
                    type: string
                  value:
                    type: object
                    properties:
                      cargo_id:
                        type: integer
                        format: int64
        status:
          type: string
          enum: ["SUCCESS", "IN_PROGRESS", "FAILED"]
        errors:
          type: object # Complex error object
          
    CargoesDeleteRequest:
      type: object
      properties:
        cargo_ids:
          type: array
          items:
            type: string
            format: int64
        supply_id:
          type: integer
          format: int64

    CargoesDeleteResponse:
      type: object
      properties:
        operation_id:
          type: string
        errors:
          type: object # Complex error object
          
    CargoesDeleteStatusResponse:
      type: object
      properties:
        status:
          type: string
          enum: ["SUCCESS", "IN_PROGRESS", "ERROR"]
        errors:
          type: object # Complex error object
          
    CargoesRulesResponse:
      type: object
      properties:
        supply_check_lists:
          type: array
          items:
            type: object # Very complex object, simplified here
            properties:
              supply_id:
                type: integer
                format: int64
              satisfied:
                type: boolean
                
    CargoesLabelCreateRequest:
      type: object
      required:
        - supply_id
      properties:
        cargoes:
          type: array
          items:
            type: object
            properties:
              cargo_id:
                type: integer
                format: int64
        supply_id:
          type: integer
          format: int64

    CargoesLabelGetResponse:
      type: object
      properties:
        result:
          type: object
          properties:
            file_guid:
              type: string
        status:
          type: string
          enum: ["SUCCESS", "IN_PROGRESS", "FAILED"]
        errors:
          type: object
          
    # Supply-Order Schemas
    SupplyOrderCancelStatusResponse:
      type: object
      properties:
        status:
          type: string
          enum: ["SUCCESS", "IN_PROGRESS", "ERROR"]
        error_reasons:
          type: array
          items:
            type: string
        result:
          type: object
          properties:
            is_order_cancelled:
              type: boolean
            supplies:
              type: array
              items:
                type: object
                properties:
                  supply_id:
                    type: integer
                    format: int64
                  is_supply_cancelled:
                    type: boolean
                    
    SupplyOrderContentUpdateRequest:
      type: object
      properties:
        items:
          type: array
          items:
            type: object
            properties:
              quant:
                type: integer
              quantity:
                type: integer
              sku:
                type: integer
                format: int64
        order_id:
          type: integer
          format: int64
        supply_id:
          type: integer
          format: int64
          
    SupplyOrderContentUpdateResponse:
      type: object
      properties:
        operation_id:
          type: string
        errors:
          type: array
          items:
            type: string
            
    SupplyOrderContentUpdateStatusResponse:
      type: object
      properties:
        status:
          type: string
          enum: ["SUCCESS", "IN_PROGRESS", "ERROR"]
        errors:
          type: array
          items:
            type: string