package main

import (
	"fmt"
	"github.com/gogf/gf/v2/util/gconv"
	"lens/internal/api/ozon"
	"strings"
	"testing"
	"time"

	"github.com/xuri/excelize/v2"
)

func TestGetClusterAndWarehouseList(t *testing.T) {
	// 创建 Ozon API 客户端
	// 注意：这里需要替换为实际的 API Key 和 Client ID
	client := ozonapi.NewClient("f8260a04-4b24-40aa-af7c-fae8d51b3dc3", "2206838")

	// 创建请求参数
	req := &ozonapi.ClusterListRequest{
		ClusterType: "CLUSTER_TYPE_OZON", // 俄罗斯集群 或 "CLUSTER_TYPE_CIS" 独联体集群
	}

	// 调用API
	resp, err := client.FBO.GetClusterAndWarehouseList(req)
	if err != nil {
		t.Fatalf("获取集群和仓库列表失败: %v", err)
	}

	// 验证响应
	if resp == nil {
		t.Fatal("响应为空")
	}

	// 打印结果
	fmt.Println("\n=== 集群和仓库列表 ===")
	fmt.Printf("集群数量: %d\n", len(resp.Clusters))

	// 打印集群详细信息
	for i, cluster := range resp.Clusters {
		fmt.Printf("\n集群 #%d:\n", i+1)
		fmt.Printf("- 集群ID: %d\n", cluster.Id)
		fmt.Printf("- 集群名称: %s\n", cluster.Name)
		fmt.Printf("- 仓库数量: %d\n", len(cluster.LogisticClusters))
		if cluster.Id == 154 {
			// 打印仓库信息
			for j, logisticCluster := range cluster.LogisticClusters {
				fmt.Printf("  物流集群 #%d:\n", j+1)
				fmt.Printf("  - 仓库数量: %d\n", len(logisticCluster.Warehouses))
				for k, warehouse := range logisticCluster.Warehouses {
					fmt.Printf("    仓库 #%d:\n", k+1)
					fmt.Printf("    - 仓库名称: %s\n", warehouse.Name)
					fmt.Printf("    - 仓库ID: %d\n", warehouse.WarehouseId)
					fmt.Printf("    - 类型: %s\n", warehouse.Type)
				}
			}
		}
	}
}

func TestSearchFboWarehouses(t *testing.T) {
	// 创建 Ozon API 客户端
	client := ozonapi.NewClient("f8260a04-4b24-40aa-af7c-fae8d51b3dc3", "2206838")

	// 创建请求参数
	req := &ozonapi.WarehouseFboListRequest{
		FilterBySupplyType: []string{"CREATE_TYPE_CROSSDOCK"}, // 或 "CREATE_TYPE_DIRECT"
		Search:             "Москва",                          // 搜索莫斯科的仓库
	}

	// 调用API
	resp, err := client.FBO.SearchFboWarehouses(req)
	if err != nil {
		t.Fatalf("搜索FBO仓库失败: %v", err)
	}

	// 验证响应
	if resp == nil {
		t.Fatal("响应为空")
	}

	// 打印结果
	fmt.Println("\n=== FBO仓库搜索结果 ===")
	fmt.Printf("找到仓库数量: %d\n", len(resp.Search))

	// 打印仓库详细信息
	for i, warehouse := range resp.Search {
		fmt.Printf("\n仓库 #%d:\n", i+1)
		fmt.Printf("- 仓库名称: %s\n", warehouse.Name)
		fmt.Printf("- 仓库ID: %d\n", warehouse.WarehouseID)
		fmt.Printf("- 仓库类型: %s\n", warehouse.WarehouseType)
		fmt.Printf("- 地址: %s\n", warehouse.Address)

		// 打印坐标信息
		fmt.Printf("- 纬度: %f\n", warehouse.Coordinates.Latitude)
		fmt.Printf("- 经度: %f\n", warehouse.Coordinates.Longitude)
	}
}

// TestCreateSupplyDraftFromCluster 测试根据集群列表创建供应草稿
func TestCreateSupplyDraftFromCluster(t *testing.T) {
	// 创建 Ozon API 客户端
	client := ozonapi.NewClient("f8260a04-4b24-40aa-af7c-fae8d51b3dc3", "2206838")

	// 1. 读取Excel表格数据
	fmt.Println("正在读取Excel表格数据...")
	f, err := excelize.OpenFile("20250726库存情况和分配计划-对外.xlsx")
	if err != nil {
		t.Fatalf("打开Excel文件失败: %v", err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			t.Fatalf("关闭Excel文件失败: %v", err)
		}
	}()

	// 获取第一个工作表的名称
	sheetName := f.GetSheetName(0)
	if sheetName == "" {
		t.Fatal("无法获取Excel工作表名称")
	}

	// 解析每个集群下的SKU数量和箱数
	type ClusterProductInfo struct {
		ClusterName string
		SKU         int64
		Article     string
		Quantity    int
		Boxes       float64
	}

	clusterProductsMap := make(map[string][]ClusterProductInfo)
	clusters := []string{}
	products := []string{}

	fmt.Printf("读取工作表: %s\n", sheetName)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		t.Fatalf("读取行数据失败: %v", err)
	}

	// 读取集群名称和产品SKU
	for i, row := range rows {
		if i == 0 { // 第一行是集群名称
			for j := 10; j < 52 && j < len(row); j += 2 {
				if row[j] != "" {
					clusters = append(clusters, strings.Split(row[j], "\n")[0])
				}
			}
		}
		if i >= 3 && len(row) > 0 { // 从第4行开始是产品数据
			if row[0] != "" {
				products = append(products, row[0]) // SKU名称在第1列
			}
		}
	}

	// 解析每个集群下的产品数据
	for i, product := range products {
		rowIndex := i + 3 // 数据从第4行开始
		if rowIndex >= len(rows) {
			break
		}

		row := rows[rowIndex]
		for j, cluster := range clusters {
			colIndex := 10 + j*2 // 数据从第11列开始，每两个列为一个集群的数据
			if colIndex < len(row) {
				quantity := 0
				boxes := 0.0
				// 数量在当前列
				if colIndex < len(row) && row[colIndex] != "" {
					quantity = gconv.Int(row[colIndex])
				}
				// 箱数在下一列
				if colIndex+1 < len(row) && row[colIndex+1] != "" {
					boxes = gconv.Float64(strings.TrimSpace(row[colIndex+1]))
				}
				cpi := ClusterProductInfo{
					ClusterName: cluster,
					SKU:         0,
					Article:     product,
					Quantity:    quantity,
					Boxes:       boxes,
				}
				clusterProductsMap[cluster] = append(clusterProductsMap[cluster], cpi)
			}
		}
	}

	fmt.Printf("从Excel文件中读取到 %d 个集群商品信息\n", len(clusterProductsMap))
	//Article 映射sku
	articleToSKU := make(map[string]int64)
	offerIds := []string{}
	for _, products_info := range clusterProductsMap {
		for _, info := range products_info {
			offerIds = append(offerIds, info.Article)
		}
	}

	// 通过 client.Products.GetProductList() 获得 ozon sku
	skuReq := &ozonapi.ProductInfoRequest{
		OfferID: offerIds,
	}
	oProducts, _ := client.Products.GetProductInfo(skuReq)

	for _, item := range oProducts.Items {
		if _, ok := articleToSKU[item.OfferId]; !ok {
			articleToSKU[item.OfferId] = gconv.Int64(item.Sources[0].Sku)
		}
	}

	// 打印每个集群的信息
	for clusterName, products_info := range clusterProductsMap {
		fmt.Printf("\n集群 %s 的商品信息:\n", clusterName)
		totalQuantity := 0
		totalBoxes := 0.0
		for _, product := range products_info {
			fmt.Printf("  - SKU: %s, 数量: %d, 箱数: %.1f \n", product.Article, product.Quantity, product.Boxes)
			totalQuantity += product.Quantity
			totalBoxes += product.Boxes
		}
		fmt.Printf("  集群总计: 数量 %d, 箱数 %.1f \n", totalQuantity, totalBoxes)
	}

	// 2. 获取集群和仓库列表
	clusterReq := &ozonapi.ClusterListRequest{
		ClusterType: "CLUSTER_TYPE_OZON", // 俄罗斯集群
	}

	clusterResp, err := client.FBO.GetClusterAndWarehouseList(clusterReq)
	if err != nil {
		t.Fatalf("获取集群和仓库列表失败: %v", err)
	}

	// 验证响应
	if clusterResp == nil {
		t.Fatal("集群和仓库列表响应为空")
	}

	fmt.Printf("\n获取到 %d 个集群\n", len(clusterResp.Clusters))

	// 打印集群信息
	for i, cluster := range clusterResp.Clusters {
		fmt.Printf("\n集群 #%d:\n", i+1)
		fmt.Printf("- 集群ID: %d\n", cluster.Id)
		fmt.Printf("- 集群名称: %s\n", cluster.Name)
		fmt.Printf("- 集群类型: %s\n", cluster.Type)

		// 打印物流集群中的仓库信息
		for j, logisticCluster := range cluster.LogisticClusters {
			fmt.Printf("  物流集群 #%d:\n", j+1)
			for k, warehouse := range logisticCluster.Warehouses {
				fmt.Printf("    仓库 #%d:\n", k+1)
				fmt.Printf("    - 仓库名称: %s\n", warehouse.Name)
				fmt.Printf("    - 仓库ID: %d\n", warehouse.WarehouseId)
				fmt.Printf("    - 类型: %s\n", warehouse.Type)
			}
		}
	}

	// 3. 从集群列表中选择第一个集群创建供应草稿
	if len(clusterResp.Clusters) == 0 {
		t.Fatal("没有获取到任何集群信息")
	}
	operationID := "01985188-c559-75fe-8071-0164379f44bb"
	if operationID == "" {
		for _, cluster := range clusterResp.Clusters {
			if cluster.Name != "Юг" {
				continue
			}
			cpms := clusterProductsMap[cluster.Name]
			// 准备草稿中的商品项
			var draftItems []struct {
				Quantity int   `json:"quantity"`
				SKU      int64 `json:"sku"`
			}
			for _, cpm := range cpms {
				if cpm.Quantity == 0 {
					continue
				}
				draftItems = append(draftItems, struct {
					Quantity int   `json:"quantity"`
					SKU      int64 `json:"sku"`
				}{Quantity: cpm.Quantity, SKU: articleToSKU[cpm.Article]})
			}

			// 创建供应草稿请求
			draftReq := &ozonapi.DraftCreateRequest{
				ClusterIDs:              []int64{6},
				DropOffPointWarehouseID: 1020002201523000,
				Items:                   draftItems,
				Type:                    "CREATE_TYPE_CROSSDOCK", // CREATE_TYPE_CROSSDOCK 或者 "CREATE_TYPE_DIRECT"
			}

			// 4. 创建供应草稿
			fmt.Printf("\n正在创建供应草稿...\n")
			fmt.Printf("- 商品项数量: %d\n", len(draftReq.Items))

			for i, item := range draftReq.Items {
				fmt.Printf("  商品 #%d: SKU=%d, 数量=%d\n", i+1, item.SKU, item.Quantity)
			}
			operationID, err := client.FBO.CreateSupplyDraft(draftReq)
			if err != nil {
				t.Fatalf("创建供应草稿失败: %v", err)
			}
			fmt.Printf("成功创建供应草稿，操作ID: %s\n", operationID)
		}
	}
	// 5. 获取草稿信息
	fmt.Printf("\n正在获取草稿信息...\n")
	draftInfo, err := client.FBO.GetSupplyDraftInfo(operationID)
	if err != nil {
		t.Fatalf("获取供应草稿信息失败: %v", err)
	}

	fmt.Printf("草稿ID: %d, 状态: %s\n", draftInfo.DraftID, draftInfo.Status)

	// 打印草稿中的集群信息
	for i, cluster := range draftInfo.Clusters {
		fmt.Printf("\n草稿集群 #%d:\n", i+1)
		fmt.Printf("- 集群ID: %d\n", cluster.ClusterID)
		fmt.Printf("- 集群名称: %s\n", cluster.ClusterName)

		// 打印仓库信息
		for j, warehouse := range cluster.Warehouses {
			fmt.Printf("  仓库 #%d:\n", j+1)
			fmt.Printf("  - 仓库ID: %d\n", warehouse.SupplyWarehouse.WarehouseID)
			fmt.Printf("  - 仓库名称: %s\n", warehouse.SupplyWarehouse.Name)
		}
	}

	// 检查是否有错误信息
	if len(draftInfo.Errors) > 0 {
		fmt.Printf("\n草稿创建过程中出现 %d 个错误:\n", len(draftInfo.Errors))
		for i, errMsg := range draftInfo.Errors {
			fmt.Printf("错误 #%d: %v\n", i+1, errMsg)
		}
	} else {
		fmt.Printf("\n草稿创建成功，无错误信息。\n")
	}

	// 6. 获取可用时间段信息
	if draftInfo.DraftID > 0 && len(draftInfo.Clusters) > 0 {
		fmt.Printf("\n正在获取可用时间段信息...\n")

		// 收集所有仓库ID
		var warehouseIDs []int64
		for _, cluster := range draftInfo.Clusters {
			for _, warehouse := range cluster.Warehouses {
				warehouseIDs = append(warehouseIDs, warehouse.SupplyWarehouse.WarehouseID)
			}
		}

		if len(warehouseIDs) > 0 {
			// 设置查询时间范围（从明天开始的7天内）
			now := time.Now()
			dateFrom := now.AddDate(0, 0, 15) // 明天
			dateTo := now.AddDate(0, 0, 43)   // 8天后

			timeslotReq := &ozonapi.DraftTimeslotInfoRequest{
				DateFrom:     dateFrom,
				DateTo:       dateTo,
				DraftID:      draftInfo.DraftID,
				WarehouseIDs: warehouseIDs,
			}

			timeslotInfo, err := client.FBO.GetDraftTimeslotInfo(timeslotReq)
			if err != nil {
				fmt.Printf("获取时间段信息失败: %v\n", err)
			} else {
				fmt.Printf("成功获取时间段信息\n")
				fmt.Printf("查询时间范围: %s 到 %s\n",
					timeslotInfo.RequestedDateFrom.Format("2006-01-02"),
					timeslotInfo.RequestedDateTo.Format("2006-01-02"))

				// 打印时间段信息
				minDaysFromNow := 4 // 最少4天后的时间段
				printTimeslotInfo(timeslotInfo, minDaysFromNow)

				// 选择符合条件的仓库和时间段
				selectedWarehouseID, _ := selectValidTimeslot(timeslotInfo, minDaysFromNow)

				// 7. 从草稿创建供应单
				if selectedWarehouseID > 0 {
					fmt.Printf("\n正在从草稿创建供应单...\n")
					fmt.Printf("选择的仓库ID: %d\n", selectedWarehouseID)
					//fmt.Printf("选择的时间段: %s - %s\n",
					//	selectedTimeslot.FromInTimezone.Format("2006-01-02 15:04"),
					//	selectedTimeslot.ToInTimezone.Format("2006-01-02 15:04"))

					supplyCreateReq := &ozonapi.DraftSupplyCreateRequest{
						DraftID: draftInfo.DraftID,
						//Timeslot:    selectedTimeslot,
						WarehouseID: 17717042026000,
					}

					supplyOperationID, err := client.FBO.CreateSupplyFromDraft(supplyCreateReq)
					if err != nil {
						fmt.Printf("从草稿创建供应单失败: %v\n", err)
					} else {
						fmt.Printf("成功创建供应单，操作ID: %s\n", supplyOperationID)

						// 8. 获取供应单创建状态
						fmt.Printf("\n正在获取供应单创建状态...\n")
						supplyStatus, err := client.FBO.GetDraftSupplyCreateStatus(supplyOperationID)
						if err != nil {
							fmt.Printf("获取供应单创建状态失败: %v\n", err)
						} else {
							fmt.Printf("供应单创建状态: %s\n", supplyStatus.Status)
							if len(supplyStatus.ErrorMessages) > 0 {
								fmt.Printf("错误信息:\n")
								for i, errMsg := range supplyStatus.ErrorMessages {
									fmt.Printf("  错误 #%d: %s\n", i+1, errMsg)
								}
							}
							if len(supplyStatus.Result.OrderIDs) > 0 {
								fmt.Printf("创建的订单IDs: %v\n", supplyStatus.Result.OrderIDs)
							}
						}
					}
				} else {
					fmt.Printf("\n未找到符合条件的仓库和时间段（需要至少%d天后的时间段）\n", minDaysFromNow)
				}
			}
		} else {
			fmt.Printf("未找到可用的仓库ID，跳过时间段查询\n")
		}
	} else {
		fmt.Printf("草稿ID无效或无集群信息，跳过时间段查询\n")
	}

}

// TestGetDraftTimeslotInfo 测试获取草稿时间段信息
func TestGetDraftTimeslotInfo(t *testing.T) {
	// 创建 Ozon API 客户端
	client := ozonapi.NewClient("f8260a04-4b24-40aa-af7c-fae8d51b3dc3", "2206838")

	// 使用已知的草稿ID进行测试（需要先创建一个草稿）
	draftID := int64(123456)                  // 替换为实际的草稿ID
	warehouseIDs := []int64{1020002201523000} // 替换为实际的仓库ID

	// 设置查询时间范围（从明天开始的7天内）
	now := time.Now()
	dateFrom := now.AddDate(0, 0, 1) // 明天
	dateTo := now.AddDate(0, 0, 8)   // 8天后

	fmt.Printf("测试获取草稿时间段信息\n")
	fmt.Printf("草稿ID: %d\n", draftID)
	fmt.Printf("仓库IDs: %v\n", warehouseIDs)
	fmt.Printf("查询时间范围: %s 到 %s\n",
		dateFrom.Format("2006-01-02"),
		dateTo.Format("2006-01-02"))

	// 创建时间段信息请求
	timeslotReq := &ozonapi.DraftTimeslotInfoRequest{
		DateFrom:     dateFrom,
		DateTo:       dateTo,
		DraftID:      draftID,
		WarehouseIDs: warehouseIDs,
	}

	// 调用API获取时间段信息
	timeslotInfo, err := client.FBO.GetDraftTimeslotInfo(timeslotReq)
	if err != nil {
		t.Fatalf("获取时间段信息失败: %v", err)
	}

	// 验证响应
	if timeslotInfo == nil {
		t.Fatal("时间段信息响应为空")
	}

	fmt.Printf("\n=== 时间段信息 ===\n")
	fmt.Printf("请求的时间范围: %s 到 %s\n",
		timeslotInfo.RequestedDateFrom.Format("2006-01-02"),
		timeslotInfo.RequestedDateTo.Format("2006-01-02"))
	fmt.Printf("找到 %d 个仓库的时间段信息\n", len(timeslotInfo.DropOffWarehouseTimeslots))

	// 打印每个仓库的可用时间段详情
	for i, warehouseTimeslot := range timeslotInfo.DropOffWarehouseTimeslots {
		fmt.Printf("\n仓库 #%d (ID: %d):\n", i+1, warehouseTimeslot.DropOffWarehouseID)

		if len(warehouseTimeslot.Days) == 0 {
			fmt.Printf("  该仓库暂无可用时间段\n")
			continue
		}

		totalTimeslots := 0
		for j, day := range warehouseTimeslot.Days {
			fmt.Printf("  日期 #%d: %s\n", j+1, day.DateInTimezone.Format("2006-01-02 Monday"))

			if len(day.Timeslots) == 0 {
				fmt.Printf("    该日期暂无可用时间段\n")
				continue
			}

			for k, timeslot := range day.Timeslots {
				fmt.Printf("    时间段 #%d: %s - %s\n", k+1,
					timeslot.FromInTimezone.Format("15:04"),
					timeslot.ToInTimezone.Format("15:04"))
				totalTimeslots++
			}
		}
		fmt.Printf("  仓库总计可用时间段: %d 个\n", totalTimeslots)
	}

	// 验证数据完整性
	if len(timeslotInfo.DropOffWarehouseTimeslots) == 0 {
		fmt.Printf("\n注意: 未找到任何可用时间段，可能原因:\n")
		fmt.Printf("- 草稿ID不存在或已过期\n")
		fmt.Printf("- 仓库ID不正确\n")
		fmt.Printf("- 查询的时间范围内暂无可用时间段\n")
	}
}

// TestCreateSupplyFromDraftWithTimeslot 测试从草稿创建供应单（带时间段选择）
func TestCreateSupplyFromDraftWithTimeslot(t *testing.T) {
	// 创建 Ozon API 客户端
	client := ozonapi.NewClient("f8260a04-4b24-40aa-af7c-fae8d51b3dc3", "2206838")

	// 使用已知的草稿ID进行测试（需要先创建一个草稿）
	draftID := int64(123456) // 替换为实际的草稿ID

	fmt.Printf("测试从草稿创建供应单（带时间段选择）\n")
	fmt.Printf("草稿ID: %d\n", draftID)

	// 1. 首先获取草稿信息
	fmt.Printf("\n正在获取草稿信息...\n")
	draftInfo, err := client.FBO.GetSupplyDraftInfo("operation-id-here") // 替换为实际的操作ID
	if err != nil {
		t.Fatalf("获取供应草稿信息失败: %v", err)
	}

	if draftInfo.DraftID == 0 || len(draftInfo.Clusters) == 0 {
		t.Fatal("草稿信息无效或无集群信息")
	}

	fmt.Printf("草稿ID: %d, 状态: %s\n", draftInfo.DraftID, draftInfo.Status)

	// 2. 收集所有仓库ID
	var warehouseIDs []int64
	for _, cluster := range draftInfo.Clusters {
		for _, warehouse := range cluster.Warehouses {
			warehouseIDs = append(warehouseIDs, warehouse.SupplyWarehouse.WarehouseID)
		}
	}

	if len(warehouseIDs) == 0 {
		t.Fatal("未找到可用的仓库ID")
	}

	// 3. 获取时间段信息
	now := time.Now()
	dateFrom := now.AddDate(0, 0, 1) // 明天
	dateTo := now.AddDate(0, 0, 15)  // 15天后

	timeslotReq := &ozonapi.DraftTimeslotInfoRequest{
		DateFrom:     dateFrom,
		DateTo:       dateTo,
		DraftID:      draftInfo.DraftID,
		WarehouseIDs: warehouseIDs,
	}

	fmt.Printf("\n正在获取时间段信息...\n")
	timeslotInfo, err := client.FBO.GetDraftTimeslotInfo(timeslotReq)
	if err != nil {
		t.Fatalf("获取时间段信息失败: %v", err)
	}

	// 4. 选择符合条件的仓库和时间段（至少4天后）
	var selectedWarehouseID int64
	var selectedTimeslot *ozonapi.Timeslot
	minDaysFromNow := 4

	fmt.Printf("\n正在选择合适的仓库和时间段...\n")
	fmt.Printf("要求: 时间段必须在%d天后\n", minDaysFromNow)

	for _, warehouseTimeslot := range timeslotInfo.DropOffWarehouseTimeslots {
		fmt.Printf("\n检查仓库 ID: %d\n", warehouseTimeslot.DropOffWarehouseID)

		for _, day := range warehouseTimeslot.Days {
			daysFromNow := int(day.DateInTimezone.Sub(now).Hours() / 24)

			if daysFromNow < minDaysFromNow {
				fmt.Printf("  日期 %s 距今%d天，不符合要求\n",
					day.DateInTimezone.Format("2006-01-02"), daysFromNow)
				continue
			}

			if len(day.Timeslots) > 0 {
				selectedWarehouseID = warehouseTimeslot.DropOffWarehouseID
				selectedTimeslot = &day.Timeslots[0] // 选择第一个可用时间段
				fmt.Printf("  ✓ 选择日期 %s (距今%d天) 的时间段 %s-%s\n",
					day.DateInTimezone.Format("2006-01-02"), daysFromNow,
					selectedTimeslot.FromInTimezone.Format("15:04"),
					selectedTimeslot.ToInTimezone.Format("15:04"))
				break
			}
		}

		if selectedWarehouseID > 0 {
			break
		}
	}

	if selectedWarehouseID == 0 || selectedTimeslot == nil {
		t.Fatalf("未找到符合条件的仓库和时间段（需要至少%d天后的时间段）", minDaysFromNow)
	}

	// 5. 创建供应单
	fmt.Printf("\n正在从草稿创建供应单...\n")
	fmt.Printf("选择的仓库ID: %d\n", selectedWarehouseID)
	fmt.Printf("选择的时间段: %s - %s\n",
		selectedTimeslot.FromInTimezone.Format("2006-01-02 15:04"),
		selectedTimeslot.ToInTimezone.Format("2006-01-02 15:04"))

	supplyCreateReq := &ozonapi.DraftSupplyCreateRequest{
		DraftID:     draftInfo.DraftID,
		Timeslot:    selectedTimeslot,
		WarehouseID: selectedWarehouseID,
	}

	supplyOperationID, err := client.FBO.CreateSupplyFromDraft(supplyCreateReq)
	if err != nil {
		t.Fatalf("从草稿创建供应单失败: %v", err)
	}

	fmt.Printf("成功创建供应单，操作ID: %s\n", supplyOperationID)

	// 6. 获取供应单创建状态
	fmt.Printf("\n正在获取供应单创建状态...\n")
	supplyStatus, err := client.FBO.GetDraftSupplyCreateStatus(supplyOperationID)
	if err != nil {
		t.Fatalf("获取供应单创建状态失败: %v", err)
	}

	fmt.Printf("供应单创建状态: %s\n", supplyStatus.Status)

	if len(supplyStatus.ErrorMessages) > 0 {
		fmt.Printf("错误信息:\n")
		for i, errMsg := range supplyStatus.ErrorMessages {
			fmt.Printf("  错误 #%d: %s\n", i+1, errMsg)
		}
	}

	if len(supplyStatus.Result.OrderIDs) > 0 {
		fmt.Printf("创建的订单IDs: %v\n", supplyStatus.Result.OrderIDs)
		fmt.Printf("供应单创建成功！\n")
	} else {
		fmt.Printf("注意: 未返回订单ID，请检查创建状态\n")
	}

	// 验证结果
	if supplyStatus.Status == "DraftSupplyCreateStatusSuccess" {
		fmt.Printf("\n✓ 测试通过: 供应单创建成功\n")
	} else if supplyStatus.Status == "DraftSupplyCreateStatusInProgress" {
		fmt.Printf("\n⏳ 供应单创建中，请稍后查看状态\n")
	} else {
		t.Errorf("供应单创建失败，状态: %s", supplyStatus.Status)
	}
}

// selectValidTimeslot 选择符合条件的时间段（至少指定天数后）
func selectValidTimeslot(timeslotInfo *ozonapi.DraftTimeslotInfoResponse, minDaysFromNow int) (int64, *ozonapi.Timeslot) {
	now := time.Now()
	var warehouseid int64
	for _, warehouseTimeslot := range timeslotInfo.DropOffWarehouseTimeslots {
		warehouseid = warehouseTimeslot.DropOffWarehouseID
		for _, day := range warehouseTimeslot.Days {
			daysFromNow := int(day.DateInTimezone.Sub(now).Hours() / 24)

			// 检查是否满足最少天数要求
			if daysFromNow < minDaysFromNow {
				continue
			}

			// 选择第一个可用时间段
			if len(day.Timeslots) > 0 {
				return warehouseTimeslot.DropOffWarehouseID, &day.Timeslots[0]
			}
		}
	}

	return warehouseid, nil
}

// printTimeslotInfo 打印时间段信息
func printTimeslotInfo(timeslotInfo *ozonapi.DraftTimeslotInfoResponse, minDaysFromNow int) {
	now := time.Now()

	fmt.Printf("查询时间范围: %s 到 %s\n",
		timeslotInfo.RequestedDateFrom.Format("2006-01-02"),
		timeslotInfo.RequestedDateTo.Format("2006-01-02"))
	fmt.Printf("找到 %d 个仓库的时间段信息\n", len(timeslotInfo.DropOffWarehouseTimeslots))

	for i, warehouseTimeslot := range timeslotInfo.DropOffWarehouseTimeslots {
		fmt.Printf("\n仓库 #%d (ID: %d):\n", i+1, warehouseTimeslot.DropOffWarehouseID)

		if len(warehouseTimeslot.Days) == 0 {
			fmt.Printf("  该仓库暂无可用时间段\n")
			continue
		}

		validTimeslots := 0
		for _, day := range warehouseTimeslot.Days {
			daysFromNow := int(day.DateInTimezone.Sub(now).Hours() / 24)

			if len(day.Timeslots) > 0 {
				status := "❌"
				if daysFromNow >= minDaysFromNow {
					status = "✅"
					validTimeslots += len(day.Timeslots)
				}

				fmt.Printf("  %s 日期: %s (距今%d天) - %d个时间段\n",
					status, day.DateInTimezone.Format("2006-01-02"), daysFromNow, len(day.Timeslots))

				// 只显示前3个时间段
				for j, timeslot := range day.Timeslots {
					if j >= 3 {
						fmt.Printf("    ... 还有%d个时间段\n", len(day.Timeslots)-3)
						break
					}
					fmt.Printf("    时间段: %s - %s\n",
						timeslot.FromInTimezone.Format("15:04"),
						timeslot.ToInTimezone.Format("15:04"))
				}
			}
		}

		fmt.Printf("  符合条件的时间段总数: %d 个\n", validTimeslots)
	}
}
