package main

import (
	"fmt"
	"time"
	ozonapi "lens/internal/api/ozon"
)

// FBOSupplyWorkflowExample 展示完整的FBO供应流程
func FBOSupplyWorkflowExample() {
	// 1. 创建 Ozon API 客户端
	client := ozonapi.NewClient("your-api-key", "your-client-id")

	// 2. 获取集群和仓库信息
	fmt.Println("=== 步骤1: 获取集群和仓库信息 ===")
	clusterReq := &ozonapi.ClusterListRequest{
		ClusterType: "CLUSTER_TYPE_OZON", // 俄罗斯集群
	}

	clusters, err := client.FBO.GetClusterAndWarehouseList(clusterReq)
	if err != nil {
		fmt.Printf("获取集群信息失败: %v\n", err)
		return
	}

	fmt.Printf("找到 %d 个集群\n", len(clusters.Clusters))

	// 3. 创建供应草稿
	fmt.Println("\n=== 步骤2: 创建供应草稿 ===")
	draftReq := &ozonapi.DraftCreateRequest{
		ClusterIDs:              []int64{6}, // 选择集群ID
		DropOffPointWarehouseID: 1020002201523000, // 投递点仓库ID
		Items: []struct {
			Quantity int   `json:"quantity"`
			SKU      int64 `json:"sku"`
		}{
			{Quantity: 10, SKU: 123456789}, // 商品SKU和数量
			{Quantity: 5, SKU: 987654321},
		},
		Type: "CREATE_TYPE_CROSSDOCK", // 跨区直送
	}

	operationID, err := client.FBO.CreateSupplyDraft(draftReq)
	if err != nil {
		fmt.Printf("创建供应草稿失败: %v\n", err)
		return
	}

	fmt.Printf("成功创建供应草稿，操作ID: %s\n", operationID)

	// 4. 获取草稿信息
	fmt.Println("\n=== 步骤3: 获取草稿信息 ===")
	draftInfo, err := client.FBO.GetSupplyDraftInfo(operationID)
	if err != nil {
		fmt.Printf("获取草稿信息失败: %v\n", err)
		return
	}

	fmt.Printf("草稿ID: %d, 状态: %s\n", draftInfo.DraftID, draftInfo.Status)

	if len(draftInfo.Errors) > 0 {
		fmt.Printf("草稿创建过程中出现错误:\n")
		for i, errMsg := range draftInfo.Errors {
			fmt.Printf("  错误 #%d: %v\n", i+1, errMsg)
		}
		return
	}

	// 5. 获取可用时间段
	fmt.Println("\n=== 步骤4: 获取可用时间段 ===")
	
	// 收集仓库ID
	var warehouseIDs []int64
	for _, cluster := range draftInfo.Clusters {
		for _, warehouse := range cluster.Warehouses {
			warehouseIDs = append(warehouseIDs, warehouse.SupplyWarehouse.WarehouseID)
		}
	}

	if len(warehouseIDs) == 0 {
		fmt.Println("未找到可用的仓库ID")
		return
	}

	// 设置查询时间范围
	now := time.Now()
	dateFrom := now.AddDate(0, 0, 1) // 明天
	dateTo := now.AddDate(0, 0, 15)  // 15天后

	timeslotReq := &ozonapi.DraftTimeslotInfoRequest{
		DateFrom:     dateFrom,
		DateTo:       dateTo,
		DraftID:      draftInfo.DraftID,
		WarehouseIDs: warehouseIDs,
	}

	timeslotInfo, err := client.FBO.GetDraftTimeslotInfo(timeslotReq)
	if err != nil {
		fmt.Printf("获取时间段信息失败: %v\n", err)
		return
	}

	fmt.Printf("查询时间范围: %s 到 %s\n",
		timeslotInfo.RequestedDateFrom.Format("2006-01-02"),
		timeslotInfo.RequestedDateTo.Format("2006-01-02"))

	// 6. 选择合适的时间段（至少4天后）
	fmt.Println("\n=== 步骤5: 选择合适的时间段 ===")
	
	minDaysFromNow := 4
	selectedWarehouseID, selectedTimeslot := selectValidTimeslotForWorkflow(timeslotInfo, minDaysFromNow)

	if selectedWarehouseID == 0 || selectedTimeslot == nil {
		fmt.Printf("未找到符合条件的时间段（需要至少%d天后）\n", minDaysFromNow)
		return
	}

	fmt.Printf("选择的仓库ID: %d\n", selectedWarehouseID)
	fmt.Printf("选择的时间段: %s - %s\n",
		selectedTimeslot.FromInTimezone.Format("2006-01-02 15:04"),
		selectedTimeslot.ToInTimezone.Format("2006-01-02 15:04"))

	// 7. 从草稿创建供应单
	fmt.Println("\n=== 步骤6: 从草稿创建供应单 ===")
	
	supplyCreateReq := &ozonapi.DraftSupplyCreateRequest{
		DraftID:     draftInfo.DraftID,
		Timeslot:    selectedTimeslot,
		WarehouseID: selectedWarehouseID,
	}

	supplyOperationID, err := client.FBO.CreateSupplyFromDraft(supplyCreateReq)
	if err != nil {
		fmt.Printf("从草稿创建供应单失败: %v\n", err)
		return
	}

	fmt.Printf("成功创建供应单，操作ID: %s\n", supplyOperationID)

	// 8. 获取供应单创建状态
	fmt.Println("\n=== 步骤7: 获取供应单创建状态 ===")
	
	supplyStatus, err := client.FBO.GetDraftSupplyCreateStatus(supplyOperationID)
	if err != nil {
		fmt.Printf("获取供应单创建状态失败: %v\n", err)
		return
	}

	fmt.Printf("供应单创建状态: %s\n", supplyStatus.Status)

	if len(supplyStatus.ErrorMessages) > 0 {
		fmt.Printf("错误信息:\n")
		for i, errMsg := range supplyStatus.ErrorMessages {
			fmt.Printf("  错误 #%d: %s\n", i+1, errMsg)
		}
	}

	if len(supplyStatus.Result.OrderIDs) > 0 {
		fmt.Printf("创建的订单IDs: %v\n", supplyStatus.Result.OrderIDs)
		fmt.Println("\n✅ 供应单创建成功！")
	} else {
		fmt.Println("\n⚠️ 供应单创建状态待确认")
	}
}

// selectValidTimeslotForWorkflow 为工作流选择有效的时间段
func selectValidTimeslotForWorkflow(timeslotInfo *ozonapi.DraftTimeslotInfoResponse, minDaysFromNow int) (int64, *ozonapi.Timeslot) {
	now := time.Now()

	for _, warehouseTimeslot := range timeslotInfo.DropOffWarehouseTimeslots {
		for _, day := range warehouseTimeslot.Days {
			daysFromNow := int(day.DateInTimezone.Sub(now).Hours() / 24)

			// 检查是否满足最少天数要求
			if daysFromNow < minDaysFromNow {
				continue
			}

			// 选择第一个可用时间段
			if len(day.Timeslots) > 0 {
				return warehouseTimeslot.DropOffWarehouseID, &day.Timeslots[0]
			}
		}
	}

	return 0, nil
}

// 使用示例
func main() {
	FBOSupplyWorkflowExample()
}
