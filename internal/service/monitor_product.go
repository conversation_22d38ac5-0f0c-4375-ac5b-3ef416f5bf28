package service

import (
	"context"
	"encoding/json"
	"fmt"
	wbhubapi "lens/internal/api/wb"
	wb_web "lens/internal/api/wb_buyer"
	"lens/internal/api/wb_buyer/search"
	"lens/internal/infrastructure/redis"
	rocket_mq "lens/internal/infrastructure/rocketmq"
	"log"
	"sync"
	"time"

	"github.com/gogf/gf/v2/util/gconv"

	"github.com/go-resty/resty/v2"

	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/google/uuid"
)

// MonitorProduct 监控产品结构
type MonitorProduct struct {
	Advert    []ProductAdvert `json:"-"`
	ProductID string          `json:"productId"`
	SKU       string          `json:"sku"`
	Keywords  []string        `json:"keywords"`
}

type ProductAdvert struct {
	ProductID  string    `json:"product_id"`
	SKU        string    `json:"sku"`
	AdvertID   int       `json:"advert_id"`
	AdvertType int       `json:"advert_type"`
	CurrentBid int       `json:"current_bid"`
	Views      int       `json:"views"`       // 展示量
	Clicks     int       `json:"clicks"`      // 点击量
	CTR        float64   `json:"ctr"`         // 点击率
	Orders     int       `json:"orders"`      // 订单量
	CR         float64   `json:"cr"`          // 转化率
	OrderSum   float64   `json:"order_sum"`   // 订单金额
	Spending   float64   `json:"spending"`    // 花费
	ROI        float64   `json:"roi"`         // 投入产出比
	CPM        float64   `json:"cpm"`         // 千次展现成本
	CPC        float64   `json:"cpc"`         // 点击成本
	CPO        float64   `json:"cpo"`         // 订单成本
	UpdateTime time.Time `json:"update_time"` // 更新时间
}

// MonitorProductMap 监控产品映射
type MonitorProductMap map[string]MonitorProduct

// ProductMonitorService 产品监控服务
type ProductMonitorService struct {
	products    MonitorProductMap
	mu          sync.RWMutex
	interval    time.Duration
	stopChan    chan struct{}
	webClient   wb_web.Web
	mqClient    *rocket_mq.RocketMQClient
	restyClient *resty.Client
	redisClient *redis.RedisClient
	wbhubClient *wbhubapi.Client
}

// NewProductMonitorService 创建产品监控服务
func NewProductMonitorService(interval time.Duration, broadcast chan []byte) *ProductMonitorService {
	if interval <= 0 {
		interval = 5 * time.Minute // 默认5分钟
	}

	// 获取 RocketMQ 客户端实例
	mqClient := rocket_mq.GetInstance()

	// 初始化生产者
	if err := mqClient.InitProducer("monitor_group"); err != nil {
		log.Printf("初始化 RocketMQ 生产者失败: %v", err)
		// 不要因为 RocketMQ 初始化失败就阻止服务启动
	}

	// 保存 broadcast 通道到局部变量
	globalBroadcast = broadcast

	// 初始化 resty 客户端
	restyClient := resty.New().
		SetTimeout(30*time.Second).
		SetHeader("Content-Type", "application/json").
		SetRetryCount(5).
		SetRetryWaitTime(3000 * time.Millisecond).
		SetRetryMaxWaitTime(15 * time.Second).
		AddRetryCondition(
			// 当发生网络错误或状态码不是2xx时重试
			func(response *resty.Response, err error) bool {
				return err != nil || response.StatusCode() < 200 || response.StatusCode() >= 300
			},
		)

	// 获取Redis客户端实例
	redisClient := redis.GetInstance()

	// 测试Redis连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := redisClient.Ping(ctx); err != nil {
		log.Printf("Redis连接失败: %v", err)
	}
	apiKey := "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
	hw := wbhubapi.NewClient(apiKey)
	return &ProductMonitorService{
		products:    make(MonitorProductMap),
		interval:    interval,
		stopChan:    make(chan struct{}),
		webClient:   wb_web.Init(),
		wbhubClient: hw,
		mqClient:    mqClient,
		restyClient: restyClient,
		redisClient: redisClient,
	}
}

// 定义全局 broadcast 变量用于推送
var globalBroadcast chan []byte

const (
	REDIS_MONITOR_PRODUCTS_KEY = "monitor:products"        // Redis中存储监控产品配置的key
	REDIS_FAILED_MESSAGES_KEY  = "monitor:failed_messages" // Redis中存储发送失败的消息
	REDIS_FAILED_QUEUE_KEY     = "monitor:failed_queue"    // Redis消息队列key
)

// UpdateProducts 更新监控产品列表
func (s *ProductMonitorService) UpdateProducts(data []byte) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	var newProducts MonitorProductMap
	if err := json.Unmarshal(data, &newProducts); err != nil {
		return err
	}

	s.products = newProducts
	return nil
}

// StartMonitoring 开始监控
func (s *ProductMonitorService) StartMonitoring() {
	ticker := time.NewTicker(s.interval)
	go func() {
		// 立即执行一次
		s.monitorProducts(true)
		for {
			select {
			case <-ticker.C:
				s.monitorProducts(false)
			case <-s.stopChan:
				ticker.Stop()
				return
			}
		}
	}()
}

// StopMonitoring 停止监控
func (s *ProductMonitorService) StopMonitoring() {
	close(s.stopChan)
}

// UpdateInterval 更新监控间隔
func (s *ProductMonitorService) UpdateInterval(interval time.Duration) {
	if interval <= 0 {
		return
	}
	s.mu.Lock()
	defer s.mu.Unlock()
	s.interval = interval

	// 重启监控以应用新的间隔时间
	s.StopMonitoring()
	s.stopChan = make(chan struct{})
	s.StartMonitoring()
}

// GetInterval 获取当前监控间隔
func (s *ProductMonitorService) GetInterval() time.Duration {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.interval
}

// SyncProductAdverts 同步产品广告信息
func (s *ProductMonitorService) SyncProductAdverts() {
	// 获取所有广告 正在运行的
	status := wbhubapi.CampaignStatusRunning
	params := &wbhubapi.CampaignQueryParams{
		Status: &status, // 只获取运行中的广告
	}

	campaigns, err := s.wbhubClient.Promotion.GetCampaignsByParams(params)
	if err != nil {
		fmt.Errorf("获取广告失败: %v", err)
		return
	}

	// 创建临时映射存储产品的广告信息
	productAdverts := make(map[string][]ProductAdvert)

	for _, campaign := range campaigns {
		if campaign.AdvertID == 22590989 ||
			campaign.AdvertID == 26973370 ||
			campaign.AdvertID == 22627671 ||
			campaign.AdvertID == 22692153 ||
			campaign.AdvertID == 22534626 {
			continue
		}
		productID := ""
		currentBid := 0
		if campaign.AuctionMultibids == nil {
			productID = fmt.Sprintf("%d", campaign.AutoParams.NMCPM[0].NM)
			currentBid = campaign.AutoParams.NMCPM[0].CPM
			campaign.Type = 8
		} else {
			productID = fmt.Sprintf("%d", campaign.AuctionMultibids[0].NM)
			currentBid = campaign.AuctionMultibids[0].Bid
			campaign.Type = 9
		}

		// 创建新的广告信息
		advert := ProductAdvert{
			ProductID:  productID,
			AdvertID:   campaign.AdvertID,
			AdvertType: campaign.Type,
			CurrentBid: currentBid,
			Views:      campaign.Statistics.Views,
			Clicks:     campaign.Statistics.Clicks,
			CTR:        campaign.Statistics.CTR,
			Orders:     campaign.Statistics.Orders,
			CR:         campaign.Statistics.CR,
			OrderSum:   campaign.Statistics.OrderSum,
			Spending:   campaign.Statistics.Spending,
			ROI:        campaign.Statistics.ROI,
			CPM:        campaign.Statistics.CPM,
			CPC:        campaign.Statistics.CPC,
			CPO:        campaign.Statistics.CPO,
			UpdateTime: campaign.ChangeTime,
		}

		// 将广告信息添加到对应产品的广告列表中
		productAdverts[productID] = append(productAdverts[productID], advert)

		// 如果产品不存在，获取SKU并创建新产品
		if _, exists := s.products[productID]; !exists {
			cursor := wbhubapi.CardListSettings{
				Filter: struct {
					WithPhoto             int      `json:"withPhoto"`
					TextSearch            string   `json:"textSearch,omitempty"`
					TagIDs                []int    `json:"tagIDs,omitempty"`
					AllowedCategoriesOnly bool     `json:"allowedCategoriesOnly"`
					ObjectIDs             []int    `json:"objectIDs,omitempty"`
					Brands                []string `json:"brands,omitempty"`
					ImtID                 int      `json:"imtID,omitempty"`
				}{
					WithPhoto:  1,
					TextSearch: productID,
				},
				Cursor: struct {
					UpdatedAt string `json:"updatedAt,omitempty"`
					NmID      int    `json:"nmID,omitempty"`
					Limit     int    `json:"limit"`
				}{
					Limit: 1,
				},
			}
			cards, err := s.wbhubClient.Cards.GetCardsList(&wbhubapi.CardListRequest{
				Settings: cursor,
			}, "ru")

			if err != nil {
				log.Printf("获取产品卡片失败: %v", err)
				continue
			}

			// 获取SKU
			var sku string
			if len(cards.Cards) > 0 {
				sku = cards.Cards[0].VendorCode
			}

			// 创建新产品
			s.products[productID] = MonitorProduct{
				ProductID: productID,
				SKU:       sku,
				Keywords:  []string{},
				Advert:    productAdverts[productID],
			}

			// 同步到Redis
			if err := s.syncProductToRedis(productID); err != nil {
				log.Printf("同步产品配置到Redis失败: %v", err)
			}
			continue
		}

		// 判断sku 是否为空
		if s.products[productID].SKU == "" {
			cursor := wbhubapi.CardListSettings{
				Filter: struct {
					WithPhoto             int      `json:"withPhoto"`
					TextSearch            string   `json:"textSearch,omitempty"`
					TagIDs                []int    `json:"tagIDs,omitempty"`
					AllowedCategoriesOnly bool     `json:"allowedCategoriesOnly"`
					ObjectIDs             []int    `json:"objectIDs,omitempty"`
					Brands                []string `json:"brands,omitempty"`
					ImtID                 int      `json:"imtID,omitempty"`
				}{
					WithPhoto:  1,
					TextSearch: productID,
				},
				Cursor: struct {
					UpdatedAt string `json:"updatedAt,omitempty"`
					NmID      int    `json:"nmID,omitempty"`
					Limit     int    `json:"limit"`
				}{
					Limit: 1,
				},
			}
			cards, err := s.wbhubClient.Cards.GetCardsList(&wbhubapi.CardListRequest{
				Settings: cursor,
			}, "ru")

			if err != nil {
				log.Printf("获取产品卡片失败: %v", err)
				continue
			}

			// 获取SKU
			var sku string
			if len(cards.Cards) > 0 {
				sku = cards.Cards[0].VendorCode
			}

			// 更新sku
			p_temp := s.products[productID]
			p_temp.SKU = sku
			s.products[productID] = p_temp
			// 同步到Redis
			if err := s.syncProductToRedis(productID); err != nil {
				log.Printf("同步产品配置到Redis失败: %v", err)
			}
			continue
		}

		// 更新现有产品的广告信息
		product := s.products[productID]
		product.Advert = productAdverts[productID]
		s.products[productID] = product

	}
}

// monitorProducts 监控产品排名
func (s *ProductMonitorService) monitorProducts(isOne bool) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	// 创建批量更新的出价列表
	batchBids := make([]wbhubapi.BidItem, 0)

	for _, product := range s.products {
		keywordCount := len(product.Keywords)
		if keywordCount == 0 {
			continue
		}

		var wg sync.WaitGroup
		semaphore := make(chan struct{}, keywordCount)
		results := make(chan *search.SearchProduct, keywordCount)

		for _, keyword := range product.Keywords {
			wg.Add(1)
			go func(p MonitorProduct, k string) {
				defer wg.Done()
				semaphore <- struct{}{}
				defer func() { <-semaphore }()
				if result := s.processKeyword(p, k, time.Now()); result != nil {
					results <- result
				}
			}(product, keyword)
		}

		// 等待所有关键词处理完成
		go func() {
			wg.Wait()
			close(results)
		}()

		// 跳过不是改价产品
		if product.SKU != "MLN191-100" {
			continue
		}

		// 计算平均排名
		var totalRank int
		var validResults int

		for result := range results {
			if result.Rank > 0 {
				totalRank += result.Rank
				validResults++
			}
		}

		// 如果有有效结果，计算平均排名并调整出价
		if validResults > 0 {
			avgRank := totalRank / validResults
			hour := time.Now().Hour()

			// 为每个广告调整出价
			for _, advert := range product.Advert {
				if advert.AdvertType == 8 { // 自动广告不改价
					continue
				}
				// 计算出价调整
				adjustment := s.calculateBidAdjustment(avgRank, hour, advert.CurrentBid)
				if adjustment != 0 {
					// 构造符合官方格式的请求体
					bid := wbhubapi.BidItem{
						AdvertID: advert.AdvertID,
						NmBids: []struct {
							NM  int `json:"nm"`
							Bid int `json:"bid"`
						}{
							{
								NM:  gconv.Int(product.ProductID),
								Bid: advert.CurrentBid + adjustment,
							},
						},
					}
					batchBids = append(batchBids, bid)
					log.Printf("计划更新广告出价 [广告ID: %d, 产品ID: %s, 平均排名: %d, 当前价格: %d, 调整金额: %d]",
						advert.AdvertID, product.ProductID, avgRank, advert.CurrentBid, adjustment)
				}
			}
		}
	}

	// 批量更新出价
	if len(batchBids) > 0 {
		err := s.wbhubClient.Promotion.UpdateBids(batchBids)
		if err != nil {
			log.Printf("批量更新广告出价失败，共 %d 个产品: %v", len(batchBids), err)
		} else {
			log.Printf("批量更新广告出价成功，共 %d 个产品", len(batchBids))
		}
	}
}

// sendWebSocketAsync 异步发送消息到WebSocket，带重试机制
func (s *ProductMonitorService) sendWebSocketAsync(record interface{}) {
	// 将记录转换为JSON
	recordJSON, err := json.Marshal(record)
	if err != nil {
		log.Printf("序列化记录失败: %v", err)
		return
	}

	go func() {
		maxRetries := 5
		retryDelay := time.Second

		for i := 0; i < maxRetries; i++ {
			select {
			case globalBroadcast <- recordJSON:
				log.Println("WebSocket 推送成功")
				return
			default:
				if i < maxRetries-1 {
					log.Printf("WebSocket 推送失败，%d秒后重试 (%d/%d)...", retryDelay/time.Second, i+1, maxRetries)
					time.Sleep(retryDelay)
					// 每次重试增加延迟
					retryDelay *= 2
				} else {
					log.Printf("WebSocket 推送失败，达到最大重试次数 (%d)，消息丢弃", maxRetries)
				}
			}
		}
	}()
}

// sendMessageToMQAsync 异步发送消息到RocketMQ
func (s *ProductMonitorService) sendMessageToMQAsync(record interface{}, productID, keyword string, rqTime time.Time) {
	go func() {
		// 生成UUID作为消息唯一ID
		messageKey := uuid.New().String()

		// 将记录转换为JSON
		recordJSON, err := json.Marshal(record)
		if err != nil {
			log.Printf("序列化记录失败: %v", err)
			return
		}

		// 创建消息
		msg := primitive.NewMessage(
			"spider_items_keyword",
			recordJSON,
		)
		msg.WithKeys([]string{messageKey}) // 设置消息Key
		_, err = s.mqClient.GetProducer().SendSync(context.Background(), msg)
		if err != nil {
			// 将失败的消息封装
			failedMsg := map[string]interface{}{
				"topic":      msg.Topic,
				"body":       string(recordJSON),
				"timestamp":  time.Now().Unix(),
				"error":      err.Error(),
				"messageKey": messageKey,
				"productId":  productID,
				"keyword":    keyword,
				"rqTime":     rqTime.Unix(),
			}
			failedMsgJSON, err := json.Marshal(failedMsg)
			if err != nil {
				log.Printf("序列化失败消息记录失败: %v", err)
				return
			}

			err = s.redisClient.RPush(context.Background(), REDIS_FAILED_QUEUE_KEY, string(failedMsgJSON))
			if err != nil {
				log.Printf("推送失败消息到Redis队列失败: %v", err)
				return
			}
			//log.Printf("消息发送失败，已推送到Redis队列，消息Key: %s, 产品ID: %s, 关键词: %s", messageKey, productID, keyword)
			return
		}
		//log.Printf("消息发送成功，msgId: %s, messageKey: %s, 产品ID: %s, 关键词: %s", result.MsgID, messageKey, productID, keyword)
	}()
}

// processKeyword 处理关键词搜索
func (s *ProductMonitorService) processKeyword(product MonitorProduct, keyword string, rqTime time.Time) *search.SearchProduct {
	// 获取搜索结果（函数本身会获取所有数据）
	searchProducts, err := s.webClient.GetSearchInfoByKeyword(keyword, 1)
	if err != nil {
		log.Printf("获取搜索结果失败 [%s]: %v", keyword, err)
		return nil
	}

	// 创建目标产品的记录
	record := &search.SearchProduct{
		Id:          gconv.Int(product.ProductID),
		PlatformId:  "wb",
		SKU:         product.SKU,
		Keyword:     keyword,
		Rank:        0,
		InProcessAt: rqTime,
	}

	// 分析搜索结果，查找目标产品
	found := false
	// 记录前100个产品的排名和目标产品（如果在100名后）
	for i, item := range searchProducts {
		// 为每条记录生成独特的时间戳，在原始时间基础上加上毫秒级的偏移
		itemTime := rqTime.Add(time.Duration(i) * time.Millisecond)

		rank := i + 1
		// 如果是目标产品，更新目标产品的记录
		if fmt.Sprint(item.Id) == product.ProductID {
			found = true
			item.PlatformId = "wb"
			item.SKU = product.SKU
			item.Rank = rank
			item.Keyword = keyword
			item.InProcessAt = itemTime // 使用独特的时间戳
			log.Printf("找到目标产品[%s] 关键词 [%s] 在第 %d 位", product.ProductID, keyword, rank)
			// WebSocket 实时推送
			s.sendWebSocketAsync(&item)
			return &item
		}
	}

	// 如果未找到目标产品，保存1000+的记录
	if !found {
		// 使用原始时间戳，因为这是单独的记录
		record.InProcessAt = rqTime
		// WebSocket 实时推送
		s.sendWebSocketAsync(record)
		log.Printf("未找到目标产品[%s] 关键词 [%s] 在第 %d 位", product.ProductID, keyword, 0)
	}
	return record
}

// calculateBidAdjustment 计算广告出价调整
func (s *ProductMonitorService) calculateBidAdjustment(currentRank int, hour int, currentBid int) int {
	// 1. 定义时间段的目标排名范围
	type rankRange struct {
		min      int     // 最小排名（更好的排名）
		max      int     // 最大排名（更差的排名）
		baseBid  int     // 基础出价
		stepRate float64 // 调整步长比例
	}

	// 2. 根据时间段设置目标排名范围和调价参数
	var targetRange rankRange
	switch {
	case hour >= 1 && hour < 8:
		// 凌晨时段：固定最低价
		if currentBid != 190 {
			return 190 - currentBid // 直接返回到190的调整值
		}
		return 0
	case hour >= 8 && hour < 11:
		// 早高峰：排名要求较宽松，调价更激进
		targetRange = rankRange{
			min:      50,
			max:      100,
			baseBid:  250,  // 基础出价
			stepRate: 0.15, // 每次调整15%
		}
	case hour >= 11 && hour < 16:
		// 日间时段：排名要求适中，调价温和
		targetRange = rankRange{
			min:      20,
			max:      50,
			baseBid:  300,
			stepRate: 0.10,
		}
	case hour >= 16 && hour < 22:
		// 晚高峰：排名要求严格，调价积极
		targetRange = rankRange{
			min:      8,
			max:      20,
			baseBid:  350,
			stepRate: 0.12,
		}
	default: // 22-1点
		// 夜间时段：排名要求适中，调价保守
		targetRange = rankRange{
			min:      20,
			max:      50,
			baseBid:  280,
			stepRate: 0.08,
		}
	}

	// 3. 计算排名差距比例
	var rankDiffRatio float64
	switch {
	case currentRank > targetRange.max:
		// 排名太靠后，需要提高出价
		rankDiffRatio = float64(currentRank-targetRange.max) / float64(targetRange.max)
	case currentRank < targetRange.min:
		// 排名太靠前，可以降低出价
		rankDiffRatio = float64(targetRange.min-currentRank) / float64(targetRange.min)
	default:
		// 排名在目标范围内，无需调整
		return 0
	}

	// 4. 计算调整金额
	adjustment := 0
	if currentRank > targetRange.max {
		// 排名靠后，增加出价
		// 基础调整：当前出价的一定比例
		baseAdjustment := int(float64(currentBid) * targetRange.stepRate)
		// 根据排名差距增加调整幅度
		rankAdjustment := int(float64(baseAdjustment) * rankDiffRatio)
		// 最终调整值：基础调整 + 排名调整，且不超过50%
		adjustment = min(baseAdjustment+rankAdjustment, int(float64(currentBid)*0.5))
		// 确保最小调整幅度
		adjustment = max(adjustment, 10)
	} else if currentRank < targetRange.min {
		// 排名靠前，减少出价
		// 基础调整：当前出价的一定比例
		baseAdjustment := int(float64(currentBid) * targetRange.stepRate)
		// 根据排名差距增加调整幅度
		rankAdjustment := int(float64(baseAdjustment) * rankDiffRatio)
		// 最终调整值：基础调整 + 排名调整，且不超过30%
		adjustment = -min(baseAdjustment+rankAdjustment, int(float64(currentBid)*0.3))
		// 确保最小调整幅度
		adjustment = min(adjustment, -10)
	}

	// 5. 确保调整后的价格在合理范围内
	if currentBid+adjustment < 190 {
		return 190 - currentBid // 确保不低于最低价
	}
	if currentBid+adjustment > 500 {
		return 500 - currentBid // 设置最高价格限制
	}

	return adjustment
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// max 返回两个整数中的较大值
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// syncProductToRedis 同步单个产品配置到Redis
func (s *ProductMonitorService) syncProductToRedis(productID string) error {
	product, exists := s.products[productID]
	if !exists {
		// 如果产品不存在，从Redis中删除
		ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
		defer cancel()
		return s.redisClient.HDel(ctx, REDIS_MONITOR_PRODUCTS_KEY, productID)
	}

	// 将产品配置转换为JSON
	productJSON, err := json.Marshal(product)
	if err != nil {
		return fmt.Errorf("序列化产品配置失败: %v", err)
	}

	// 存储到Redis
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	return s.redisClient.HSet(ctx, REDIS_MONITOR_PRODUCTS_KEY, productID, string(productJSON))
}

// CreateProduct 创建新的监控产品
func (s *ProductMonitorService) CreateProduct(productID string, sku string, keywords []string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 检查产品是否已存在
	if _, exists := s.products[productID]; exists {
		return fmt.Errorf("产品ID %s 已存在", productID)
	}

	// 创建新产品
	s.products[productID] = MonitorProduct{
		ProductID: productID,
		SKU:       sku,
		Keywords:  keywords,
	}

	// 同步到Redis
	if err := s.syncProductToRedis(productID); err != nil {
		log.Printf("同步产品配置到Redis失败: %v", err)
	}

	log.Printf("已创建新产品 [%s], SKU: %s", productID, sku)
	return nil
}

// DeleteProduct 删除监控产品
func (s *ProductMonitorService) DeleteProduct(productID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 检查产品是否存在
	if _, exists := s.products[productID]; !exists {
		return fmt.Errorf("产品ID %s 不存在", productID)
	}

	// 删除产品
	delete(s.products, productID)

	// 从Redis中删除
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	if err := s.redisClient.HDel(ctx, REDIS_MONITOR_PRODUCTS_KEY, productID); err != nil {
		log.Printf("从Redis删除产品配置失败: %v", err)
	}

	log.Printf("已删除产品 [%s]", productID)
	return nil
}

// GetProduct 获取产品信息
func (s *ProductMonitorService) GetProduct(productID string) (*MonitorProduct, error) {
	//s.mu.RLock()
	//defer s.mu.RUnlock()

	product, exists := s.products[productID]
	if !exists {
		return nil, fmt.Errorf("产品ID %s 不存在", productID)
	}

	return &product, nil
}

// GetAllProducts 获取所有产品信息
func (s *ProductMonitorService) GetAllProducts() MonitorProductMap {
	//s.mu.RLock()
	//defer s.mu.RUnlock()

	// 创建一个新的 map 来避免直接返回内部 map
	products := make(MonitorProductMap)
	for k, v := range s.products {
		products[k] = v
	}

	return products
}

// UpdateProductKeywords 更新指定产品的关键词列表
func (s *ProductMonitorService) UpdateProductKeywords(productID string, keywords []string) error {
	//s.mu.Lock()
	//defer s.mu.Unlock()

	// 检查产品是否存在
	product, exists := s.products[productID]
	if !exists {
		return fmt.Errorf("产品ID %s 不存在", productID)
	}

	// 更新关键词
	product.Keywords = keywords
	s.products[productID] = product

	// 同步到Redis
	if err := s.syncProductToRedis(productID); err != nil {
		log.Printf("同步产品配置到Redis失败: %v", err)
	}

	// 记录日志
	log.Printf("已更新产品 [%s] 的关键词: %v", productID, keywords)
	return nil
}

// AppendProductKeywords 为指定产品追加关键词
func (s *ProductMonitorService) AppendProductKeywords(productID string, newKeywords []string) error {
	//s.mu.Lock()
	//defer s.mu.Unlock()

	// 检查产品是否存在
	product, exists := s.products[productID]
	if !exists {
		return fmt.Errorf("产品ID %s 不存在", productID)
	}

	// 创建一个map来检查重复的关键词
	existingKeywords := make(map[string]bool)
	for _, kw := range product.Keywords {
		existingKeywords[kw] = true
	}

	// 追加不重复的关键词
	for _, newKw := range newKeywords {
		if !existingKeywords[newKw] {
			product.Keywords = append(product.Keywords, newKw)
			existingKeywords[newKw] = true
		}
	}

	// 更新产品
	s.products[productID] = product

	// 同步到Redis
	if err := s.syncProductToRedis(productID); err != nil {
		log.Printf("同步产品配置到Redis失败: %v", err)
	}

	// 记录日志
	log.Printf("已为产品 [%s] 追加关键词，当前关键词列表: %v", productID, product.Keywords)
	return nil
}

// RemoveProductKeywords 从指定产品中删除关键词
func (s *ProductMonitorService) RemoveProductKeywords(productID string, keywordsToRemove []string) error {
	//s.mu.Lock()
	//defer s.mu.Unlock()

	// 检查产品是否存在
	product, exists := s.products[productID]
	if !exists {
		return fmt.Errorf("产品ID %s 不存在", productID)
	}

	// 创建一个map来存储要删除的关键词
	toRemove := make(map[string]bool)
	for _, kw := range keywordsToRemove {
		toRemove[kw] = true
	}

	// 创建新的关键词列表，排除要删除的关键词
	newKeywords := make([]string, 0)
	for _, kw := range product.Keywords {
		if !toRemove[kw] {
			newKeywords = append(newKeywords, kw)
		}
	}

	// 更新产品的关键词列表
	product.Keywords = newKeywords
	s.products[productID] = product

	// 同步到Redis
	if err := s.syncProductToRedis(productID); err != nil {
		log.Printf("同步产品配置到Redis失败: %v", err)
	}

	// 记录日志
	log.Printf("已从产品 [%s] 删除关键词，当前关键词列表: %v", productID, product.Keywords)
	return nil
}

// StartStatsMonitoring 开始监控广告统计数据
func (s *ProductMonitorService) StartStatsMonitoring() {
	ticker := time.NewTicker(3 * time.Minute)
	go func() {
		// 立即执行一次
		s.monitorStats()

		for {
			select {
			case <-ticker.C:
				s.monitorStats()
			case <-s.stopChan:
				ticker.Stop()
				return
			}
		}
	}()
}

// monitorStats 监控广告统计数据
func (s *ProductMonitorService) monitorStats() {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 获取当前日期
	now := time.Now()
	today := now.Format("2006-01-02")

	// 准备统计请求
	statsRequests := make([]*wbhubapi.StatsRequest, 0)
	advertIDMap := make(map[int]string) // 广告ID到产品ID的映射

	// 收集所有需要查询的广告ID
	for productID, product := range s.products {
		for _, advert := range product.Advert {
			statsRequests = append(statsRequests, &wbhubapi.StatsRequest{
				AdvertID: advert.AdvertID,
				Dates:    []string{today, today},
			})
			advertIDMap[advert.AdvertID] = productID
		}
	}

	// 如果没有广告ID，直接返回
	if len(statsRequests) == 0 {
		return
	}

	// 获取统计数据
	stats, err := s.wbhubClient.Promotion.GetFullStats(statsRequests)
	if err != nil {
		log.Printf("获取广告统计数据失败: %v", err)
		return
	}

	// 处理统计数据
	for _, stat := range stats {
		// 从请求中获取广告ID
		advertID := stat.AdvertId
		productID := advertIDMap[advertID]
		// 更新 s.products 中的广告数据
		if product, ok := s.products[productID]; ok {
			for j, advert := range product.Advert {
				if advert.AdvertID == advertID {
					// 更新广告统计数据
					product.Advert[j].Views = stat.Views
					product.Advert[j].Clicks = stat.Clicks
					product.Advert[j].CTR = stat.Ctr
					product.Advert[j].CPC = stat.Cpc
					product.Advert[j].Spending = stat.Sum
					product.Advert[j].Orders = stat.Orders
					product.Advert[j].UpdateTime = now
					// 更新 s.products
					product.Advert[j].SKU = product.SKU
					s.products[productID] = product
				}
			}
		}
	}

	// 构建统计数据响应用于 WebSocket 推送
	response := map[string][]ProductAdvert{}
	for productID, product := range s.products {
		response[productID] = product.Advert
	}
	//recordJSON, err := json.Marshal(response)
	//fmt.Println(string(recordJSON))
	s.sendWebSocketAsync(response)

}
