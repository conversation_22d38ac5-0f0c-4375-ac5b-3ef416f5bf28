package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/joho/godotenv"
)

// Config 通用配置结构
type Config struct {
	// Redis 配置
	RedisAddress  string
	RedisPassword string
	RedisDB       int

	// RocketMQ 配置
	RocketMQNameserver string
	RocketMQGroup      string

	// API Keys
	WBApiKey string

	// 服务配置
	MonitorInterval time.Duration
	CrawlerInterval time.Duration

	// 日志配置
	LogLevel   string
	LogFormat  string
	ServerPort int
}

// RedisConfig Redis配置接口适配器
func (c *Config) GetRedisAddress() string {
	return c.RedisAddress
}

func (c *Config) GetRedisPassword() string {
	return c.RedisPassword
}

func (c *Config) GetRedisDB() int {
	return c.RedisDB
}

// RocketMQConfig RocketMQ配置接口适配器
func (c *Config) GetRocketMQNameserver() string {
	return c.RocketMQNameserver
}

func (c *Config) GetRocketMQGroup() string {
	return c.RocketMQGroup
}

// LoadFromFile 从指定文件加载配置
func LoadFromFile(filename string) (*Config, error) {
	// 加载环境变量文件
	if err := godotenv.Load(filename); err != nil {
		return nil, fmt.Errorf("加载配置文件失败: %v", err)
	}

	// 使用环境变量加载配置
	return LoadFromEnv()
}

// LoadFromEnv 从环境变量加载配置
func LoadFromEnv() (*Config, error) {
	cfg := &Config{}

	// Redis 配置
	cfg.RedisAddress = getEnv("REDIS_ADDRESS", "localhost:6379")
	cfg.RedisPassword = getEnv("REDIS_PASSWORD", "")
	cfg.RedisDB = getEnvAsInt("REDIS_DB", 0)

	// RocketMQ 配置
	cfg.RocketMQNameserver = getEnv("ROCKETMQ_NAMESERVER", "localhost:9876")
	cfg.RocketMQGroup = getEnv("ROCKETMQ_GROUP", "lens-group")

	// API Keys
	cfg.WBApiKey = getEnv("WB_API_KEY", "")

	// 服务配置
	cfg.MonitorInterval = getEnvAsDuration("MONITOR_INTERVAL", "5m")
	cfg.CrawlerInterval = getEnvAsDuration("CRAWLER_INTERVAL", "1h")

	// 日志配置
	cfg.LogLevel = getEnv("LOG_LEVEL", "info")
	cfg.LogFormat = getEnv("LOG_FORMAT", "json")
	cfg.ServerPort = getEnvAsInt("SERVER_PORT", 8080)

	return cfg, nil
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultValue
}

// getEnvAsInt 获取环境变量并转换为整数
func getEnvAsInt(name string, defaultVal int) int {
	valueStr := getEnv(name, "")
	if value, err := strconv.Atoi(valueStr); err == nil {
		return value
	}
	return defaultVal
}

// getEnvAsDuration 获取环境变量并转换为时间间隔
func getEnvAsDuration(name string, defaultVal string) time.Duration {
	valueStr := getEnv(name, defaultVal)
	if duration, err := time.ParseDuration(strings.ToLower(valueStr)); err == nil {
		return duration
	}
	// 如果解析失败，返回默认值
	duration, _ := time.ParseDuration(defaultVal)
	return duration
}

// Validate 验证配置是否有效
func (c *Config) Validate() error {
	if c.RedisAddress == "" {
		return fmt.Errorf("REDIS_ADDRESS 不能为空")
	}

	if c.RocketMQNameserver == "" {
		return fmt.Errorf("ROCKETMQ_NAMESERVER 不能为空")
	}

	return nil
}
