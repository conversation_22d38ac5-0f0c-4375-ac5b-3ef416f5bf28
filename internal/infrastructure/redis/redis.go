package redis

import (
	"context"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
)

var (
	once     sync.Once
	instance *RedisClient
)

type RedisClient struct {
	client *redis.Client
}

// defaultRedisConfig 默认Redis配置结构体
type defaultRedisConfig struct {
	address  string
	password string
	db       int
}

func (d *defaultRedisConfig) RedisAddress() string {
	return d.address
}

func (d *defaultRedisConfig) RedisPassword() string {
	return d.password
}

func (d *defaultRedisConfig) RedisDB() int {
	return d.db
}

// GetInstance 获取Redis客户端单例（使用默认配置）
func GetInstance() *RedisClient {
	// 创建默认配置
	defaultCfg := &defaultRedisConfig{
		address:  "************:6379",
		password: "Ls3903850",
		db:       0,
	}
	
	return GetInstanceWithConfig(defaultCfg)
}

// RedisConfig Redis配置接口
type RedisConfig interface {
	RedisAddress() string
	RedisPassword() string
	RedisDB() int
}

// GetInstanceWithConfig 使用配置获取Redis客户端单例
func GetInstanceWithConfig(cfg RedisConfig) *RedisClient {
	once.Do(func() {
		instance = &RedisClient{
			client: redis.NewClient(&redis.Options{
				Addr:         cfg.RedisAddress(),
				Password:     cfg.RedisPassword(),
				DB:           cfg.RedisDB(),
				PoolSize:     10,   // 连接池大小
				MinIdleConns: 5,    // 最小空闲连接数
				MaxRetries:   3,    // 最大重试次数
			}),
		}
	})
	return instance
}

// Set 设置键值对
func (r *RedisClient) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return r.client.Set(ctx, key, value, expiration).Err()
}

// Get 获取值
func (r *RedisClient) Get(ctx context.Context, key string) (string, error) {
	return r.client.Get(ctx, key).Result()
}

// Del 删除键
func (r *RedisClient) Del(ctx context.Context, keys ...string) error {
	return r.client.Del(ctx, keys...).Err()
}

// Close 关闭Redis连接
func (r *RedisClient) Close() error {
	return r.client.Close()
}

// Ping 测试Redis连接
func (r *RedisClient) Ping(ctx context.Context) error {
	return r.client.Ping(ctx).Err()
}

// HSet 设置Hash字段
func (r *RedisClient) HSet(ctx context.Context, key string, field string, value interface{}) error {
	return r.client.HSet(ctx, key, field, value).Err()
}

// HGet 获取Hash字段
func (r *RedisClient) HGet(ctx context.Context, key string, field string) (string, error) {
	return r.client.HGet(ctx, key, field).Result()
}

// HGetAll 获取Hash所有字段
func (r *RedisClient) HGetAll(ctx context.Context, key string) (map[string]string, error) {
	return r.client.HGetAll(ctx, key).Result()
}

// HDel 删除Hash字段
func (r *RedisClient) HDel(ctx context.Context, key string, fields ...string) error {
	return r.client.HDel(ctx, key, fields...).Err()
}

// HExists 检查Hash字段是否存在
func (r *RedisClient) HExists(ctx context.Context, key string, field string) (bool, error) {
	return r.client.HExists(ctx, key, field).Result()
}

// RPush 将元素推送到列表末尾
func (r *RedisClient) RPush(ctx context.Context, key string, values ...interface{}) error {
	return r.client.RPush(ctx, key, values...).Err()
}

// LPop 从列表头部弹出元素
func (r *RedisClient) LPop(ctx context.Context, key string) (string, error) {
	return r.client.LPop(ctx, key).Result()
}

// BLPop 阻塞式从列表头部弹出元素
func (r *RedisClient) BLPop(ctx context.Context, timeout time.Duration, keys ...string) ([]string, error) {
	return r.client.BLPop(ctx, timeout, keys...).Result()
}

// LLen 获取列表长度
func (r *RedisClient) LLen(ctx context.Context, key string) (int64, error) {
	return r.client.LLen(ctx, key).Result()
}
