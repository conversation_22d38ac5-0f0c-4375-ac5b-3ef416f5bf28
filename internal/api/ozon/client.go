package ozonapi

import (
	"time"

	"github.com/go-resty/resty/v2"
)

const (
	defaultTimeout = 10 * time.Second
)

// Client Ozon API 客户端
type Client struct {
	httpClient *resty.Client // HTTP 请求客户端

	// API 服务
	Categories *CategoryService  // 分类服务
	Products   *ProductService   // 商品服务
	Orders     *OrderService     // 订单服务
	Analytics  *AnalyticsService // 分析服务
	FBO        *FBOService       // FBO服务
	// 这里可以添加其他服务模块
}

// ClientOption 客户端配置选项
type ClientOption func(*Client)

// WithTimeout 设置超时时间
func WithTimeout(timeout time.Duration) ClientOption {
	return func(c *Client) {
		c.httpClient.SetTimeout(timeout)
	}
}

// NewClient 创建新的 Ozon API 客户端
func NewClient(apiKey, clientId string, opts ...ClientOption) *Client {
	rc := resty.New()
	rc.SetTimeout(defaultTimeout)
	rc.SetHeader("Client-Id", clientId) // Ozon API需要Client-Id header
	rc.SetHeader("Api-Key", apiKey)     // Ozon API需要Api-Key header
	rc.SetHeader("Host", "api-seller.ozon.ru")
	rc.SetHeader("Accept", "application/json")
	rc.SetBaseURL("https://api-seller.ozon.ru")
	rc.SetHeader("Content-Type", "application/json")
	rc.SetProxy("socks5://lens:ls3903850@*************:23481")
	c := &Client{
		httpClient: rc,
	}

	// 应用自定义选项
	for _, opt := range opts {
		opt(c)
	}

	// 初始化各个服务
	c.Products = newProductService(c)
	c.Orders = newOrderService(c)
	c.Analytics = newAnalyticsService(c)
	c.FBO = newFBOService(c)

	return c
}

// GetHTTPClient 获取HTTP客户端
func (c *Client) GetHTTPClient() *resty.Client {
	return c.httpClient
}
