package ozonapi

import (
	"time"
)

// OrderListRequest 获取订单列表请求
type OrderListRequest struct {
	Dir    string      `json:"dir"`    // ASC, DESC
	Filter OrderFilter `json:"filter"`
	Limit  int32       `json:"limit"`
	Offset int32       `json:"offset"`
	With   OrderWith   `json:"with"`
}

// OrderFilter 订单过滤器
type OrderFilter struct {
	Since      time.Time `json:"since"`
	To         time.Time `json:"to"`
	Status     string    `json:"status,omitempty"`     // awaiting_packaging, awaiting_deliver, delivered, cancelled
	DeliveryMethod []string `json:"delivery_method,omitempty"` // fbo, fbs, crossdock
}

// OrderWith 订单附加信息
type OrderWith struct {
	AnalyticsData bool `json:"analytics_data,omitempty"`
	FinancialData bool `json:"financial_data,omitempty"`
}

// OrderListResponse 获取订单列表响应
type OrderListResponse struct {
	Result []Order `json:"result"`
}

// Order 订单信息
type Order struct {
	OrderID           int64             `json:"order_id"`
	OrderNumber       string            `json:"order_number"`
	PostedNumber      string            `json:"posted_number"`
	Status            string            `json:"status"`
	CancelReasonID    int64             `json:"cancel_reason_id"`
	CreatedAt         time.Time         `json:"created_at"`
	InProcessAt       time.Time         `json:"in_process_at"`
	Products          []OrderProduct    `json:"products"`
	AnalyticsData     *OrderAnalytics   `json:"analytics_data,omitempty"`
	FinancialData     *OrderFinancial   `json:"financial_data,omitempty"`
	AdditionalData    []interface{}     `json:"additional_data"`
	DeliveryMethod    DeliveryMethod    `json:"delivery_method"`
	IsMultibox        bool              `json:"is_multibox"`
	Requirements      OrderRequirements `json:"requirements"`
	TPLIntegrationID  string            `json:"tpl_integration_id"`
	ParentPostingNumber string          `json:"parent_posting_number"`
	MultiBoxQty       int32             `json:"multi_box_qty"`
	Addressee         *OrderAddressee   `json:"addressee,omitempty"`
	BarcodeInfo       *OrderBarcode     `json:"barcode_info,omitempty"`
	DeliveryPrice     string            `json:"delivery_price"`
	IsEarlyShip       bool              `json:"is_early_ship"`
	ShipmentDate      time.Time         `json:"shipment_date"`
	DeliveryDate      *OrderDeliveryDate `json:"delivery_date,omitempty"`
}

// OrderProduct 订单商品
type OrderProduct struct {
	SKU              int64   `json:"sku"`
	Name             string  `json:"name"`
	Quantity         int32   `json:"quantity"`
	OfferID          string  `json:"offer_id"`
	Price            string  `json:"price"`
	DigitalCodes     []string `json:"digital_codes"`
	CurrencyCode     string  `json:"currency_code"`
	MandatoryMark    []string `json:"mandatory_mark"`
	Dimensions       ProductDimensions `json:"dimensions"`
}

// OrderAnalytics 订单分析数据
type OrderAnalytics struct {
	Region            string `json:"region"`
	City              string `json:"city"`
	DeliveryType      string `json:"delivery_type"`
	IsPremium         bool   `json:"is_premium"`
	PaymentType       string `json:"payment_type"`
	WarehouseID       int64  `json:"warehouse_id"`
	WarehouseName     string `json:"warehouse_name"`
	IsLegal           bool   `json:"is_legal"`
}

// OrderFinancial 订单财务数据
type OrderFinancial struct {
	PostingServices OrderPostingServices `json:"posting_services"`
	Products        []OrderFinancialProduct `json:"products"`
}

// OrderPostingServices 订单服务费用
type OrderPostingServices struct {
	MarketplaceServiceItemFulfillment        float64 `json:"marketplace_service_item_fulfillment"`
	MarketplaceServiceItemPickup             float64 `json:"marketplace_service_item_pickup"`
	MarketplaceServiceItemDropoffPVZ         float64 `json:"marketplace_service_item_dropoff_pvz"`
	MarketplaceServiceItemDropoffSC          float64 `json:"marketplace_service_item_dropoff_sc"`
	MarketplaceServiceItemDropoffFF          float64 `json:"marketplace_service_item_dropoff_ff"`
	MarketplaceServiceItemDirectFlowTrans    float64 `json:"marketplace_service_item_direct_flow_trans"`
	MarketplaceServiceItemReturnFlowTrans    float64 `json:"marketplace_service_item_return_flow_trans"`
	MarketplaceServiceItemDelivToCustomer    float64 `json:"marketplace_service_item_deliv_to_customer"`
	MarketplaceServiceItemReturnNotDelivToCustomer float64 `json:"marketplace_service_item_return_not_deliv_to_customer"`
	MarketplaceServiceItemReturnPartGoodsCustomer  float64 `json:"marketplace_service_item_return_part_goods_customer"`
	MarketplaceServiceItemReturnAfterDelivToCustomer float64 `json:"marketplace_service_item_return_after_deliv_to_customer"`
}

// OrderFinancialProduct 订单商品财务信息
type OrderFinancialProduct struct {
	CommissionAmount          float64 `json:"commission_amount"`
	CommissionPercent         float64 `json:"commission_percent"`
	Payout                    float64 `json:"payout"`
	ProductID                 int64   `json:"product_id"`
	CurrencyCode              string  `json:"currency_code"`
	OldPrice                  float64 `json:"old_price"`
	Price                     float64 `json:"price"`
	TotalDiscountValue        float64 `json:"total_discount_value"`
	TotalDiscountPercent      float64 `json:"total_discount_percent"`
	Actions                   []string `json:"actions"`
	Picking                   *OrderFinancialPicking `json:"picking,omitempty"`
	Quantity                  int32   `json:"quantity"`
	ClientPrice               string  `json:"client_price"`
	ItemServices              OrderItemServices `json:"item_services"`
}

// OrderFinancialPicking 拣货信息
type OrderFinancialPicking struct {
	Amount   float64 `json:"amount"`
	Moment   time.Time `json:"moment"`
	Tag      string  `json:"tag"`
}

// OrderItemServices 商品服务费用
type OrderItemServices struct {
	MarketplaceServiceItemFulfillment        float64 `json:"marketplace_service_item_fulfillment"`
	MarketplaceServiceItemPickup             float64 `json:"marketplace_service_item_pickup"`
	MarketplaceServiceItemDropoffPVZ         float64 `json:"marketplace_service_item_dropoff_pvz"`
	MarketplaceServiceItemDropoffSC          float64 `json:"marketplace_service_item_dropoff_sc"`
	MarketplaceServiceItemDropoffFF          float64 `json:"marketplace_service_item_dropoff_ff"`
	MarketplaceServiceItemDirectFlowTrans    float64 `json:"marketplace_service_item_direct_flow_trans"`
	MarketplaceServiceItemReturnFlowTrans    float64 `json:"marketplace_service_item_return_flow_trans"`
	MarketplaceServiceItemDelivToCustomer    float64 `json:"marketplace_service_item_deliv_to_customer"`
	MarketplaceServiceItemReturnNotDelivToCustomer float64 `json:"marketplace_service_item_return_not_deliv_to_customer"`
	MarketplaceServiceItemReturnPartGoodsCustomer  float64 `json:"marketplace_service_item_return_part_goods_customer"`
	MarketplaceServiceItemReturnAfterDelivToCustomer float64 `json:"marketplace_service_item_return_after_deliv_to_customer"`
}

// DeliveryMethod 配送方式
type DeliveryMethod struct {
	ID           int64  `json:"id"`
	Name         string `json:"name"`
	WarehouseID  int64  `json:"warehouse_id"`
	Warehouse    string `json:"warehouse"`
	TPLProviderID int64 `json:"tpl_provider_id"`
	TPLProvider  string `json:"tpl_provider"`
}

// OrderRequirements 订单要求
type OrderRequirements struct {
	ProductsRequiringGTD      []int64 `json:"products_requiring_gtd"`
	ProductsRequiringCountry  []int64 `json:"products_requiring_country"`
	ProductsRequiringMandatoryMark []int64 `json:"products_requiring_mandatory_mark"`
}

// OrderAddressee 收件人信息
type OrderAddressee struct {
	Name  string `json:"name"`
	Phone string `json:"phone"`
}

// OrderBarcode 条码信息
type OrderBarcode struct {
	UpperBarcode string `json:"upper_barcode"`
	LowerBarcode string `json:"lower_barcode"`
}

// OrderDeliveryDate 配送日期
type OrderDeliveryDate struct {
	From time.Time `json:"from"`
	To   time.Time `json:"to"`
	Name string    `json:"name"`
}

// OrderCancelRequest 取消订单请求
type OrderCancelRequest struct {
	CancelReasonID   int64  `json:"cancel_reason_id"`
	CancelReasonMessage string `json:"cancel_reason_message"`
	Items            []OrderCancelItem `json:"items"`
	PostingNumber    string `json:"posting_number"`
}

// OrderCancelItem 取消订单商品
type OrderCancelItem struct {
	SKU      int64 `json:"sku"`
	Quantity int32 `json:"quantity"`
}

// OrderCancelResponse 取消订单响应
type OrderCancelResponse struct {
	Result bool `json:"result"`
}

// OrderShipRequest 发货请求
type OrderShipRequest struct {
	Packages      []OrderPackage `json:"packages"`
	PostingNumber string         `json:"posting_number"`
	With          *OrderShipWith `json:"with,omitempty"`
}

// OrderPackage 包裹信息
type OrderPackage struct {
	Products []OrderShipProduct `json:"products"`
}

// OrderShipProduct 发货商品
type OrderShipProduct struct {
	ProductID int64 `json:"product_id"`
	Quantity  int32 `json:"quantity"`
}

// OrderShipWith 发货附加信息
type OrderShipWith struct {
	AdditionalData bool `json:"additional_data,omitempty"`
}

// OrderShipResponse 发货响应
type OrderShipResponse struct {
	Result []OrderShipResult `json:"result"`
}

// OrderShipResult 发货结果
type OrderShipResult struct {
	PostingNumber string   `json:"posting_number"`
	Status        string   `json:"status"`
	Errors        []string `json:"errors"`
}

// OrderUnfulfilledListRequest 获取未履行订单列表请求
type OrderUnfulfilledListRequest struct {
	Dir    string                 `json:"dir"`    // ASC, DESC
	Filter OrderUnfulfilledFilter `json:"filter"`
	Limit  int32                  `json:"limit"`
	Offset int32                  `json:"offset"`
}

// OrderUnfulfilledFilter 未履行订单过滤器
type OrderUnfulfilledFilter struct {
	CutoffFrom time.Time `json:"cutoff_from"`
	CutoffTo   time.Time `json:"cutoff_to"`
	DeliveryMethod []string `json:"delivery_method,omitempty"`
	ProviderID []int64   `json:"provider_id,omitempty"`
	Status     string    `json:"status,omitempty"`
	WarehouseID []int64  `json:"warehouse_id,omitempty"`
}

// OrderUnfulfilledListResponse 获取未履行订单列表响应
type OrderUnfulfilledListResponse struct {
	Result struct {
		Postings []OrderUnfulfilled `json:"postings"`
		HasNext  bool               `json:"has_next"`
	} `json:"result"`
}

// OrderUnfulfilled 未履行订单
type OrderUnfulfilled struct {
	PostingNumber    string                    `json:"posting_number"`
	OrderID          int64                     `json:"order_id"`
	OrderNumber      string                    `json:"order_number"`
	Status           string                    `json:"status"`
	DeliveryMethod   DeliveryMethod            `json:"delivery_method"`
	TrackingNumber   string                    `json:"tracking_number"`
	TPLIntegrationID string                    `json:"tpl_integration_id"`
	InProcessAt      time.Time                 `json:"in_process_at"`
	ShipmentDate     time.Time                 `json:"shipment_date"`
	DeliveryDate     *OrderDeliveryDate        `json:"delivery_date,omitempty"`
	Addressee        *OrderAddressee           `json:"addressee,omitempty"`
	BarcodeInfo      *OrderBarcode             `json:"barcode_info,omitempty"`
	DeliveryPrice    string                    `json:"delivery_price"`
	CancellationInfo *OrderCancellationInfo    `json:"cancellation_info,omitempty"`
	CustomerInfo     *OrderCustomerInfo        `json:"customer_info,omitempty"`
	Products         []OrderProduct            `json:"products"`
	Requirements     OrderRequirements         `json:"requirements"`
	ParentPostingNumber string                 `json:"parent_posting_number"`
	MultiBoxQty      int32                     `json:"multi_box_qty"`
	IsMultibox       bool                      `json:"is_multibox"`
	Substatus        string                    `json:"substatus"`
}

// OrderCancellationInfo 取消信息
type OrderCancellationInfo struct {
	CancelReasonID      int64     `json:"cancel_reason_id"`
	CancelReasonMessage string    `json:"cancel_reason_message"`
	CancellationInitiator string  `json:"cancellation_initiator"`
	CancellationDate    time.Time `json:"cancellation_date"`
	CancelledAfterShip  bool      `json:"cancelled_after_ship"`
	AffectCancellationRating bool `json:"affect_cancellation_rating"`
	CancellationReason  string    `json:"cancellation_reason"`
}

// OrderCustomerInfo 客户信息
type OrderCustomerInfo struct {
	CustomerID   int64  `json:"customer_id"`
	Name         string `json:"name"`
	Phone        string `json:"phone"`
	Email        string `json:"email"`
}

// ReturnRequest 获取退货信息请求
type ReturnRequest struct {
	Filter ReturnFilter `json:"filter"`
	Limit  int32        `json:"limit,omitempty"`
	Offset int32        `json:"offset,omitempty"`
}

// ReturnFilter 退货过滤器
type ReturnFilter struct {
	OrderNumber   string    `json:"order_number,omitempty"`
	PostingNumber string    `json:"posting_number,omitempty"`
	Status        string    `json:"status,omitempty"`
	Since         time.Time `json:"since,omitempty"`
	To            time.Time `json:"to,omitempty"`
}

// ReturnResponse 获取退货信息响应
type ReturnResponse struct {
	Returns []Return `json:"returns"`
}

// Return 退货信息
type Return struct {
	ID                int64         `json:"id"`
	PostingNumber     string        `json:"posting_number"`
	OrderNumber       string        `json:"order_number"`
	Status            string        `json:"status"`
	StatusName        string        `json:"status_name"`
	CreatedAt         time.Time     `json:"created_at"`
	ReturnDate        time.Time     `json:"return_date"`
	ReturnReason      string        `json:"return_reason"`
	ReturnReasonName  string        `json:"return_reason_name"`
	Products          []ReturnProduct `json:"products"`
	CompanyID         int64         `json:"company_id"`
	AcceptedFromCustomerMoment time.Time `json:"accepted_from_customer_moment"`
	LastFreeWaitingDay time.Time     `json:"last_free_waiting_day"`
	IsOpened          bool          `json:"is_opened"`
	Place             string        `json:"place"`
	PlaceName         string        `json:"place_name"`
	CommissionPercent float64       `json:"commission_percent"`
	PaidToCustomer    float64       `json:"paid_to_customer"`
	ProductPrice      float64       `json:"product_price"`
	CurrencyCode      string        `json:"currency_code"`
}

// ReturnProduct 退货商品
type ReturnProduct struct {
	SKU              int64   `json:"sku"`
	Name             string  `json:"name"`
	OfferID          string  `json:"offer_id"`
	Price            float64 `json:"price"`
	Quantity         int32   `json:"quantity"`
	ReturnedQuantity int32   `json:"returned_quantity"`
	CurrencyCode     string  `json:"currency_code"`
}

// ChatListRequest 获取聊天列表请求
type ChatListRequest struct {
	Filter ChatListFilter `json:"filter"`
	Limit  int32          `json:"limit,omitempty"`
	Offset int32          `json:"offset,omitempty"`
}

// ChatListFilter 聊天列表过滤器
type ChatListFilter struct {
	PostingNumber string    `json:"posting_number,omitempty"`
	Status        string    `json:"status,omitempty"`
	Since         time.Time `json:"since,omitempty"`
	To            time.Time `json:"to,omitempty"`
}

// ChatListResponse 获取聊天列表响应
type ChatListResponse struct {
	Chats []Chat `json:"chats"`
}

// Chat 聊天信息
type Chat struct {
	ChatID        string    `json:"chat_id"`
	PostingNumber string    `json:"posting_number"`
	Status        string    `json:"status"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	UnreadCount   int32     `json:"unread_count"`
	LastMessage   ChatMessage `json:"last_message"`
}

// ChatHistoryRequest 获取聊天历史请求
type ChatHistoryRequest struct {
	ChatID string `json:"chat_id"`
	Limit  int32  `json:"limit,omitempty"`
	Offset int32  `json:"offset,omitempty"`
}

// ChatHistoryResponse 获取聊天历史响应
type ChatHistoryResponse struct {
	Messages []ChatMessage `json:"messages"`
}

// ChatMessage 聊天消息
type ChatMessage struct {
	ID        string    `json:"id"`
	ChatID    string    `json:"chat_id"`
	UserID    string    `json:"user_id"`
	UserType  string    `json:"user_type"`
	Text      string    `json:"text"`
	CreatedAt time.Time `json:"created_at"`
	Files     []ChatFile `json:"files"`
}

// ChatFile 聊天文件
type ChatFile struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	URL      string `json:"url"`
	MimeType string `json:"mime_type"`
	Size     int64  `json:"size"`
}

// ChatSendMessageRequest 发送聊天消息请求
type ChatSendMessageRequest struct {
	ChatID string `json:"chat_id"`
	Text   string `json:"text"`
}

// ChatSendMessageResponse 发送聊天消息响应
type ChatSendMessageResponse struct {
	MessageID string `json:"message_id"`
}

// CancellationListRequest 获取取消申请列表请求
type CancellationListRequest struct {
	Filter CancellationFilter `json:"filter"`
	Limit  int32              `json:"limit,omitempty"`
	Offset int32              `json:"offset,omitempty"`
}

// CancellationFilter 取消申请过滤器
type CancellationFilter struct {
	PostingNumber string    `json:"posting_number,omitempty"`
	Status        string    `json:"status,omitempty"`
	Since         time.Time `json:"since,omitempty"`
	To            time.Time `json:"to,omitempty"`
}

// CancellationListResponse 获取取消申请列表响应
type CancellationListResponse struct {
	Cancellations []Cancellation `json:"cancellations"`
}

// Cancellation 取消申请
type Cancellation struct {
	ID                    int64     `json:"id"`
	PostingNumber         string    `json:"posting_number"`
	Status                string    `json:"status"`
	StatusName            string    `json:"status_name"`
	CancelReasonID        int64     `json:"cancel_reason_id"`
	CancelReasonName      string    `json:"cancel_reason_name"`
	CancelReasonMessage   string    `json:"cancel_reason_message"`
	CreatedAt             time.Time `json:"created_at"`
	UpdatedAt             time.Time `json:"updated_at"`
	CancellationInitiator string    `json:"cancellation_initiator"`
	Products              []CancellationProduct `json:"products"`
}

// CancellationProduct 取消申请商品
type CancellationProduct struct {
	SKU      int64 `json:"sku"`
	Quantity int32 `json:"quantity"`
}

// CancellationReasonResponse 获取取消原因列表响应
type CancellationReasonResponse struct {
	Reasons []CancellationReason `json:"reasons"`
}

// CancellationReason 取消原因
type CancellationReason struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	TypeName    string `json:"type_name"`
	IsAvailable bool   `json:"is_available"`
}
