package ozonapi

import (
	"time"
)

// ProductListRequest 获取商品列表请求
type ProductListRequest struct {
	Filter *ProductFilter `json:"filter,omitempty"`
	LastID string         `json:"last_id,omitempty"`
	Limit  int32          `json:"limit,omitempty"`
}

// ProductFilter 商品过滤器
type ProductFilter struct {
	OfferID    []string `json:"offer_id,omitempty"`
	ProductID  []int64  `json:"product_id,omitempty"`
	Visibility string   `json:"visibility,omitempty"` // ALL, VISIBLE, INVISIBLE
}

// ProductListResponse 获取商品列表响应
type ProductListResponse struct {
	Result struct {
		Items  []ProductItem `json:"items"`
		Total  int32         `json:"total"`
		LastID string        `json:"last_id"`
	} `json:"result"`
}

// ProductItem 商品项
type ProductItem struct {
	ProductID         int64    `json:"product_id"`
	OfferID           string   `json:"offer_id"`
	IsVisible         bool     `json:"is_visible"`
	IsDiscounted      bool     `json:"is_discounted"`
	HasDiscountedItem bool     `json:"has_discounted_item"`
	IsKGT             bool     `json:"is_kgt"`
	CurrencyCode      string   `json:"currency_code"`
	MarketingPrice    string   `json:"marketing_price"`
	MinPrice          string   `json:"min_price"`
	OldPrice          string   `json:"old_price"`
	PremiumPrice      string   `json:"premium_price"`
	Price             string   `json:"price"`
	RecommendedPrice  string   `json:"recommended_price"`
	Sources           []Source `json:"sources"`
	Stocks            struct {
		Coming   int32 `json:"coming"`
		Present  int32 `json:"present"`
		Reserved int32 `json:"reserved"`
	} `json:"stocks"`
	Errors []string `json:"errors"`
}

// Source 价格来源
type Source struct {
	IsEnabled bool   `json:"is_enabled"`
	SKU       int64  `json:"sku"`
	Source    string `json:"source"`
}

// ProductInfoRequest 获取商品信息请求
type ProductInfoRequest struct {
	OfferID   []string `json:"offer_id,omitempty"`
	ProductID []int64  `json:"product_id,omitempty"`
	SKU       []int64  `json:"sku,omitempty"`
}

// ProductInfoResponse 获取商品信息响应
type ProductInfoResponse struct {
	Items []ProductInfo `json:"items"`
}
type ProductInfo struct {
	Barcodes    []string `json:"barcodes"`
	ColorImage  []string `json:"color_image"`
	Commissions []struct {
		DeliveryAmount int    `json:"delivery_amount"`
		Percent        int    `json:"percent"`
		ReturnAmount   int    `json:"return_amount"`
		SaleSchema     string `json:"sale_schema"`
		Value          int    `json:"value"`
	} `json:"commissions"`
	CreatedAt             time.Time `json:"created_at"`
	CurrencyCode          string    `json:"currency_code"`
	DescriptionCategoryId int       `json:"description_category_id"`
	DiscountedFboStocks   int       `json:"discounted_fbo_stocks"`
	Errors                []struct {
		AttributeId int    `json:"attribute_id"`
		Code        string `json:"code"`
		Field       string `json:"field"`
		Level       string `json:"level"`
		State       string `json:"state"`
		Texts       struct {
			AttributeName string `json:"attribute_name"`
			Description   string `json:"description"`
			HintCode      string `json:"hint_code"`
			Message       string `json:"message"`
			Params        []struct {
				Name  string `json:"name"`
				Value string `json:"value"`
			} `json:"params"`
			ShortDescription string `json:"short_description"`
		} `json:"texts"`
	} `json:"errors"`
	HasDiscountedFboItem bool     `json:"has_discounted_fbo_item"`
	Id                   int      `json:"id"`
	Images               []string `json:"images"`
	Images360            []string `json:"images360"`
	IsArchived           bool     `json:"is_archived"`
	IsAutoarchived       bool     `json:"is_autoarchived"`
	IsDiscounted         bool     `json:"is_discounted"`
	IsKgt                bool     `json:"is_kgt"`
	IsPrepaymentAllowed  bool     `json:"is_prepayment_allowed"`
	IsSuper              bool     `json:"is_super"`
	MarketingPrice       string   `json:"marketing_price"`
	MinPrice             string   `json:"min_price"`
	ModelInfo            struct {
		Count   int `json:"count"`
		ModelId int `json:"model_id"`
	} `json:"model_info"`
	Name         string `json:"name"`
	OfferId      string `json:"offer_id"`
	OldPrice     string `json:"old_price"`
	Price        string `json:"price"`
	PriceIndexes struct {
		ColorIndex        string `json:"color_index"`
		ExternalIndexData struct {
			MinimalPrice         string `json:"minimal_price"`
			MinimalPriceCurrency string `json:"minimal_price_currency"`
			PriceIndexValue      int    `json:"price_index_value"`
		} `json:"external_index_data"`
		OzonIndexData struct {
			MinimalPrice         string `json:"minimal_price"`
			MinimalPriceCurrency string `json:"minimal_price_currency"`
			PriceIndexValue      int    `json:"price_index_value"`
		} `json:"ozon_index_data"`
		SelfMarketplacesIndexData struct {
			MinimalPrice         string `json:"minimal_price"`
			MinimalPriceCurrency string `json:"minimal_price_currency"`
			PriceIndexValue      int    `json:"price_index_value"`
		} `json:"self_marketplaces_index_data"`
	} `json:"price_indexes"`
	PrimaryImage []string `json:"primary_image"`
	Sources      []struct {
		CreatedAt    time.Time `json:"created_at"`
		QuantCode    string    `json:"quant_code"`
		ShipmentType string    `json:"shipment_type"`
		Sku          int64     `json:"sku"`
		Source       string    `json:"source"`
	} `json:"sources"`
	Statuses struct {
		IsCreated         bool      `json:"is_created"`
		ModerateStatus    string    `json:"moderate_status"`
		Status            string    `json:"status"`
		StatusDescription string    `json:"status_description"`
		StatusFailed      string    `json:"status_failed"`
		StatusName        string    `json:"status_name"`
		StatusTooltip     string    `json:"status_tooltip"`
		StatusUpdatedAt   time.Time `json:"status_updated_at"`
		ValidationStatus  string    `json:"validation_status"`
	} `json:"statuses"`
	Stocks struct {
		HasStock bool `json:"has_stock"`
		Stocks   []struct {
			Present  int    `json:"present"`
			Reserved int    `json:"reserved"`
			Sku      int    `json:"sku"`
			Source   string `json:"source"`
		} `json:"stocks"`
	} `json:"stocks"`
	TypeId            int       `json:"type_id"`
	UpdatedAt         time.Time `json:"updated_at"`
	Vat               string    `json:"vat"`
	VisibilityDetails struct {
		HasPrice bool `json:"has_price"`
		HasStock bool `json:"has_stock"`
	} `json:"visibility_details"`
	VolumeWeight int `json:"volume_weight"`
}

// ProductInfo 商品详细信息
type ProductInfo2 struct {
	ID                int64                `json:"id"`
	Name              string               `json:"name"`
	OfferID           string               `json:"offer_id"`
	Barcode           string               `json:"barcode"`
	BuyboxPrice       string               `json:"buybox_price"`
	CategoryID        int64                `json:"category_id"`
	CreatedAt         time.Time            `json:"created_at"`
	Images            []ProductImage       `json:"images"`
	MarketingPrice    string               `json:"marketing_price"`
	MinPrice          string               `json:"min_price"`
	OldPrice          string               `json:"old_price"`
	PremiumPrice      string               `json:"premium_price"`
	Price             string               `json:"price"`
	RecommendedPrice  string               `json:"recommended_price"`
	Sources           []Source             `json:"sources"`
	State             string               `json:"state"`
	Stocks            ProductStocks        `json:"stocks"`
	Errors            []string             `json:"errors"`
	Vat               string               `json:"vat"`
	Visible           bool                 `json:"visible"`
	VisibilityDetails ProductVisibility    `json:"visibility_details"`
	PriceIndex        string               `json:"price_index"`
	Images360         []string             `json:"images360"`
	ColorImage        string               `json:"color_image"`
	PrimaryImage      string               `json:"primary_image"`
	Status            ProductStatus        `json:"status"`
	Attributes        []ProductAttribute   `json:"attributes"`
	ComplexAttributes []ProductComplexAttr `json:"complex_attributes"`
	Dimensions        ProductDimensions    `json:"dimensions"`
}

// ProductImage 商品图片
type ProductImage struct {
	FileName string `json:"file_name"`
	Default  bool   `json:"default"`
	Index    int32  `json:"index"`
}

// ProductStocks 商品库存
type ProductStocks struct {
	Coming   int32 `json:"coming"`
	Present  int32 `json:"present"`
	Reserved int32 `json:"reserved"`
}

// ProductVisibility 商品可见性详情
type ProductVisibility struct {
	HasPrice      bool `json:"has_price"`
	HasStock      bool `json:"has_stock"`
	ActiveProduct bool `json:"active_product"`
}

// ProductStatus 商品状态
type ProductStatus struct {
	State            string   `json:"state"`
	StateFailed      string   `json:"state_failed"`
	ModerationStatus string   `json:"moderation_status"`
	DeclineReasons   []string `json:"decline_reasons"`
	ValidationState  string   `json:"validation_state"`
	StateDescription string   `json:"state_description"`
	IsFailed         bool     `json:"is_failed"`
	IsCreated        bool     `json:"is_created"`
	StateTooltip     string   `json:"state_tooltip"`
	ItemErrors       []struct {
		Code        string `json:"code"`
		Field       string `json:"field"`
		Description string `json:"description"`
	} `json:"item_errors"`
	StateUpdatedAt time.Time `json:"state_updated_at"`
}

// ProductAttribute 商品属性
type ProductAttribute struct {
	AttributeID int64       `json:"attribute_id"`
	ComplexID   int64       `json:"complex_id"`
	Values      []AttrValue `json:"values"`
}

// AttrValue 属性值
type AttrValue struct {
	DictionaryValueID int64  `json:"dictionary_value_id"`
	Value             string `json:"value"`
}

// ProductComplexAttr 商品复合属性
type ProductComplexAttr struct {
	Attributes []ProductAttribute `json:"attributes"`
}

// ProductDimensions 商品尺寸
type ProductDimensions struct {
	Height string `json:"height"`
	Length string `json:"length"`
	Weight string `json:"weight"`
	Width  string `json:"width"`
}

// ProductCreateRequest 创建商品请求
type ProductCreateRequest struct {
	Items []ProductCreateItem `json:"items"`
}

// ProductCreateItem 创建商品项
type ProductCreateItem struct {
	Attributes        []ProductAttribute   `json:"attributes"`
	Barcode           string               `json:"barcode"`
	CategoryID        int64                `json:"category_id"`
	ColorImage        string               `json:"color_image,omitempty"`
	ComplexAttributes []ProductComplexAttr `json:"complex_attributes,omitempty"`
	CurrencyCode      string               `json:"currency_code"`
	Depth             int32                `json:"depth"`
	DimensionUnit     string               `json:"dimension_unit"`
	Height            int32                `json:"height"`
	Images            []string             `json:"images"`
	Images360         []string             `json:"images360,omitempty"`
	Name              string               `json:"name"`
	OfferID           string               `json:"offer_id"`
	OldPrice          string               `json:"old_price,omitempty"`
	PremiumPrice      string               `json:"premium_price,omitempty"`
	Price             string               `json:"price"`
	PrimaryImage      string               `json:"primary_image,omitempty"`
	Vat               string               `json:"vat"`
	Weight            int32                `json:"weight"`
	WeightUnit        string               `json:"weight_unit"`
	Width             int32                `json:"width"`
}

// ProductCreateResponse 创建商品响应
type ProductCreateResponse struct {
	Result struct {
		TaskID int64 `json:"task_id"`
	} `json:"result"`
}

// ProductImportInfoRequest 获取商品导入信息请求
type ProductImportInfoRequest struct {
	TaskID int64 `json:"task_id"`
}

// ProductImportInfoResponse 获取商品导入信息响应
type ProductImportInfoResponse struct {
	Result struct {
		Items []ProductImportItem `json:"items"`
		Total int32               `json:"total"`
	} `json:"result"`
}

// ProductImportItem 商品导入项
type ProductImportItem struct {
	OfferID   string   `json:"offer_id"`
	ProductID int64    `json:"product_id"`
	Status    string   `json:"status"`
	Errors    []string `json:"errors"`
}

// ProductUpdateRequest 更新商品请求
type ProductUpdateRequest struct {
	Items []ProductUpdateItem `json:"items"`
}

// ProductUpdateItem 更新商品项
type ProductUpdateItem struct {
	Attributes        []ProductAttribute   `json:"attributes,omitempty"`
	Barcode           string               `json:"barcode,omitempty"`
	CategoryID        int64                `json:"category_id,omitempty"`
	ColorImage        string               `json:"color_image,omitempty"`
	ComplexAttributes []ProductComplexAttr `json:"complex_attributes,omitempty"`
	CurrencyCode      string               `json:"currency_code,omitempty"`
	Depth             int32                `json:"depth,omitempty"`
	DimensionUnit     string               `json:"dimension_unit,omitempty"`
	Height            int32                `json:"height,omitempty"`
	Images            []string             `json:"images,omitempty"`
	Images360         []string             `json:"images360,omitempty"`
	Name              string               `json:"name,omitempty"`
	OfferID           string               `json:"offer_id"`
	OldPrice          string               `json:"old_price,omitempty"`
	PremiumPrice      string               `json:"premium_price,omitempty"`
	Price             string               `json:"price,omitempty"`
	PrimaryImage      string               `json:"primary_image,omitempty"`
	ProductID         int64                `json:"product_id,omitempty"`
	Vat               string               `json:"vat,omitempty"`
	Weight            int32                `json:"weight,omitempty"`
	WeightUnit        string               `json:"weight_unit,omitempty"`
	Width             int32                `json:"width,omitempty"`
}

// ProductUpdateResponse 更新商品响应
type ProductUpdateResponse struct {
	Result struct {
		TaskID int64 `json:"task_id"`
	} `json:"result"`
}

// ProductStocksRequest 更新库存请求
type ProductStocksRequest struct {
	Stocks []ProductStock `json:"stocks"`
}

// ProductStock 商品库存
type ProductStock struct {
	OfferID     string `json:"offer_id,omitempty"`
	ProductID   int64  `json:"product_id,omitempty"`
	Stock       int32  `json:"stock"`
	WarehouseID int64  `json:"warehouse_id"`
}

// ProductStocksResponse 更新库存响应
type ProductStocksResponse struct {
	Result []ProductStockResult `json:"result"`
}

// ProductStockResult 库存更新结果
type ProductStockResult struct {
	Errors    []string `json:"errors"`
	OfferID   string   `json:"offer_id"`
	ProductID int64    `json:"product_id"`
	Updated   bool     `json:"updated"`
}

// ProductPricesRequest 更新价格请求
type ProductPricesRequest struct {
	Prices []ProductPrice `json:"prices"`
}

// ProductPrice 商品价格
type ProductPrice struct {
	AutoActionEnabled    string `json:"auto_action_enabled,omitempty"` // UNKNOWN, ENABLED, DISABLED
	CurrencyCode         string `json:"currency_code,omitempty"`
	MinPrice             string `json:"min_price,omitempty"`
	OfferID              string `json:"offer_id,omitempty"`
	OldPrice             string `json:"old_price,omitempty"`
	Price                string `json:"price"`
	PriceStrategyEnabled string `json:"price_strategy_enabled,omitempty"` // UNKNOWN, ENABLED, DISABLED
	ProductID            int64  `json:"product_id,omitempty"`
}

// ProductPricesResponse 更新价格响应
type ProductPricesResponse struct {
	Result []ProductPriceResult `json:"result"`
}

// ProductPriceResult 价格更新结果
type ProductPriceResult struct {
	Errors    []string `json:"errors"`
	OfferID   string   `json:"offer_id"`
	ProductID int64    `json:"product_id"`
	Updated   bool     `json:"updated"`
}

// ProductPicturesRequest 导入商品图片请求
type ProductPicturesRequest struct {
	ProductID int64    `json:"product_id"`
	Images    []string `json:"images"`
}

// ProductPicturesResponse 导入商品图片响应
type ProductPicturesResponse struct {
	Result []ProductPictureResult `json:"result"`
}

// ProductPictureResult 图片导入结果
type ProductPictureResult struct {
	ProductID int64    `json:"product_id"`
	Errors    []string `json:"errors"`
	Updated   bool     `json:"updated"`
}

// ProductRatingRequest 获取商品评分请求
type ProductRatingRequest struct {
	SKUs []int64 `json:"skus"`
}

// ProductRatingResponse 获取商品评分响应
type ProductRatingResponse struct {
	Result []ProductRating `json:"result"`
}

// ProductRating 商品评分
type ProductRating struct {
	SKU    int64   `json:"sku"`
	Rating float64 `json:"rating"`
	Count  int32   `json:"count"`
}

// ProductAttributesRequest 获取商品属性请求
type ProductAttributesRequest struct {
	Filter ProductAttributesFilter `json:"filter"`
	Limit  int32                   `json:"limit,omitempty"`
	LastID string                  `json:"last_id,omitempty"`
}

// ProductAttributesFilter 商品属性过滤器
type ProductAttributesFilter struct {
	OfferID    []string `json:"offer_id,omitempty"`
	ProductID  []int64  `json:"product_id,omitempty"`
	Visibility string   `json:"visibility,omitempty"`
}

// ProductAttributesResponse 获取商品属性响应
type ProductAttributesResponse struct {
	Result []ProductAttributeInfo `json:"result"`
	LastID string                 `json:"last_id"`
}

// ProductAttributeInfo 商品属性信息
type ProductAttributeInfo struct {
	ID                int64                `json:"id"`
	OfferID           string               `json:"offer_id"`
	Barcode           string               `json:"barcode"`
	Name              string               `json:"name"`
	CategoryID        int64                `json:"category_id"`
	TypeID            int64                `json:"type_id"`
	Attributes        []ProductAttribute   `json:"attributes"`
	ComplexAttributes []ProductComplexAttr `json:"complex_attributes"`
}

// ProductImportBySKURequest 通过SKU导入商品请求
type ProductImportBySKURequest struct {
	Items []ProductImportBySKUItem `json:"items"`
}

// ProductImportBySKUItem 通过SKU导入商品项
type ProductImportBySKUItem struct {
	SKU               int64                `json:"sku"`
	Name              string               `json:"name"`
	OfferID           string               `json:"offer_id"`
	CurrencyCode      string               `json:"currency_code"`
	OldPrice          string               `json:"old_price,omitempty"`
	PremiumPrice      string               `json:"premium_price,omitempty"`
	Price             string               `json:"price"`
	Vat               string               `json:"vat"`
	Attributes        []ProductAttribute   `json:"attributes,omitempty"`
	ComplexAttributes []ProductComplexAttr `json:"complex_attributes,omitempty"`
}

// ProductImportBySKUResponse 通过SKU导入商品响应
type ProductImportBySKUResponse struct {
	Result struct {
		TaskID int64 `json:"task_id"`
	} `json:"result"`
}

// ProductCertificateRequest 获取商品证书请求
type ProductCertificateRequest struct {
	CertificateID []int64 `json:"certificate_id,omitempty"`
	ProductID     []int64 `json:"product_id,omitempty"`
	Page          int32   `json:"page,omitempty"`
	PageSize      int32   `json:"page_size,omitempty"`
}

// ProductCertificateResponse 获取商品证书响应
type ProductCertificateResponse struct {
	Result []ProductCertificate `json:"result"`
}

// ProductCertificate 商品证书
type ProductCertificate struct {
	ID         int64    `json:"id"`
	Name       string   `json:"name"`
	Type       string   `json:"type"`
	Status     string   `json:"status"`
	Number     string   `json:"number"`
	IssueDate  string   `json:"issue_date"`
	ExpireDate string   `json:"expire_date"`
	ProductIDs []int64  `json:"product_ids"`
	Documents  []string `json:"documents"`
}

// CertificateTypesResponse 获取证书类型响应
type CertificateTypesResponse struct {
	Result []CertificateType `json:"result"`
}

// CertificateType 证书类型
type CertificateType struct {
	ID          int64   `json:"id"`
	Name        string  `json:"name"`
	IsRequired  bool    `json:"is_required"`
	CategoryIDs []int64 `json:"category_ids"`
}
