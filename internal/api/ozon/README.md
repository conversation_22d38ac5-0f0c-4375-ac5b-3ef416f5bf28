# Ozon API 客户端

这是一个完整的 Ozon Seller API Go 客户端，提供了对 Ozon 平台各种服务的访问。

## 功能特性

### 🏷️ 分类服务 (CategoryService)
- 获取分类树和分类属性
- 品牌和认证管理
- 仓库和配送方式查询
- 国家和货币信息

### 🛍️ 商品服务 (ProductService)
- 获取商品列表和详细信息
- 创建、更新和删除商品
- 管理商品库存和价格
- 商品归档和取消归档
- 商品图片和证书管理
- 商品评分和属性查询

### 📦 订单服务 (OrderService)
- 获取订单列表和详情（FBS/FBO）
- 订单发货和取消
- 生成包裹标签和交接单
- 订单状态管理
- 退货和取消申请处理
- 客户聊天管理

### 📊 分析服务 (AnalyticsService)
- 获取销售和库存分析数据
- 商品周转率和需求预测
- 财务报告和交易记录
- 自定义报告生成

### 🏭 FBO服务 (FBOService)
- 仓库和集群管理
- 供应草稿和供应单
- 货物单位设置
- 标签生成

## 快速开始

### 安装依赖

```bash
go mod tidy
```

### 基本用法

```go
package main

import (
    "fmt"
    "log"
    "time"
    
    ozonapi "your-project/internal/api/ozon"
)

func main() {
    // 创建客户端
    client := ozonapi.NewClient("your-api-key", "your-client-id")
    
    // 使用商品服务
    products, err := client.Products.GetProductList(&ozonapi.ProductListRequest{
        Limit: 10,
        Filter: &ozonapi.ProductFilter{
            Visibility: "ALL",
        },
    })
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("找到 %d 个商品\n", len(products.Result.Items))
}
```

## 详细使用示例

### 分类管理

```go
// 获取所有分类
categories, err := client.Categories.GetAllCategories()

// 获取分类属性
attributes, err := client.Categories.GetAttributesByCategory(categoryID, typeID)

// 搜索属性值
values, err := client.Categories.SearchAttributeValues(&ozonapi.SearchAttributeValuesRequest{
    AttributeID: attributeID,
    CategoryID:  categoryID,
    TypeID:      typeID,
    Value:       "搜索关键词",
    Language:    "RU",
    Limit:       50,
})

// 获取品牌列表
brands, err := client.Categories.GetBrands(&ozonapi.BrandRequest{
    Language: "RU",
})

// 获取仓库列表
warehouses, err := client.Categories.GetAllWarehouses()

// 获取配送方式
deliveryMethods, err := client.Categories.GetAllDeliveryMethods()
```

### 商品管理

```go
// 获取商品列表
products, err := client.Products.GetProductList(&ozonapi.ProductListRequest{
    Limit: 100,
    Filter: &ozonapi.ProductFilter{
        Visibility: "VISIBLE",
    },
})

// 获取商品详细信息
productInfo, err := client.Products.GetProductInfo(&ozonapi.ProductInfoRequest{
    OfferID: "your-offer-id",
})

// 更新商品价格
priceResult, err := client.Products.UpdateProductPrices(&ozonapi.ProductPricesRequest{
    Prices: []ozonapi.ProductPrice{
        {
            OfferID: "your-offer-id",
            Price:   "1000",
        },
    },
})

// 更新商品库存
stockResult, err := client.Products.UpdateProductStocks(&ozonapi.ProductStocksRequest{
    Stocks: []ozonapi.ProductStock{
        {
            OfferID:     "your-offer-id",
            Stock:       100,
            WarehouseID: 123456,
        },
    },
})

// 导入商品图片
pictureResult, err := client.Products.ImportProductPictures(&ozonapi.ProductPicturesRequest{
    ProductID: 123456,
    Images:    []string{"https://example.com/image1.jpg", "https://example.com/image2.jpg"},
})

// 获取商品评分
rating, err := client.Products.GetProductRating(&ozonapi.ProductRatingRequest{
    SKUs: []int64{123456, 789012},
})

// 获取商品属性
attributes, err := client.Products.GetProductAttributes(&ozonapi.ProductAttributesRequest{
    Filter: ozonapi.ProductAttributesFilter{
        OfferID:    []string{"your-offer-id"},
        Visibility: "ALL",
    },
    Limit: 100,
})
```

### 订单管理

```go
// 获取订单列表
orders, err := client.Orders.GetOrderList(&ozonapi.OrderListRequest{
    Dir:    "DESC",
    Limit:  50,
    Offset: 0,
    Filter: ozonapi.OrderFilter{
        Since:  time.Now().AddDate(0, 0, -30), // 最近30天
        To:     time.Now(),
        Status: "awaiting_packaging",
    },
    With: ozonapi.OrderWith{
        AnalyticsData: true,
        FinancialData: true,
    },
})

// 获取订单详情
order, err := client.Orders.GetOrder("posting-number", true, true)

// 发货
shipResult, err := client.Orders.ShipOrder(&ozonapi.OrderShipRequest{
    PostingNumber: "posting-number",
    Packages: []ozonapi.OrderPackage{
        {
            Products: []ozonapi.OrderShipProduct{
                {
                    ProductID: 123456,
                    Quantity:  1,
                },
            },
        },
    },
})

// 取消订单
cancelResult, err := client.Orders.CancelOrder(&ozonapi.OrderCancelRequest{
    PostingNumber:       "posting-number",
    CancelReasonID:      1,
    CancelReasonMessage: "商品缺货",
    Items: []ozonapi.OrderCancelItem{
        {
            SKU:      123456,
            Quantity: 1,
        },
    },
})

// 获取FBO订单
fboOrders, err := client.Orders.GetFBOOrderList(&ozonapi.OrderListRequest{
    Dir:    "DESC",
    Limit:  50,
    Filter: ozonapi.OrderFilter{
        Since: time.Now().AddDate(0, 0, -7),
        To:    time.Now(),
    },
})

// 获取退货信息
returns, err := client.Orders.GetReturns(&ozonapi.ReturnRequest{
    Filter: ozonapi.ReturnFilter{
        Since:  time.Now().AddDate(0, 0, -30),
        To:     time.Now(),
        Status: "returned",
    },
    Limit: 100,
})

// 获取聊天列表
chats, err := client.Orders.GetChatList(&ozonapi.ChatListRequest{
    Filter: ozonapi.ChatListFilter{
        Since: time.Now().AddDate(0, 0, -7),
        To:    time.Now(),
    },
    Limit: 50,
})

// 发送聊天消息
messageResp, err := client.Orders.SendChatMessage(&ozonapi.ChatSendMessageRequest{
    ChatID: "chat-id",
    Text:   "您好，关于您的订单...",
})

// 获取取消申请列表
cancellations, err := client.Orders.GetCancellationList(&ozonapi.CancellationListRequest{
    Filter: ozonapi.CancellationFilter{
        Since: time.Now().AddDate(0, 0, -7),
        To:    time.Now(),
    },
    Limit: 50,
})

// 获取取消原因
reasons, err := client.Orders.GetCancellationReasons()
```

### 分析数据

```go
// 获取库存分析
stockData, err := client.Analytics.GetStockOnWarehouses(&ozonapi.StockOnWarehousesRequest{
    Limit:         1000,
    WarehouseType: "ALL",
})

// 获取商品周转率
turnover, err := client.Analytics.GetItemTurnover(&ozonapi.ItemTurnoverRequest{
    DateFrom: time.Now().AddDate(0, -1, 0), // 一个月前
    DateTo:   time.Now(),
    Limit:    100,
})

// 获取财务交易记录
transactions, err := client.Analytics.GetFinanceTransactionList(&ozonapi.FinanceTransactionListRequest{
    Filter: ozonapi.FinanceTransactionFilter{
        Date: ozonapi.FinanceDateFilter{
            From: time.Now().AddDate(0, 0, -7), // 最近7天
            To:   time.Now(),
        },
    },
    Page:     1,
    PageSize: 100,
})

// 创建自定义报告
reportResp, err := client.Analytics.CreateReport(&ozonapi.ReportCreateRequest{
    ReportType: "seller_products",
    Language:   "RU",
})
```

### FBO 供应管理

```go
// 获取集群和仓库信息
clusters, err := client.FBO.GetClusterAndWarehouseList(&ozonapi.ClusterListRequest{
    ClusterType: "CLUSTER_TYPE_OZON",
})

// 创建供应草稿
draftID, err := client.FBO.CreateSupplyDraft(&ozonapi.DraftCreateRequest{
    ClusterIDs: []int64{123, 456},
    Items: []struct {
        Quantity int    `json:"quantity"`
        SKU      string `json:"sku"`
    }{
        {Quantity: 10, SKU: "your-sku"},
    },
    Type: "CREATE_TYPE_DIRECT",
})

// 从草稿创建供应单
supplyID, err := client.FBO.CreateSupplyFromDraft(&ozonapi.DraftSupplyCreateRequest{
    DraftID:     123456,
    WarehouseID: 789012,
})
```

## 配置选项

```go
// 自定义超时时间
client := ozonapi.NewClient(
    "your-api-key", 
    "your-client-id",
    ozonapi.WithTimeout(30*time.Second),
)
```

## 错误处理

所有API方法都返回错误，建议进行适当的错误处理：

```go
products, err := client.Products.GetProductList(req)
if err != nil {
    log.Printf("获取商品列表失败: %v", err)
    return
}

// 检查API响应中的业务错误
for _, item := range products.Result.Items {
    if len(item.Errors) > 0 {
        log.Printf("商品 %s 有错误: %v", item.OfferID, item.Errors)
    }
}
```

## 注意事项

1. **API限制**: 请遵守Ozon API的调用频率限制
2. **认证**: 确保API密钥和客户端ID的安全性
3. **代理**: 代码中包含代理设置，请根据实际情况调整
4. **错误处理**: 始终检查返回的错误和响应中的业务错误
5. **数据验证**: 在发送请求前验证必要的参数

## 支持的API版本

- 商品API: v1, v2
- 订单API: v2, v3
- 分析API: v1, v3
- FBO API: v1

## 贡献

欢迎提交Issue和Pull Request来改进这个客户端。
