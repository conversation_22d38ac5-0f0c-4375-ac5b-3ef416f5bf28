package ozonapi

import (
	"testing"
	"time"
)

func TestNewClient(t *testing.T) {
	// 测试创建客户端
	client := NewClient("test-api-key", "test-client-id")
	
	if client == nil {
		t.Fatal("客户端创建失败")
	}
	
	// 验证所有服务都已初始化
	if client.Categories == nil {
		t.<PERSON>rror("分类服务未初始化")
	}

	if client.Products == nil {
		t.Error("商品服务未初始化")
	}

	if client.Orders == nil {
		t.Error("订单服务未初始化")
	}

	if client.Analytics == nil {
		t.Error("分析服务未初始化")
	}

	if client.FBO == nil {
		t.Error("FBO服务未初始化")
	}
	
	// 验证HTTP客户端配置
	httpClient := client.GetHTTPClient()
	if httpClient == nil {
		t.Error("HTTP客户端未初始化")
	}
	
	// 验证基础URL
	if httpClient.BaseURL != "https://api-seller.ozon.ru" {
		t.<PERSON><PERSON><PERSON>("基础URL配置错误，期望: https://api-seller.ozon.ru, 实际: %s", httpClient.BaseURL)
	}
}

func TestClientWithOptions(t *testing.T) {
	// 测试带选项的客户端创建
	timeout := 30 * time.Second
	client := NewClient("test-api-key", "test-client-id", WithTimeout(timeout))
	
	if client == nil {
		t.Fatal("客户端创建失败")
	}
	
	// 注意：由于resty客户端的timeout字段不是公开的，我们无法直接验证
	// 但可以确保客户端创建成功且没有panic
}

func TestServiceInitialization(t *testing.T) {
	client := NewClient("test-api-key", "test-client-id")
	
	// 测试商品服务
	if client.Products == nil {
		t.Error("商品服务未初始化")
	} else {
		// 验证服务的HTTP客户端是否正确设置
		if client.Products.client == nil {
			t.Error("商品服务的HTTP客户端未设置")
		}
	}
	
	// 测试订单服务
	if client.Orders == nil {
		t.Error("订单服务未初始化")
	} else {
		if client.Orders.client == nil {
			t.Error("订单服务的HTTP客户端未设置")
		}
	}
	
	// 测试分析服务
	if client.Analytics == nil {
		t.Error("分析服务未初始化")
	} else {
		if client.Analytics.client == nil {
			t.Error("分析服务的HTTP客户端未设置")
		}
	}
	
	// 测试FBO服务
	if client.FBO == nil {
		t.Error("FBO服务未初始化")
	} else {
		if client.FBO.client == nil {
			t.Error("FBO服务的HTTP客户端未设置")
		}
	}
}

// 示例：如何使用各个服务
func ExampleUsage() {
	// 创建客户端
	client := NewClient("your-api-key", "your-client-id")
	
	// 使用商品服务
	productList, err := client.Products.GetProductList(&ProductListRequest{
		Limit: 10,
		Filter: &ProductFilter{
			Visibility: "ALL",
		},
	})
	if err != nil {
		// 处理错误
		return
	}
	_ = productList
	
	// 使用订单服务
	orderList, err := client.Orders.GetOrderList(&OrderListRequest{
		Dir:    "DESC",
		Limit:  10,
		Offset: 0,
		Filter: OrderFilter{
			Since: time.Now().AddDate(0, 0, -7), // 最近7天
			To:    time.Now(),
		},
		With: OrderWith{
			AnalyticsData: true,
			FinancialData: true,
		},
	})
	if err != nil {
		// 处理错误
		return
	}
	_ = orderList
	
	// 使用分析服务
	stockData, err := client.Analytics.GetStockOnWarehouses(&StockOnWarehousesRequest{
		Limit:         100,
		WarehouseType: "ALL",
	})
	if err != nil {
		// 处理错误
		return
	}
	_ = stockData
	
	// 使用FBO服务
	clusterList, err := client.FBO.GetClusterAndWarehouseList(&ClusterListRequest{
		ClusterType: "CLUSTER_TYPE_OZON",
	})
	if err != nil {
		// 处理错误
		return
	}
	_ = clusterList
}
