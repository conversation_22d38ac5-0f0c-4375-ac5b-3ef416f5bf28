package ozonapi

import (
	"fmt"
	"log"
	"time"
)

// ExampleUsageDemo 演示如何使用Ozon API客户端的各种服务
func ExampleUsageDemo() {
	// 创建客户端
	client := NewClient("your-api-key", "your-client-id")

	// 1. 分类服务示例
	fmt.Println("=== 分类服务示例 ===")
	
	// 获取所有分类
	categories, err := client.Categories.GetAllCategories()
	if err != nil {
		log.Printf("获取分类失败: %v", err)
	} else {
		fmt.Printf("获取到 %d 个分类\n", len(categories.Result))
	}

	// 获取品牌列表
	brands, err := client.Categories.GetBrands(&BrandRequest{
		Language: "RU",
	})
	if err != nil {
		log.Printf("获取品牌失败: %v", err)
	} else {
		fmt.Printf("获取到 %d 个品牌\n", len(brands.Result))
	}

	// 获取仓库列表
	warehouses, err := client.Categories.GetAllWarehouses()
	if err != nil {
		log.Printf("获取仓库失败: %v", err)
	} else {
		fmt.Printf("获取到 %d 个仓库\n", len(warehouses.Result))
	}

	// 2. 商品服务示例
	fmt.Println("\n=== 商品服务示例 ===")
	
	// 获取商品列表
	products, err := client.Products.GetProductList(&ProductListRequest{
		Limit: 10,
		Filter: &ProductFilter{
			Visibility: "ALL",
		},
	})
	if err != nil {
		log.Printf("获取商品列表失败: %v", err)
	} else {
		fmt.Printf("获取到 %d 个商品\n", len(products.Result.Items))
		
		// 遍历商品并获取详细信息
		for _, item := range products.Result.Items {
			productInfo, err := client.Products.GetProductInfo(&ProductInfoRequest{
				OfferID: item.OfferID,
			})
			if err != nil {
				log.Printf("获取商品 %s 详情失败: %v", item.OfferID, err)
				continue
			}
			fmt.Printf("商品: %s, 价格: %s, 库存: %d\n", 
				productInfo.Result.Name, 
				item.Price, 
				item.Stocks.Present)
		}
	}

	// 更新商品价格
	priceResult, err := client.Products.UpdateProductPrices(&ProductPricesRequest{
		Prices: []ProductPrice{
			{
				OfferID: "example-offer-id",
				Price:   "1500",
			},
		},
	})
	if err != nil {
		log.Printf("更新价格失败: %v", err)
	} else {
		for _, result := range priceResult.Result {
			if result.Updated {
				fmt.Printf("商品 %s 价格更新成功\n", result.OfferID)
			} else {
				fmt.Printf("商品 %s 价格更新失败: %v\n", result.OfferID, result.Errors)
			}
		}
	}

	// 3. 订单服务示例
	fmt.Println("\n=== 订单服务示例 ===")
	
	// 获取最近7天的订单
	orders, err := client.Orders.GetOrderList(&OrderListRequest{
		Dir:    "DESC",
		Limit:  20,
		Offset: 0,
		Filter: OrderFilter{
			Since: time.Now().AddDate(0, 0, -7),
			To:    time.Now(),
		},
		With: OrderWith{
			AnalyticsData: true,
			FinancialData: true,
		},
	})
	if err != nil {
		log.Printf("获取订单列表失败: %v", err)
	} else {
		fmt.Printf("获取到 %d 个订单\n", len(orders.Result))
		
		// 遍历订单并显示基本信息
		for _, order := range orders.Result {
			fmt.Printf("订单: %s, 状态: %s, 商品数量: %d\n", 
				order.OrderNumber, 
				order.Status, 
				len(order.Products))
		}
	}

	// 获取待发货订单
	awaitingOrders, err := client.Orders.GetAwaitingDeliverOrders(10, 0)
	if err != nil {
		log.Printf("获取待发货订单失败: %v", err)
	} else {
		fmt.Printf("待发货订单数量: %d\n", len(awaitingOrders.Result))
	}

	// 4. 分析服务示例
	fmt.Println("\n=== 分析服务示例 ===")
	
	// 获取库存分析
	stockData, err := client.Analytics.GetStockOnWarehouses(&StockOnWarehousesRequest{
		Limit:         100,
		WarehouseType: "ALL",
	})
	if err != nil {
		log.Printf("获取库存分析失败: %v", err)
	} else {
		fmt.Printf("获取到 %d 个库存记录\n", len(stockData.Result.Rows))
		
		// 显示库存统计
		totalStock := int32(0)
		for _, item := range stockData.Result.Rows {
			totalStock += item.FreeToSellAmount
		}
		fmt.Printf("总可售库存: %d\n", totalStock)
	}

	// 获取商品周转率
	turnover, err := client.Analytics.GetItemTurnover(&ItemTurnoverRequest{
		DateFrom: time.Now().AddDate(0, -1, 0), // 一个月前
		DateTo:   time.Now(),
		Limit:    50,
	})
	if err != nil {
		log.Printf("获取周转率失败: %v", err)
	} else {
		fmt.Printf("获取到 %d 个商品的周转率数据\n", len(turnover.Result.Turnovers))
	}

	// 获取财务交易记录
	transactions, err := client.Analytics.GetFinanceTransactionList(&FinanceTransactionListRequest{
		Filter: FinanceTransactionFilter{
			Date: FinanceDateFilter{
				From: time.Now().AddDate(0, 0, -7),
				To:   time.Now(),
			},
		},
		Page:     1,
		PageSize: 50,
	})
	if err != nil {
		log.Printf("获取财务交易失败: %v", err)
	} else {
		fmt.Printf("获取到 %d 个财务交易记录\n", len(transactions.Result.Operations))
	}

	// 5. FBO服务示例
	fmt.Println("\n=== FBO服务示例 ===")
	
	// 获取集群和仓库信息
	clusters, err := client.FBO.GetClusterAndWarehouseList(&ClusterListRequest{
		ClusterType: "CLUSTER_TYPE_OZON",
	})
	if err != nil {
		log.Printf("获取集群信息失败: %v", err)
	} else {
		fmt.Printf("获取到 %d 个集群\n", len(clusters.Result))
	}

	// 获取供应草稿列表
	drafts, err := client.FBO.GetSupplyDraftList(&DraftListRequest{
		Limit: 20,
	})
	if err != nil {
		log.Printf("获取供应草稿失败: %v", err)
	} else {
		fmt.Printf("获取到 %d 个供应草稿\n", len(drafts.Result.Items))
	}

	fmt.Println("\n=== 示例完成 ===")
}

// ExampleErrorHandling 演示错误处理的最佳实践
func ExampleErrorHandling() {
	client := NewClient("your-api-key", "your-client-id")

	// 获取商品列表并处理各种错误
	products, err := client.Products.GetProductList(&ProductListRequest{
		Limit: 10,
		Filter: &ProductFilter{
			Visibility: "ALL",
		},
	})

	if err != nil {
		// 网络错误或API错误
		log.Printf("API调用失败: %v", err)
		return
	}

	// 检查业务逻辑错误
	for _, item := range products.Result.Items {
		if len(item.Errors) > 0 {
			log.Printf("商品 %s 有错误: %v", item.OfferID, item.Errors)
		}
	}

	fmt.Printf("成功获取 %d 个商品\n", len(products.Result.Items))
}

// ExampleBatchOperations 演示批量操作
func ExampleBatchOperations() {
	client := NewClient("your-api-key", "your-client-id")

	// 批量更新商品价格
	var prices []ProductPrice
	offerIDs := []string{"offer1", "offer2", "offer3"}
	
	for _, offerID := range offerIDs {
		prices = append(prices, ProductPrice{
			OfferID: offerID,
			Price:   "1000",
		})
	}

	result, err := client.Products.UpdateProductPrices(&ProductPricesRequest{
		Prices: prices,
	})

	if err != nil {
		log.Printf("批量更新价格失败: %v", err)
		return
	}

	// 检查每个商品的更新结果
	successCount := 0
	for _, item := range result.Result {
		if item.Updated {
			successCount++
		} else {
			log.Printf("商品 %s 更新失败: %v", item.OfferID, item.Errors)
		}
	}

	fmt.Printf("成功更新 %d/%d 个商品价格\n", successCount, len(prices))
}

// ExamplePagination 演示分页处理
func ExamplePagination() {
	client := NewClient("your-api-key", "your-client-id")

	var allProducts []ProductItem
	lastID := ""
	limit := int32(100)

	for {
		products, err := client.Products.GetProductList(&ProductListRequest{
			Limit:  limit,
			LastID: lastID,
			Filter: &ProductFilter{
				Visibility: "ALL",
			},
		})

		if err != nil {
			log.Printf("获取商品列表失败: %v", err)
			break
		}

		allProducts = append(allProducts, products.Result.Items...)
		
		// 检查是否还有更多数据
		if len(products.Result.Items) < int(limit) || products.Result.LastID == "" {
			break
		}

		lastID = products.Result.LastID
		fmt.Printf("已获取 %d 个商品，继续获取...\n", len(allProducts))
	}

	fmt.Printf("总共获取到 %d 个商品\n", len(allProducts))
}
