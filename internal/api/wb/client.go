package wbapi

import (
	"time"

	"github.com/go-resty/resty/v2"
)

const (
	defaultTimeout = 10 * time.Second
)

// Client WB API 客户端
type Client struct {
	baseClient *resty.Client // HTTP 请求客户端
	// API 服务
	Promotion *PromotionService    // 推广服务
	Cards     *ProductCardsService // 商品卡片服务
	// 这里可以添加其他服务模块
	// Content  *ContentService  // 内容服务
	// Order    *OrderService   // 订单服务
	// 等等...
}

// ClientOption 客户端配置选项
type ClientOption func(*Client)

// WithTimeout 设置超时时间
func WithTimeout(timeout time.Duration) ClientOption {
	return func(c *Client) {
		c.baseClient.SetTimeout(timeout)
	}
}

// NewClient 创建新的 WB API 客户端
func NewClient(apiKey string, opts ...ClientOption) *Client {
	rc := resty.New()
	rc.SetTimeout(defaultTimeout)
	rc.SetHeader("Authorization", apiKey)
	rc.SetHeader("Accept", "application/json")
	rc.SetHeader("Content-Type", "application/json")
	rc.SetProxy("socks5://lens:ls3903850@185.22.152.62:23481")

	c := &Client{
		baseClient: rc,
	}

	// 应用自定义选项
	for _, opt := range opts {
		opt(c)
	}

	// 初始化各个服务
	c.Promotion = newPromotionService(c)
	c.Cards = newProductCardsService(c)
	// 这里可以初始化其他服务
	// c.Content = newContentService(c)
	// c.Order = newOrderService(c)

	return c
}
