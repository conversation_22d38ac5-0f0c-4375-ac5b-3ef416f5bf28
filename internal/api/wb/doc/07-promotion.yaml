openapi: 3.0.1
info:
  title: Marketing and Promotions
  version: promotion
  description: |
    Campaign Management, bid settings, financial data accounting, and settings for automatic and media campaigns.

    Data synchronization from the database occurs every 3 minutes. Status changes occur every 1 minute. The bid change occurs every 30 seconds. The latest changes are saved within the intervals
  x-file-name: promotion
security:
  - HeaderApiKey: []
tags:
  - name: Campaigns
    description: ''
  - name: Campaigns Creation
    description: ''
  - name: Campaigns Management
    description: ''
  - name: Finances
    description: ''
  - name: Campaigns Parameters
    description: ''
  - name: Media
    description: ''
  - name: Promotions Calendar
    description: ''
paths:
  /adv/v1/promotion/count:
    servers:
      - url: https://advert-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Campaigns lists
      description: |
        Method allows to get campaigns lists grouped by type and status with information about last campaign change date.

        <div class="description_limit">  
          Maximum of 5 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Campaigns
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  adverts:
                    type: array
                    nullable: true
                    description: Campaign data
                    items:
                      type: object
                      properties:
                        type:
                          description: Campaign type
                          type: integer
                        status:
                          description: Campaign status
                          type: integer
                        count:
                          description: Campaigns number
                          type: integer
                        advert_list:
                          description: Campaigns list
                          type: array
                          items:
                            type: object
                            properties:
                              advertId:
                                description: Campaign ID
                                type: integer
                              changeTime:
                                description: Date and time of the last campaign change
                                type: string
                                format: date-time
                  all:
                    description: Total number of campaigns with all statuses and types
                    type: integer
              example:
                adverts:
                  - type: 4
                    status: 8
                    count: 3
                    advert_list:
                      - advertId: 6485174
                        changeTime: '2023-05-10T12:12:52.676254+03:00'
                      - advertId: 6500443
                        changeTime: '2023-05-10T17:08:46.370656+03:00'
                      - advertId: 7936341
                        changeTime: '2023-07-12T15:51:08.367478+03:00'
                all: 3
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v0/config:
    servers:
      - url: https://advert-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Promotional configuration values
      description: |
        The method provides acceptable values for the main configuration parameters of campaigns  

        <div class="description_limit">  
          Maximum of 1 request per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account
        </div>
      tags:
        - Campaigns Creation
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  categories:
                    type: array
                    description: List of product categories (subjects) with minimum allowable rates
                    items:
                      $ref: '#/components/schemas/V0GetConfigCategoriesResponse'
                  config:
                    type: array
                    description: List of the main configuration parameters with acceptable values
                    items:
                      type: object
                      properties:
                        description:
                          type: string
                          description: Parameter description
                        name:
                          type: string
                          description: Parameter name
                        value:
                          type: string
                          description: Value
              example:
                categories:
                  - id: 760
                    name: Автомобильные товары
                    cpm_min: 112
                config:
                  - description: Минимальный бюджет кампании
                    name: budget_min
                    value: '1000'
                  - description: Максимальный период в днях, за который можно получить статистику
                    name: api_fullstat_day_depth
                    value: '31'
                  - description: Минимальная ставка CPM для автоматической кампании
                    name: cpm_min_booster
                    value: '100'
                  - description: Минимальная ставка CPM для аукциона
                    name: cpm_min_search_catalog
                    value: '150'
                  - description: Максимальное количество товаров для аукциона
                    name: max_nm_count
                    value: '50'
                  - description: Максимальное количество товаров для автоматической кампании
                    name: max_auto_nms
                    value: '100'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/promotion/adverts:
    servers:
      - url: https://advert-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Campaigns information
      description: |
        The method allows to retrieve information about campaigns via query parameters, or by a list of campaign IDs.<br>

        <div class="description_limit">  
          Maximum of 5 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Campaigns
      parameters:
        - name: status
          in: query
          description: |
            <dl>
            <dt>Campaign status:</dt>
            <dd><code>-1</code> - the campaign is in the process of being deleted</dd>
            <dd><code>4</code> - ready to be launched</dd>
            <dd><code>7</code> - campaign completed</dd>
            <dd><code>8</code> - declined</dd>
            <dd><code>9</code> - displays are ongoing</dd>
            <dd><code>11</code> - campaign is paused</dd>
            </dl>
            Campaign in the process of deletion. The status means that the campaign has been deleted and will disappear from the method's response within 3-10 minutes.
          schema:
            type: integer
            enum:
              - -1
              - 4
              - 7
              - 8
              - 9
              - 11
        - name: type
          in: query
          description: |
            <dl>
            <dt>Campaign type:</dt>
            <dd><code>4</code> - campaign in catalog (<strong>deprecated type</strong>)</dd>
            <dd><code>5</code> - campaign in content (<strong>deprecated type</strong>)</dd>
            <dd><code>6</code> - campaign in search (<strong>deprecated type</strong>)</dd>
            <dd><code>7</code> - campaign in recommendations on the main page (<strong>deprecated type</strong>)</dd>
            <dd><code>8</code> - automatic campaign </dd>
            <dd><code>9</code> - Auction </dd>
            </dl>
          schema:
            type: integer
            enum:
              - 4
              - 5
              - 6
              - 7
              - 8
              - 9
        - name: order
          in: query
          description: |
            <dl>
            <dt>Order:</dt>
            <dd><code>create</code> (by the time of campaign creation)</dd>
            <dd><code>change</code> (by the time of the last change to the campaign)</dd>
            <dd><code>id</code> (by the campaign identifier)</dd>
            </dl>
          schema:
            type: string
            enum:
              - create
              - change
              - id
        - name: direction
          in: query
          description: |
            <dl>
            <dt>Direction:</dt>
            <dd><code>desc</code> (from greater to lesser)</dd>
            <dd><code>asc</code> (from lesser to greater)</dd>
            </dl>
            <br>For example: <code>/adv/v1/promotion/adverts?type=6&order=change&<b>direction=asc</b></code>
          schema:
            type: string
            enum:
              - desc
              - asc
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                type: integer
                description: List of campaign IDs. A Maximum of of 50. <br> <br> You can get campaign IDs using the <b>Campaign Lists</b> method.
            example:
              - 1234567
              - 63453471
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  anyOf:
                    - $ref: '#/components/schemas/ResponseInfoAdvert'
                    - $ref: '#/components/schemas/ResponseInfoAdvertType8'
                    - $ref: '#/components/schemas/ResponseInfoAdvertType9'
              examples:
                ResponseInfoAdvert:
                  $ref: '#/components/examples/ResponseInfoAdvert'
                ResponseInfoAdvertType8:
                  $ref: '#/components/examples/ResponseInfoAdvertType8'
                ResponseInfoAdvertType9:
                  $ref: '#/components/examples/ResponseInfoAdvertType9'
                ResponseInfoAdvertsAll:
                  $ref: '#/components/examples/ResponseInfoAdvertsAll'
        '204':
          description: Campaigns not found
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
              examples:
                IncorrectTypeAdv:
                  $ref: '#/components/examples/IncorrectTypeAdv'
                IncorrectSupplierIdAdv:
                  $ref: '#/components/examples/IncorrectSupplierIdAdv'
                IncorrectUsingMethods:
                  $ref: '#/components/examples/IncorrectUsingMethods'
        '401':
          $ref: '#/components/responses/401'
        '422':
          description: Error processing request parameters
          content:
            text/plain:
              schema:
                type: string
              examples:
                ErrorProcessRequestParam:
                  $ref: '#/components/examples/ErrorProcessRequestParam'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/save-ad:
    servers:
      - url: https://advert-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Creating automatic campaign
      description: |
        Creates an automatic campaign.<br>

        <div class="description_limit">  
          Maximum of 1 request per 20 <a href="/openapi/api-information#tag/Introduction/Rate-Limits">seconds</a> per one seller's account
        </div>
      tags:
        - Campaigns Creation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  description: |
                    <dl>
                    <dt>Type of automatic campaign:</dt>
                    <dd><code>8</code></dd>
                    </dl>
                  type: integer
                  example: 8
                name:
                  description: Campaign name (max. 128 characters)
                  type: string
                  example: Парашюты
                subjectId:
                  description: |
                    ID of the item for which the campaign is created.<br>
                    Seller's existing IDs can be received by "Content / View - "Nomenclature list" method, response field is `subjectID`.
                  type: integer
                  example: 270
                sum:
                  description: Top-up amount
                  type: integer
                  example: 500
                btype:
                  description: |
                    <dl>
                    <dt>Type of write-off.</dt>
                    <dd><code>0</code> - Account</dd>
                    <dd><code>1</code> - Balance</dd>
                    <dd><code>3</code> - Bonuses</dd>
                    </dl>
                  type: integer
                  example: 1
                on_pause:
                  description: |
                    <dl>
                    <dt>After creating campaign:</dt> 
                    <dl>
                      <dt><code>true</code> - Will be suspended.</dt>
                      <dd>The campaign launch will be available within 3 minutes after the campaign is created.</dd>
                    </dl> 
                    <dd><code>false</code> - Will be launched</dd> 
                    </dl>
                  type: boolean
                  example: true
                nms:
                  description: |
                    WB articles array.  <br>
                    Maximum of 100 articles
                  type: array
                  items:
                    type: integer
                cpm:
                  description: |
                    Bid. <br>
                    If a bid is specified that is less than the allowed size, the bid of the minimum allowed size will be automatically set.
                  type: integer
            example:
              type: 8
              name: Парашюты
              subjectId: 270
              sum: 500
              btype: 1
              on_pause: true
              nms:
                - 9178363
                - 9178364
              cpm: 10
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                description: ID of created campaign
                type: string
              example: '9008917'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
              examples:
                IncorrectId:
                  summary: ''
                  description: ''
                  value:
                    error: Некорректный ID предмета
                BaseBudget500:
                  value:
                    error: базовый бюджет должен быть больше 500р
                InvalidWriteOffType:
                  value:
                    error: Некорректный тип списания
                DepositAmount50:
                  value:
                    error: Сумма пополнения должна быть кратна 50р
                InsufficientFundsInAccount:
                  value:
                    error: недостаточно средств на счете
                IncorrectCompanyName:
                  value:
                    error: Некорректное название кампании
                NoProductsSelectedCategory:
                  value:
                    error: Товары выбранной категории не в наличии
                PlacementIsNotPossible:
                  value:
                    error: размещение невозможно
        '401':
          $ref: '#/components/responses/401'
        '422':
          description: Error processing request parameters
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
              examples:
                ReceivingRecommendationsOnMainPage:
                  value:
                    error: Ошибка получения размещения в рекомендациях на главной
                NotGetSupplierBalance:
                  value:
                    error: Не удалось получить баланс поставщика
                CreatingSinglePlacement:
                  value:
                    error: Ошибка создания единственного размещения
                ReceivingInProductCard:
                  value:
                    error: Ошибка получения размещения в карточке товара
                GettingCpmCalculation:
                  value:
                    error: Ошибка получения расчета ставок
                SavingPlacementInCard:
                  value:
                    error: Ошибка сохранения размещения в карточке
                SavingPlacementInRecommend:
                  value:
                    error: Ошибка сохранения размещения в рекомендациях
                FailedPutCampaignInCache:
                  value:
                    error: не удалось положить кампанию в кеш
        '429':
          $ref: '#/components/responses/429'
  /adv/v2/seacat/save-ad:
    servers:
      - url: https://advert-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Create Auction campaign
      description: "Creates Auction campaign. <br>\n\n<div class=\"description_limit\">  \n  Maximum of 5\_requests per minute per one seller's account\n</div>\n"
      tags:
        - Campaigns Creation
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                campaignName:
                  description: Campaign name
                  type: string
                nms:
                  type: array
                  description: "Nomenclature for this campaign. You can available nomenclatures with [Nomenclatures for campaigns](./promotion#tag/Nomenclatures/paths/~1adv~1v2~1supplier~1nms/post) method. Maximum of 50\_products (`nm`)\n"
                  items:
                    type: integer
                    description: WB article (`nmId`)
            example:
              name: Телефоны
              nms:
                - *********
                - *********
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                type: integer
                description: Campaign ID
                example: 1234567
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
              example: Нет доступных категорий для рк. Создайте новую кампанию для попадания в текущие категории
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/supplier/subjects:
    servers:
      - url: https://advert-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Subjects for campaigns
      description: |
        Returns subjects nomenclatures from which are available for all campaigns

        <div class="description_limit">  
          Maximum of 1 request per 12 <a href="/openapi/api-information#tag/Introduction/Rate-Limits">seconds</a> per one seller's account
        </div>
      tags:
        - Campaigns Creation
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                nullable: true
                x-nullable: true
                items:
                  type: object
                  properties:
                    id:
                      description: Subject ID
                      type: integer
                    name:
                      description: Subject
                      type: string
                    count:
                      description: Number of WB Articles (`nmId`) in this subject
                      type: integer
              examples:
                Array:
                  value:
                    - name: 3D очки
                      id: 2560
                      count: 1899
                'null':
                  value: null
        '401':
          $ref: '#/components/responses/401'
        '404':
          description: Not found
        '429':
          $ref: '#/components/responses/429'
  /adv/v2/supplier/nms:
    servers:
      - url: https://advert-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Nomenclatures for campaigns
      description: "Returns nomenclatures that are available for all campaigns.\n\n<div class=\"description_limit\">  \n  Maximum of 5\_requests per minute per one seller's account\n</div>\n"
      tags:
        - Campaigns Creation
      requestBody:
        description: ID of subjects to get nomenclatures
        content:
          application/json:
            schema:
              type: array
              items:
                type: integer
            example:
              - 123
              - 456
              - 765
              - 321
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                description: Nomenclatures for campaigns
                items:
                  type: object
                  properties:
                    title:
                      type: string
                      description: Product name
                      example: Плед
                    nm:
                      type: integer
                      description: WB article (`nmId`)
                      example: *********
                    subjectId:
                      type: integer
                      description: Subject ID
                      example: 765
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
              example: Error processing request body
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v0/cpm:
    servers:
      - url: https://advert-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Bid update
      deprecated: true
      description: |
        The changed bid will appear in the campaign information within 3 minutes.<br>
        <div class="description_important">
          If the rate to be set is less than the allowable rate, the status code 422 (Rate size not changed) will be returned.
          </div>        

        The procedure for filling in the parameters `type`, `instrument`, `param` when changing the rate for a campaign with type 9 (Auction):
        <br> For 'type' it is necessary to specify the value 9 (always).
        <br> A value of 4 or 6 should be specified for `instrument`, depending on whether the rate is to be changed in the catalogue (<strong>deprecated campaign type</strong>) or in the search (<strong>deprecated campaign type</strong>).
        <br> For 'param' it is necessary to specify the value of the <b>id</b> field from the <b>subject</b> structure response of the [Information about campaign](/openapi/promotion#tag/Campaign-Management/paths/~1adv~1v1~1promotion~1adverts/post) method.
        <div class="description_limit">  
          Maximum of 5 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Campaigns Management
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - advertId
                - type
                - cpm
                - param
              properties:
                advertId:
                  description: Campaign ID where the bid is changing
                  type: integer
                  example: 789
                type:
                  description: |
                    <dl>
                    <dt>Campaign type to change the price:</dt>
                    <dd><code>5</code> - campaign in content (<strong>deprecated type</strong>)</dd>
                    <dd><code>6</code> - campaign in search (<strong>deprecated type</strong>)</dd>
                    <dd><code>7</code> - campaign on main page recommendations (<strong>deprecated type</strong>)</dd>
                    <dd><code>8</code> - automatic campaign</dd>
                    <dd><code>9</code> - Auction campaign </dd>
                    </dl>
                  type: integer
                  enum:
                    - 5
                    - 6
                    - 7
                    - 8
                    - 9
                cpm:
                  description: New bid value
                  type: integer
                  example: 456
                param:
                  description: |
                    The parameter for which the change will be made is the value of `subjectId` (for search and recommendation campaigns (<strong>deprecated campaign types</strong>)), `setId` (for product card campaigns (<strong>deprecated campaign type</strong>)), or `menuId` (for catalog campaigns (<strong>deprecated campaign type</strong>)). 
                    <br> For automated campaigns, this parameter is not required.
                  type: integer
                  example: 23
                instrument:
                  description: Campaign type for bid change in Auction
                  type: integer
                  example: 4
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
              examples:
                IncorrectParamAdv:
                  $ref: '#/components/examples/IncorrectParamAdv'
                IncorrectTypeAdv:
                  $ref: '#/components/examples/IncorrectTypeAdv'
                IncorrectCpmAdv:
                  $ref: '#/components/examples/IncorrectCpmAdv'
                IncorrectSupplierIdAdv:
                  $ref: '#/components/examples/IncorrectSupplierIdAdv'
        '401':
          $ref: '#/components/responses/401'
        '422':
          description: Bid size not changed
          content:
            text/plain:
              schema:
                type: string
              examples:
                RequestBodyProcessErrorAdv:
                  $ref: '#/components/examples/RequestBodyProcessErrorAdv'
                AmountNotChanged:
                  $ref: '#/components/examples/AmountNotChanged'
        '429':
          $ref: '#/components/responses/429'
  /adv/v0/bids:
    servers:
      - url: https://advert-api.wildberries.ru
    patch:
      summary: Change product cards bids
      description: |
        The method changes the bids of product cards by WB articles in automatic campaigns and Auctions.
        <br><br> 
        For campaigns in any status except `-1`, `7` and `8`
        <br>
        Minimum allowable bids can be found in the response of the method for obtaining [configuration values](/openapi/promotion#tag/Sozdanie-kampanij/paths/~1adv~1v0~1config/get)
        <div class="description_limit">  
          Maximum of 5 request per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Campaigns Management
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - bids
              properties:
                bids:
                  type: array
                  maxItems: 20
                  items:
                    $ref: '#/components/schemas/V0AdvertMultibid'
      responses:
        '204':
          description: Success
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  detail:
                    type: string
                    description: Error details
                  origin:
                    type: string
                    description: WB internal service ID
                  request_id:
                    type: string
                    description: Unique request ID
                  status:
                    type: integer
                    description: HTTP status code
                  title:
                    type: string
                    description: Error title
                  errors:
                    type: array
                    description: Error details
                    items:
                      type: object
                      properties:
                        detail:
                          type: string
                          description: Error description
                        field:
                          type: string
                          description: Field containing error
                  type:
                    type: string
                    description: Error type
              examples:
                CampaignIsNotUnique:
                  $ref: '#/components/examples/CampaignIsNotUnique'
                CanNotDeserializeResponseBody:
                  $ref: '#/components/examples/CanNotDeserializeResponseBody'
                CampaignNotFound:
                  $ref: '#/components/examples/CampaignNotFoundBids'
                NmNotFound:
                  $ref: '#/components/examples/NmNotFound'
                WrongCampaignID:
                  $ref: '#/components/examples/WrongCampaignID'
                WrongCampaignStatus:
                  $ref: '#/components/examples/WrongCampaignStatus'
                WrongBidValue:
                  $ref: '#/components/examples/WrongBidValue'
  /adv/v0/delete:
    servers:
      - url: https://advert-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Delete campaign
      description: |
        The method allows to delete campaigns in the status <b>4 - ready to launch</b>. <br>

        After deleting, the campaign will be in <b>-1</b> status for a while.<br>

        It takes between 3 and 10 minutes to completely delete the campaign.

        <div class="description_limit">  
          Maximum of 5 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Campaigns Management
      parameters:
        - name: id
          in: query
          description: Campaign ID
          schema:
            type: integer
          required: true
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseAdvError1'
              examples:
                ResponseInvalidCampaignID:
                  $ref: '#/components/examples/ResponseInvalidCampaignID'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v0/rename:
    servers:
      - url: https://advert-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Rename campaign
      description: |
        The method allows to rename a campaign. <br>

        <div class="description_limit">  
          Maximum of 5 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Campaigns Management
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - advertId
                - name
              properties:
                advertId:
                  type: integer
                  description: ID of the campaign that has the name change
                name:
                  type: string
                  description: New name (max 100 characters)
            example:
              advertId: 2233344
              name: newnmame
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
              examples:
                InvalidRcIdAdv:
                  $ref: '#/components/examples/InvalidRcIdAdv'
                IncorrectName:
                  $ref: '#/components/examples/IncorrectName'
                IncorrectSupplierIdAdv:
                  $ref: '#/components/examples/IncorrectSupplierIdAdv'
        '401':
          $ref: '#/components/responses/401'
        '422':
          description: Error processing request parameters
          content:
            text/plain:
              schema:
                type: string
              examples:
                RequestBodyProcessErrorAdv:
                  $ref: '#/components/examples/RequestBodyProcessErrorAdv'
                CompanyNameChangeErr:
                  $ref: '#/components/examples/CompanyNameChangeErr'
        '429':
          $ref: '#/components/responses/429'
  /adv/v0/start:
    servers:
      - url: https://advert-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Launch campaign
      description: |
        The method allows to run campaigns that are in statuses <b>4 - ready to launch</b> or <b>11 - paused campaign</b>. <br>

        To run an ad campaign with status <b>11</b>, it must have a replenished budget.<br>
        <dl>
        <dt>To run a campaign with status <b>4</b>, it is necessary to do following (the order of actions does not matter):</dt>
        <dd>1. After creating a campaign in the WB. Promotion cabinet click the `Apply changes` button.</dd>
        <dd>2. Set the budget.</dd>

        <div class="description_limit">  
          Maximum of 5 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Campaigns Management
      parameters:
        - name: id
          in: query
          description: Campaign ID
          schema:
            type: integer
            example: 1234
          required: true
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
              examples:
                InvalidRcIdAdv:
                  $ref: '#/components/examples/InvalidRcIdAdv'
                IncorrectSupplierIdAdv:
                  $ref: '#/components/examples/IncorrectSupplierIdAdv'
        '401':
          $ref: '#/components/responses/401'
        '422':
          description: Status not changed
          content:
            text/plain:
              schema:
                type: string
              examples:
                StatusNoChangeAdv:
                  $ref: '#/components/examples/StatusNoChangeAdv'
        '429':
          $ref: '#/components/responses/429'
  /adv/v0/pause:
    servers:
      - url: https://advert-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Pause campaign
      description: |
        Campaign in status `9` - active - can be paused

        <div class="description_limit">  
          Maximum of 5 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> ='./api-information#tag/Introduction/Rate-Limits'>second</a> per one seller's account
        </div>
      tags:
        - Campaigns Management
      parameters:
        - name: id
          in: query
          description: Campaign ID
          schema:
            type: integer
            example: 1234
          required: true
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
              examples:
                InvalidRcIdAdv:
                  $ref: '#/components/examples/InvalidRcIdAdv'
                IncorrectSupplierIdAdv:
                  $ref: '#/components/examples/IncorrectSupplierIdAdv'
        '401':
          $ref: '#/components/responses/401'
        '422':
          description: Status not changed
          content:
            text/plain:
              schema:
                type: string
              examples:
                StatusNoChangeAdv:
                  $ref: '#/components/examples/StatusNoChangeAdv'
        '429':
          $ref: '#/components/responses/429'
  /adv/v0/stop:
    servers:
      - url: https://advert-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Stop campaign
      description: |-
        The method allows to end campaigns that are in status 9, 11 or 4.
        <div class="description_limit"> Maximum of 5 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> ='./api-information#tag/Introduction/Rate-Limits'>second</a> per one seller's account </div>
      tags:
        - Campaigns Management
      parameters:
        - name: id
          in: query
          description: Campaign ID
          schema:
            type: integer
            example: 1234
          required: true
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
              examples:
                InvalidRcIdAdv:
                  $ref: '#/components/examples/InvalidRcIdAdv'
                IncorrectSupplierIdAdv:
                  $ref: '#/components/examples/IncorrectSupplierIdAdv'
        '401':
          $ref: '#/components/responses/401'
        '422':
          description: Status not changed
          content:
            text/plain:
              schema:
                type: string
              examples:
                StatusNoChangeAdv:
                  $ref: '#/components/examples/StatusNoChangeAdv'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/balance:
    servers:
      - url: https://advert-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Balance
      description: |
        The method allows to get information about the seller's net, balance and bonuses<br>

        <div class="description_limit">  
          Maximum of 1 request per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Finances
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  balance:
                    description: Account, russian ruble
                    type: integer
                  net:
                    description: Balance, russian ruble
                    type: integer
                  bonus:
                    description: Bonuses, russian ruble
                    type: integer
              example:
                balance: 11083
                net: 0
                bonus: 15187
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
              examples:
                IncorrectSupplierIdAdv:
                  $ref: '#/components/examples/IncorrectSupplierIdAdv'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/budget:
    servers:
      - url: https://advert-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Campaign budget
      description: |
        The method allows to get information about the budget of a campaign. 
        <br>
        <div class="description_limit">  
          Maximum of 4 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Finances
      parameters:
        - name: id
          in: query
          description: Campaign ID
          schema:
            type: integer
            example: 1
          required: true
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  cash:
                    description: The field is not used. The value is always equal to 0.
                    type: integer
                  netting:
                    description: The field is not used. The value is always equal to 0.
                    type: integer
                  total:
                    description: Campaign budget, RUB
                    type: integer
              example:
                cash: 0
                netting: 0
                total: 500
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
              examples:
                CampaignNotBelongSeller:
                  $ref: '#/components/examples/CampaignNotBelongSeller'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/budget/deposit:
    servers:
      - url: https://advert-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Top-up of the campaign budget
      description: |
        The method allows to top up the budget of the campaign.

        <div class="description_limit">  
          Maximum of 1 request per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Finances
      parameters:
        - name: id
          in: query
          description: Campaign ID
          schema:
            type: integer
            example: 1234567
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                sum:
                  description: Top-up amount
                  type: integer
                  example: 5000
                type:
                  description: |
                    <dl>
                    <dt>Type of top-up source:</dt>
                    <dd><code>0</code> - Account</dd>
                    <dd><code>1</code> - Balance</dd>
                    <dd><code>3</code> - Bonuses</dd>
                    </dl>
                  type: integer
                  example: 1
                return:
                  type: boolean
                  description: Response return flag (`true` means updated campaign budget size will be returned in the response, `false` or empty means nothing will be returned).
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/ResponseWithReturn'
              examples:
                ResponseWithReturn:
                  $ref: '#/components/examples/ResponseWithReturn'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
              examples:
                DepositAmountMultiple50:
                  description: The top-up amount must be a multiple of 50 roubles
                  value:
                    error: Сумма пополнения должна быть кратна 50 руб
                MinimumDepositAmountIs500:
                  description: Minimum top-up amount is 1000 RUB
                  value:
                    error: Минимальная сумма пополнения 1000 рублей
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/upd:
    servers:
      - url: https://advert-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Receiving costs history
      description: |
        The method allows to get a costs history

        <div class="description_limit">  
          Maximum of 1 request per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Finances
      parameters:
        - name: from
          in: query
          description: Beginning of the interval
          schema:
            type: string
            format: date
            example: '2023-07-31'
          required: true
        - name: to
          in: query
          description: |
            End of interval. <br>
            (Minimum interval is 1 day, maximum is 31)
          schema:
            type: string
            format: date
            example: '2023-08-02'
          required: true
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    updNum:
                      description: Billing document number (if any)
                      type: integer
                    updTime:
                      description: Time of charge-off
                      type: string
                      format: time-date
                    updSum:
                      description: Amount invoiced
                      type: integer
                    advertId:
                      description: Campaign ID
                      type: integer
                    campName:
                      description: Campaign name
                      type: string
                    advertType:
                      description: Type of campaign
                      type: integer
                    paymentType:
                      description: |
                        Source of charge-off:
                         - `Баланс` - Balance
                         - `Бонусы` - Bonuses
                         - `Счет` - Money account
                         - `Кэшбэк` - Debit card cashback
                      type: string
                    advertStatus:
                      description: |
                        <dl>
                          <dt>Campaign status:</dt>
                          <dd><code>4</code> - ready to run </dd>
                          <dd><code>7</code> - is over</dd>
                          <dd><code>8</code> - refused</dd>
                          <dd><code>9</code> - active</dd>
                          <dd><code>11</code> - paused</dd>
                        </dl>
                      type: integer
              example:
                - updNum: 0
                  updTime: '2023-07-31T12:12:54.060536+03:00'
                  updSum: 24
                  advertId: 3355881
                  campName: лук лучок
                  advertType: 6
                  paymentType: Баланс
                  advertStatus: 9
                - updNum: 0
                  updTime: null
                  updSum: 107
                  advertId: 3366882
                  campName: золотая луковица
                  advertType: 8
                  paymentType: Счет
                  advertStatus: 11
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
              examples:
                IncorrectSupplierIdAdv:
                  $ref: '#/components/examples/IncorrectSupplierIdAdv'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/payments:
    servers:
      - url: https://advert-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Receiving the history of account top-ups
      description: |
        The method allows you to get a history of top-ups.

        <div class="description_limit">  
          Maximum of 1 request per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Finances
      parameters:
        - name: from
          in: query
          description: Beginning of the interval
          schema:
            type: string
            format: date
            example: '2023-07-31'
        - name: to
          in: query
          description: |
            End of interval. <br>
            (Minimum interval is 1 day, maximum is 31)
          schema:
            type: string
            format: date
            example: '2023-08-02'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      description: Payment ID
                      type: integer
                    date:
                      description: Payment date
                      type: string
                      format: time-date
                    sum:
                      description: Payment amount
                      type: integer
                    type:
                      description: |
                        <dl>
                        <dt>Type of charge-off source:</dt>
                        <dd><code>0</code> - Account</dd>
                        <dd><code>1</code> - Balance</dd>
                        <dd><code>3</code> - Card</dd>
                        </dl>
                      type: integer
                    statusId:
                      description: |
                        <dl>
                        <dt>Status:</dt>
                        <dd><code>0</code> - error</dd>
                        <dd><code>1</code> - processed</dd>
                        </dl>
                      type: integer
                    cardStatus:
                      description: |
                        <dl>
                        <dt>Transaction status (when paying by card):</dt>
                        <dd><b>success</b> - success</dd>
                        <dd><b>fail</b> - not success</dd>
                        <dd><b>pending</b> - waiting for response</dd>
                        <dd><b>unknown</b> - unknown</dd>
                        </dl>
                      type: string
              example:
                - id: 1036666
                  date: '2022-02-04T09:06:47.985843Z'
                  sum: 600
                  type: 0
                  statusId: 1
                  cardStatus: ''
                - id: 55261296
                  date: '2023-04-13T10:07:42'
                  sum: 1500
                  type: 3
                  statusId: 1
                  cardStatus: succeeded
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
              examples:
                IncorrectSupplierIdAdv:
                  $ref: '#/components/examples/IncorrectSupplierIdAdv'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/search/set-plus:
    servers:
      - url: https://advert-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Managing the activity of fixed phrases
      description: |
        The method allows to change the activity of fixed phrases. Only for Auction campaigns.  

        <div class="description_limit">  
          Maximum of 1 request per 500 <a href="/openapi/api-information#tag/Introduction/Rate-Limits">milliseconds</a> per one seller's account
        </div>
      tags:
        - Campaigns Parameters
      parameters:
        - name: id
          in: query
          description: Campaign ID
          schema:
            type: integer
            example: 1234567
          required: true
        - name: fixed
          in: query
          description: New state (`false` - make inactive, `true` - make active)
          schema:
            type: boolean
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
                example: Bad request
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
    post:
      security:
        - HeaderApiKey: []
      summary: Setting/deleting fixed phrases
      description: |
        The method allows to set and delete fixed phrases.Only for Auction campaigns. 

        Sending an empty array removes all fixed phrases and disables fixed phrase activity in the campaign.

        <div class="description_limit">  
          Maximum of 1 request per 500 <a href="/openapi/api-information#tag/Introduction/Rate-Limits">milliseconds</a> per one seller's account
        </div>
      tags:
        - Campaigns Parameters
      parameters:
        - name: id
          in: query
          description: Campaign ID
          schema:
            type: integer
            example: 1234567
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                pluse:
                  description: Fixed phrase list (max. 100)
                  type: array
                  items:
                    type: string
            example:
              pluse:
                - Phrase 1
                - Phrase 2
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
              example:
                - Фраза 1
                - Фраза 2
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
                example: 'json: cannot unmarshal number into Go struct field request.pluse of type string'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/search/set-phrase:
    servers:
      - url: https://advert-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Setting/removing minus-phrases of phrase matching
      description: |
        Sets/removes minus-phrases of a phrase match. Only for Auction campaigns.<br>  
        The Maximum of allowed number of minus-phrases in a campaign is 1000 pcs.<br> 
        Posting an empty array removes all minus-phrase of phrase matching from the campaign.

        <div class="description_limit">  
          Maximum of 2 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Campaigns Parameters
      parameters:
        - name: id
          in: query
          description: Campaign ID
          schema:
            type: integer
            example: 1234567
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                phrase:
                  description: Minus-phrases (max. 1000 pcs.)
                  type: array
                  items:
                    type: string
            example:
              phrase:
                - сло
                - гу
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
                example: Bad request
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/search/set-strong:
    servers:
      - url: https://advert-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Setting/removing minus of exact match phrases
      description: |
        Sets/removes minus-phrases of exact match. Only for Auction campaigns.<br>  

        The Maximum of allowed number of minus-phrases in a campaign is 1000 pcs.<br> 

        Sending an empty array removes all minus-phrases of the exact match from the ad campaign.

        <div class="description_limit">  
          Maximum of 2 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Campaigns Parameters
      parameters:
        - name: id
          in: query
          description: Campaign ID
          schema:
            type: integer
            example: 1234567
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                strong:
                  description: Minus-phrases (max. 1000 pcs.)
                  type: array
                  items:
                    type: string
            example:
              strong:
                - стоять
                - лопата
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
                example: Bad request
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/search/set-excluded:
    servers:
      - url: https://advert-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Setting/removing minus phrases from search
      description: |
        Sets/removes minus-phrases for search. Only for Auction campaigns.<br>  
        The Maximum of allowed number of minus-phrases in a campaign is 1000 pcs.<br> 
        Sending an empty array removes all minus-phrases from the Search ad campaign.

        <div class="description_limit">  
          Maximum of 2 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Campaigns Parameters
      parameters:
        - name: id
          in: query
          description: Campaign ID
          schema:
            type: integer
            example: 1234567
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                excluded:
                  description: Minus-phrases (max. 1000 pcs.)
                  type: array
                  items:
                    type: string
            example:
              excluded:
                - что-то синее
                - картошечка
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
                example: Bad request
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/auto/set-excluded:
    servers:
      - url: https://advert-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Setting/removing minus-phrases for automatic campaigns
      description: |
        The method allows to set or remove minus phrases.<br>
        Sending an empty array removes all minus phrases from the campaign.      

        <div class="description_limit">  
          Maximum of 1 request per 6 <a href="/openapi/api-information#tag/Introduction/Rate-Limits">seconds</a> per one seller's account
        </div>
      tags:
        - Campaigns Parameters
      parameters:
        - name: id
          in: query
          description: Campaign ID
          schema:
            type: integer
            example: 1234567
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                excluded:
                  description: List of phrases (Maximum of 1000 pcs.)
                  type: array
                  items:
                    type: string
            examples:
              SettingMinusPhrase:
                $ref: '#/components/examples/SettingMinusPhrase'
              RemovingMinusPhrase:
                $ref: '#/components/examples/RemovingMinusPhrase'
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
                example: Bad request
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/auto/getnmtoadd:
    servers:
      - url: https://advert-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: List of nomenclatures for automatic campaign
      description: |
        The method allows to get the list of nomenclatures available for adding to the campaign. <br>

        <div class="description_limit">  
          Maximum of 1 request per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Campaigns Parameters
      parameters:
        - name: id
          in: query
          description: Campaign ID
          schema:
            type: integer
            example: 1
          required: true
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  type: integer
              example:
                - **********
                - **********
                - **********
                - **********
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/auto/updatenm:
    servers:
      - url: https://advert-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Changing the list of nomenclatures in an automatic campaign
      description: |
        The method allows you to add and remove nomenclatures.

        <div class="description_important">
          It is possible to add only those nomenclatures that will be returned in the method response <a href="/openapi/promotion#tag/Automated-Campaign-Parameters/paths/~1adv~1v1~1auto~1getnmtoadd/get">List of nomenclatures in an automatic campaign</a>.<br>You cannot delete a one single nomenclature from a campaign.
        </div>

        There is no validation by the `delete` parameter.

        If you receive a response with a status code of 200 and no change has occurred, check the request for documentation compliance

        <div class="description_limit">  
          Maximum of 60 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account
        </div>
      tags:
        - Campaigns Parameters
      parameters:
        - name: id
          in: query
          description: Campaign ID
          schema:
            type: integer
            example: 1
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                add:
                  description: The nomenclatures that need to be added
                  type: array
                  items:
                    type: integer
                delete:
                  description: The nomenclatures that need to be deleted
                  type: array
                  items:
                    type: integer
            example:
              add:
                - ********
                - ********
              delete:
                - ********
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseAdvError1'
              examples:
                CampaignNotFoundAdv:
                  $ref: '#/components/examples/CampaignNotFoundAdv'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/count:
    servers:
      - url: https://advert-media-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Media campaigns number
      description: |
        Method allows you to get the number of the seller's media campaigns.

        <div class="description_limit">  
          Maximum of 10 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Media
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  all:
                    description: Total number of media campaigns with all statuses and types
                    type: integer
                  adverts:
                    type: object
                    properties:
                      type:
                        description: |
                          <dl>
                          <dt>Media campaign type:</dt>
                          <dd><code>1</code> — daily basis</dd>
                          <dd><code>2</code> — views basis</dd>
                          </dl>
                        type: integer
                      status:
                        description: |
                          <dl>
                          <dt>Media campaign status:</dt>
                            <dd><code>1</code> — template</dd>
                            <dd><code>2</code> — moderation
                            <dd><code>3</code> — rejected (with the possibility to resubmit for moderation)</dd>
                            <dd><code>4</code> — approved</dd>
                            <dd><code>5</code> — scheduled</dd>
                            <dd><code>6</code> — running</dd>
                            <dd><code>7</code> — completed</dd>
                            <dd><code>8</code> — refused</dd>
                            <dd><code>9</code> — paused by seller</dd>
                            <dd><code>10</code> — paused due to daily limit</dd>
                            <dd><code>11</code> — paused due to budget limit</dd>
                          </dl>
                        type: integer
                      count:
                        description: Number of media campaigns
                        type: integer
              example:
                all: 6
                adverts:
                  - type: 2
                    status: 7
                    count: 2
                  - type: 2
                    status: 8
                    count: 4
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/adverts:
    servers:
      - url: https://advert-media-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: List of media campaigns
      description: |
        The method allows to get the list of media campaigns of the seller

        <div class="description_limit">  
          Maximum of 10 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Media
      parameters:
        - name: status
          in: query
          description: |
            <dl>
            <dt>Media campaign status:</dt>
              <dd><code>1</code> — template</dd>
              <dd><code>2</code> — moderation
              <dd><code>3</code> — rejected (with the possibility to resubmit for moderation)</dd>
              <dd><code>4</code> — approved</dd>
              <dd><code>5</code> — scheduled</dd>
              <dd><code>6</code> — running</dd>
              <dd><code>7</code> — completed</dd>
              <dd><code>8</code> — refused</dd>
              <dd><code>9</code> — paused by seller</dd>
              <dd><code>10</code> — paused due to daily limit</dd>
              <dd><code>11</code> — paused due to budget limit</dd>
            </dl>
          schema:
            type: integer
            example: 1
        - name: type
          in: query
          description: |
            <dl>
            <dt>Media campaign type:</dt>
            <dd><code>1</code> — daily basis</dd>
            <dd><code>2</code> — views basis</dd>
            </dl>
          schema:
            type: integer
            example: 1
        - name: limit
          in: query
          description: Number of campaigns in the response
          schema:
            type: integer
            example: 1
        - name: offset
          in: query
          description: Offset relative to the first media campaign
          schema:
            type: integer
            example: 1
        - name: order
          in: query
          description: |
            <dl>
            <dt>The order in which the response is displayed:</dt>
            <dd><code>create</code> — by time of media campaign creation</dd>
            <dd><code>id</code> — by ID of media campaign creation</dd>
            </dl>
          schema:
            type: string
            example: id
        - name: direction
          in: query
          description: |
            <dl>
            <dt>Sorting order:</dt>
            <dd><code>desc</code> — upward</dd>
            <dd><code>asc</code> — smaller to larger</dd>            
            </dl>
          schema:
            type: string
            example: desc
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    advertId:
                      description: Media campaign ID
                      type: integer
                    name:
                      description: Media campaign name
                      type: string
                    brand:
                      description: Brand name
                      type: string
                    type:
                      description: |
                        <dl>
                        <dt>Media campaign type:</dt>
                        <dd><code>1</code> — daily basis</dd>
                        <dd><code>2</code> — views basis</dd>
                        </dl>
                      type: integer
                    status:
                      description: |
                        <dl>
                        <dt>Media campaign status:</dt>
                          <dd><code>1</code> — template</dd>
                          <dd><code>2</code> — moderation
                          <dd><code>3</code> — rejected (with the possibility to resubmit for moderation)</dd>
                          <dd><code>4</code> — approved</dd>
                          <dd><code>5</code> — scheduled</dd>
                          <dd><code>6</code> — running</dd>
                          <dd><code>7</code> — completed</dd>
                          <dd><code>8</code> — refused</dd>
                          <dd><code>9</code> — paused by seller</dd>
                          <dd><code>10</code> — paused due to daily limit</dd>
                          <dd><code>11</code> — paused due to budget limit</dd>
                        </dl>
                      type: integer
                    createTime:
                      description: Time of media campaign creation
                      type: string
                      format: date-time
                    endTime:
                      description: 'Time of completion of the media campaign '
                      type: string
                      format: date-time
              example:
                - advertId: 123456
                  name: тост
                  brand: goosb
                  type: 2
                  status: 8
                  createTime: '2023-03-25T20:35:57.116943+03:00'
                - advertId: 54321
                  name: тест
                  brand: bobr
                  type: 1
                  status: 7
                  createTime: '2023-07-24T16:48:20.935599+03:00'
                  endTime: '2023-07-25T20:35:50.104978Z'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/advert:
    servers:
      - url: https://advert-media-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Information about media campaign
      description: |
        The method allows to get information about a single media campaign

        <div class="description_limit">  
          Maximum of 10 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Media
      parameters:
        - name: id
          in: query
          description: Media campaign ID
          schema:
            type: integer
            example: 23569
          required: true
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  advertId:
                    description: Media campaign ID
                    type: integer
                  name:
                    description: Media campaign name
                    type: string
                  brand:
                    description: Brand name
                    type: string
                  type:
                    description: |
                      <dl>
                      <dt>Media campaign type:</dt>
                      <dd><code>1</code> — daily basis</dd>
                      <dd><code>2</code> — views basis</dd>
                      </dl>
                    type: integer
                  status:
                    description: |
                      <dl>
                      <dt>Media campaign status:</dt>
                        <dd><code>1</code> — template</dd>
                        <dd><code>2</code> — moderation
                        <dd><code>3</code> — rejected (with the possibility to resubmit for moderation)</dd>
                        <dd><code>4</code> — approved</dd>
                        <dd><code>5</code> — scheduled</dd>
                        <dd><code>6</code> — running</dd>
                        <dd><code>7</code> — completed</dd>
                        <dd><code>8</code> — refused</dd>
                        <dd><code>9</code> — paused by seller</dd>
                        <dd><code>10</code> — paused due to daily limit</dd>
                        <dd><code>11</code> — paused due to budget limit</dd>
                      </dl>
                    type: integer
                  createTime:
                    description: Time of media campaign creation
                    type: string
                    format: date-time
                  extended:
                    type: object
                    properties:
                      reason:
                        description: Moderator's remark
                        nullable: true
                        type: string
                      expenses:
                        description: Expenses
                        type: integer
                      from:
                        description: Start of media campaign displays
                        type: string
                        format: date-time
                      to:
                        description: End of media campaign displays
                        type: string
                        format: date-time
                      updated_at:
                        description: Time of change in media campaign
                        type: string
                        format: date-time
                      price:
                        description: Cost of placement by day (for type 1)
                        type: integer
                      budget:
                        description: Budget balance (for type 2)
                        type: integer
                      operation:
                        description: Source of charge-off (1 - Balance Sheet, 2 - Account)
                        type: integer
                      contract_id:
                        description: Contract ID (for contracted sellers)
                        type: integer
                  items:
                    description: |
                      Banner information.
                      <br>
                      The response may not contain all fields, it depends on your media campaign configuration.
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          description: Banner ID
                          type: integer
                        name:
                          description: Brand
                          type: string
                        status:
                          description: Status (same as media campaign status)
                          type: integer
                        place:
                          description: Placement on the page
                          type: integer
                        budget:
                          description: Budget
                          type: integer
                        daily_limit:
                          description: Daily limit (for display banners)
                          type: integer
                        category_name:
                          description: Category name
                          type: string
                        cpm:
                          description: Bid
                          type: integer
                        url:
                          description: The URL of the page the user lands on when they click on the banner
                          type: string
                        advert_type:
                          description: |
                            <dl>
                            <dt>Campaign type:</dt>
                            <dd><code>1</code> — banner</dd>
                            <dd><code>2</code> — pop-up menu</dd>
                            <dd><code>3</code> — mailings</dd>
                            <dd><code>4</code> — social networks</dd>
                            <dd><code>5</code> — push notification in WB app</dd>
                            </dl>
                          type: integer
                        created_at:
                          description: Banner creation date
                          format: date-time
                          type: string
                        updated_at:
                          description: Banner update date
                          format: date-time
                          type: string
                        date_from:
                          description: Banner start date
                          format: date-time
                          type: string
                        date_to:
                          description: Banner stop date
                          format: date-time
                          type: string
                        nms:
                          description: WB articles
                          type: array
                          items:
                            type: integer
                        bottomText1:
                          description: Text under the banner
                          type: string
                        bottomText2:
                          description: Text under the banner, second string
                          type: string
                        message:
                          description: Notification or mailing text
                          type: string
                        additionalSettings:
                          description: |
                            Additional information.
                            <dl>
                            <dt>Mailing format:</dt>
                            <dd><code>1</code> — common</dd>
                            <dd><code>2</code> — segment</dd>
                            <dd><code>3</code> — unique</dd>
                            </dl>
                            <dl>
                            <dt>Social network:</dt>
                            <dd><code>1</code> — VK (<a href="vk.ru">vk.ru</a>)</dd>
                            <dd><code>2</code> — OK (<a href="ok.ru">ok.ru</a>)</dd>
                            </dl>
                          type: integer
                        receiversCount:
                          description: Number of push notification recipients
                          type: integer
                        subject_id:
                          description: Parent category ID for the good
                          type: integer
                        subject_name:
                          description: Parent category name for the good
                          type: string
                        action_name:
                          description: Promo name
                          type: string
                        show_hours:
                          description: Display time
                          type: array
                          items:
                            type: object
                            properties:
                              From:
                                description: Start time
                                type: integer
                              To:
                                description: End time
                                type: integer
                        Erid:
                          description: Unique campaign ID for the ad data operator
                          type: string
                example:
                  advertId: 23569
                  name: Реклама денег принеси
                  brand: Plank
                  type: 2
                  status: 11
                  createTime: '2023-07-19T11:13:41.195138+03:00'
                  extended:
                    reason: Для возобновления показов пополните бюджет медиакампании
                    expenses: 10000
                    from: '2023-07-19T12:05:35.847348Z'
                    to: '2123-07-20T08:14:13.079176+03:00'
                    updated_at: '2023-07-21T13:25:31.129766+03:00'
                    price: 0
                    budget: 0
                    operation: 1
                    contract_id: 0
                  items:
                    - id: 68080
                      name: Унисон
                      status: 7
                      place: 2
                      budget: 650000
                      daily_limit: 500
                      category_name: Главная
                      cpm: 351
                      url: https://www.wildberries.ru/promotions/ssylka-na-akciyou
                      advert_type: 1
                      created_at: '2023-11-01T15:40:46.86165+03:00'
                      updated_at: '2023-11-08T23:44:33.248229+03:00'
                      date_from: '2023-11-01T16:05:22.286002Z'
                      date_to: '2023-11-09T17:27:32.745869+03:00'
                      nms:
                        - 123456
                        - ********
                      bottomText1: string
                      bottomText2: string
                      message: string
                      additionalSettings: 1
                      receiversCount: 1
                      subject_id: 6945
                      subject_name: Бельё
                      action_name: Распродажа! Создай себе домашний уют!
                      show_hours:
                        - From: 7
                          To: 8
                      Erid: string
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /api/v1/calendar/promotions:
    servers:
      - url: https://dp-calendar-api.wildberries.ru
    get:
      tags:
        - Promotions Calendar
      security:
        - HeaderApiKey: []
      summary: Promotions List
      description: |
        Returns a promotions list with dates and times of occurrence

        <div class="description_limit">  
          Maximum of 10 requests per 6 <a href="/openapi/api-information#tag/Introduction/Rate-Limits">seconds</a> for all methods in the <a href="/openapi/promotion#tag/Promotions Calendar">Promotions Calendar</a> per one seller's account
        </div>
      parameters:
        - $ref: '#/components/parameters/startDateTime'
        - $ref: '#/components/parameters/endDateTime'
        - $ref: '#/components/parameters/allPromo'
        - $ref: '#/components/parameters/limitPromotion'
        - $ref: '#/components/parameters/offset'
      responses:
        '200':
          $ref: '#/components/responses/PromotionsSuccessResponse'
        '400':
          $ref: '#/components/responses/ErrorFailedParseData'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /api/v1/calendar/promotions/details:
    servers:
      - url: https://dp-calendar-api.wildberries.ru
    get:
      tags:
        - Promotions Calendar
      security:
        - HeaderApiKey: []
      summary: Promotions Details
      description: |
        Returns detailed information about the selected promotions

        <div class="description_limit">  
          Maximum of 10 requests per 6 <a href="/openapi/api-information#tag/Introduction/Rate-Limits">seconds</a> for all methods in the <a href="/openapi/promotion#tag/Promotions Calendar">Promotions Calendar</a> per one seller's account
        </div>
      parameters:
        - $ref: '#/components/parameters/promotionIDs'
      responses:
        '200':
          $ref: '#/components/responses/PromotionsGetByIDSuccessResponse'
        '400':
          $ref: '#/components/responses/ErrorFailedParseData'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /api/v1/calendar/promotions/nomenclatures:
    servers:
      - url: https://dp-calendar-api.wildberries.ru
    get:
      tags:
        - Promotions Calendar
      security:
        - HeaderApiKey: []
      summary: Products List for Participating in the Promotion
      description: |
        Returns a list of products suitable for participation in the promotion.

        Not applicable for auto promotions

        <div class="description_limit">  
          Maximum of 10 requests per 6 <a href="/openapi/api-information#tag/Introduction/Rate-Limits">seconds</a> for all methods in the <a href="/openapi/promotion#tag/Promotions Calendar">Promotions Calendar</a> per one seller's account
        </div>
      parameters:
        - $ref: '#/components/parameters/promotionID'
        - $ref: '#/components/parameters/inAction'
        - $ref: '#/components/parameters/limitNomenclature'
        - $ref: '#/components/parameters/offset'
      responses:
        '200':
          $ref: '#/components/responses/ResponsePromotionGoodsLists'
        '400':
          $ref: '#/components/responses/ErrorWrongParameters'
        '401':
          $ref: '#/components/responses/401'
        '422':
          $ref: '#/components/responses/ErrParameterValuesIncorrect'
        '429':
          $ref: '#/components/responses/429'
  /api/v1/calendar/promotions/upload:
    servers:
      - url: https://dp-calendar-api.wildberries.ru
    post:
      tags:
        - Promotions Calendar
      security:
        - HeaderApiKey: []
      summary: Add Product to the Promotion
      description: |
        Creates a product upload for the promotion.<br>The upload status can be checked using [separate methods](/openapi/work-with-products#tag/Prices-and-Discounts/paths/~1api~1v2~1history~1tasks/get).

        Not applicable for auto promotions

        <div class="description_limit">  
          Maximum of 10 requests per 6 <a href="/openapi/api-information#tag/Introduction/Rate-Limits">seconds</a> for all methods in the <a href="/openapi/promotion#tag/Promotions Calendar">Promotions Calendar</a> per one seller's account
        </div>
      requestBody:
        $ref: '#/components/requestBodies/PromotionSupplierTaskRequest'
      responses:
        '200':
          $ref: '#/components/responses/UploadSuccessResponse'
        '400':
          $ref: '#/components/responses/ErrorWrongParameters'
        '401':
          $ref: '#/components/responses/401'
        '422':
          $ref: '#/components/responses/UnprocessableEntity'
        '429':
          $ref: '#/components/responses/429'
components:
  schemas:
    V0AdvertMultibid:
      type: object
      required:
        - advert_id
        - nm_bids
      properties:
        advert_id:
          type: integer
          example: 6348555
          description: Campaign ID
        nm_bids:
          type: array
          description: WB article numbers and their bids
          items:
            $ref: '#/components/schemas/V0AdvertMultiBidItem'
    V0AdvertMultiBidItem:
      type: object
      required:
        - nm
        - bid
      properties:
        nm:
          type: integer
          example: 3462354
          description: WB article
        bid:
          type: integer
          example: 500
          description: Bid. Minimum allowable bids can be found in the response of the method for obtaining [configuration values](./promotion#tag/Sozdanie-kampanij/paths/~1adv~1v0~1config/get)
    V0GetConfigCategoriesResponse:
      type: object
      properties:
        id:
          type: integer
          description: Product category ID
        name:
          type: string
          description: Product category name
        cpm_min:
          type: integer
          description: Minimum allowable rate
      required:
        - id
        - name
        - cpm_min
      example:
        id: 760
        name: Автомобильные товары
        cpm_min: 112
    ResponseWithReturn:
      type: object
      properties:
        total:
          type: integer
          description: Amount of the updated budget
    RequestWithDate:
      type: array
      minItems: 1
      maxItems: 100
      items:
        type: object
        properties:
          id:
            description: Campaign ID
            type: integer
          dates:
            description: Dates for which information needs to be provided.
            type: array
            items:
              type: string
              format: date
    RequestWithCampaignID:
      type: array
      minItems: 1
      maxItems: 100
      items:
        type: object
        properties:
          id:
            description: Campaign ID
            type: integer
    RequestWithInterval:
      type: array
      minItems: 1
      maxItems: 100
      items:
        type: object
        properties:
          id:
            description: Campaign ID
            type: integer
          interval:
            description: The time period for which information needs to be provided.
            type: object
            properties:
              begin:
                description: Beginning of the requested period
                type: string
                format: date
              end:
                description: End of the requested period
                type: string
                format: date
    Days:
      description: Statistics by days
      type: array
      items:
        type: object
        properties:
          date:
            description: Date for which the data is presented
            type: string
            format: date-time
          views:
            type: integer
            description: Number of views
          clicks:
            type: integer
            description: Number of clicks
          ctr:
            type: number
            description: "Click-Through Rate, ratio of clicks to displays,\_%\n"
          cpc:
            type: number
            description: Average cost per click, ₽.
          sum:
            type: number
            description: Expenses, ₽
          atbs:
            type: integer
            description: Number of products added to the basket
          orders:
            type: integer
            description: Orders number
          cr:
            type: integer
            description: |
              CR(conversion rate) is the ratio of the number of orders to the total number of visits to the campaign.
          shks:
            type: integer
            description: Number of ordered products, pcs.
          sum_price:
            type: number
            description: Orders amount, ₽
          apps:
            description: Information block about the platform
            type: array
            items:
              type: object
              properties:
                views:
                  type: integer
                  description: Number of views
                clicks:
                  type: integer
                  description: Number of clicks
                ctr:
                  type: number
                  description: "Click-Through Rate, ratio of clicks to displays,\_%\n"
                cpc:
                  type: number
                  description: Average cost per click, ₽.
                sum:
                  type: number
                  description: Expenses, ₽
                atbs:
                  type: integer
                  description: Number of products added to the basket
                orders:
                  type: integer
                  description: Orders number
                cr:
                  type: integer
                  description: |
                    CR(conversion rate) is the ratio of the number of orders to the total number of visits to the campaign.
                shks:
                  type: integer
                  description: Number of ordered products, pcs.
                sum_price:
                  type: number
                  description: Orders amount, ₽
                apps:
                  description: Information block about WB articles
                  type: array
                  items:
                    type: object
                    properties:
                      views:
                        type: integer
                        description: Number of views
                      clicks:
                        type: integer
                        description: Number of clicks
                      ctr:
                        type: number
                        description: "Click-Through Rate, ratio of clicks to displays,\_%    \n"
                      cpc:
                        type: number
                        description: Average cost per click, ₽.
                      sum:
                        type: number
                        description: Expenses, ₽
                      atbs:
                        type: integer
                        description: Number of products added to the basket
                      orders:
                        type: integer
                        description: Orders number
                      cr:
                        type: integer
                        description: |
                          CR(conversion rate) is the ratio of the number of orders to the total number of visits to the campaign.
                      shks:
                        type: integer
                        description: Number of ordered products, pcs.
                      sum_price:
                        type: number
                        description: Orders amount, ₽
                      name:
                        description: Product name
                        type: string
                      nmId:
                        description: WB article ID
                        type: integer
                appType:
                  type: integer
                  description: Platform type (`1` - website, `32` - Android, `64` - IOS)
    ResponseInfoAdvert:
      type: object
      properties:
        endTime:
          description: Campaign end date
          type: string
        createTime:
          description: Campaign create time
          type: string
          example: '2022-03-09T10:50:21.831623+03:00'
        changeTime:
          description: Campaign last update time
          type: string
          example: '2022-12-22T18:24:19.808701+03:00'
        startTime:
          description: Campaign launch date
          type: string
        name:
          description: Campaign name
          type: string
        params:
          description: Campaign parameters
          type: array
          items:
            type: object
            properties:
              subjectName:
                description: Name of the object group (for the campaign in search and recommendations (<strong>deprecated campaign types</strong>))
                type: string
              active:
                description: The object group activity flag, <code>true</code> - active, <code>false</code> - inactive
                type: boolean
              intervals:
                description: Showing hours intervals
                type: array
                items:
                  type: object
                  properties:
                    Begin:
                      description: Show start time
                      type: integer
                    End:
                      description: Show end time
                      type: integer
              price:
                description: Current price
                type: integer
              menuId:
                description: Campaign target menu item ID (for catalogue campaign (<strong>deprecated campaign type</strong>))
                type: integer
              subjectId:
                description: Campaign target subject ID (for search and recommendations campaigns (<strong>deprecated campaign types</strong>))
                type: integer
              setId:
                description: Subject and gender selection ID (for product card campaign (<strong>deprecated campaign type</strong>))
                type: integer
              setName:
                description: Item and gender combination (for the campaign in the product card (<strong>deprecated campaign type</strong>))
                type: string
              menuName:
                description: Name of the menu where the campaign is placed (for a catalog campaign (<strong>deprecated campaign type</strong>))
                type: string
              nms:
                description: An array of campaign nomenclatures
                type: array
                items:
                  type: object
                  properties:
                    nm:
                      type: integer
                      description: WB article (`nmId`)
                    active:
                      type: boolean
                      description: Nomenclature status (<code>true</code> - active or <code>false</code> - inactive)
        dailyBudget:
          description: Daily budget; 0 if not set
          type: integer
        advertId:
          description: Campaign ID
          type: integer
          example: 1234
        status:
          description: |
            <dl> <dt>Campaign status:</dt> <dd><code>-1</code> - campaign
            in the process of removing </dd> <dd><code>4</code>
            - ready to start </dd> <dd><code>7</code> - is over</dd> <dd><code>8</code>
            - refused</dd> <dd><code>9</code> - active</dd> <dd><code>11</code> -
            paused</dd> </dl> Campaign in the process of removing.  The status means
            that the campaign has been deleted and in 3-10 minutes it will disappear
            from the method response.
          type: integer
          example: 9
        type:
          description: |
            <dl>
            <dt>Type of campaign:</dt>
            <dd><code>4</code> - campaign in catalogue (<strong>deprecated type</strong>)</dd>
            <dd><code>5</code> - campaign in content (<strong>deprecated type</strong>)</dd>
            <dd><code>6</code> - campaign in search (<strong>deprecated type</strong>)</dd>
            <dd><code>7</code> - campaign on main page recommendations (<strong>deprecated type</strong>)</dd> </dl>
          type: integer
          example: 4
        paymentType:
          type: string
          description: |
            Payment model:
            - `cpm` — for displays
        searchPluseState:
          description: |
            Fixed phrase activity:
              - `false` — inactive
              - `true` — active
          type: boolean
    ResponseInfoAdvertType8:
      type: object
      properties:
        endTime:
          description: Campaign end date
          type: string
          format: date-time
        createTime:
          description: Campaign create time
          type: string
          format: date-time
        changeTime:
          description: Campaign last update time
          type: string
          format: date-time
        startTime:
          description: Campaign launch date
          type: string
          format: date-time
        autoParams:
          type: object
          properties:
            subject:
              description: Promoted subject
              type: object
              properties:
                id:
                  description: Subject ID
                  type: integer
                name:
                  description: Subject name
                  type: string
            sets:
              description: Internal (system) entity (gender + product)
              type: array
              items:
                type: object
                properties:
                  id:
                    description: Set ID
                    type: integer
                  name:
                    description: Set name
                    type: string
            menus:
              type: array
              items:
                type: object
                properties:
                  id:
                    description: Menu ID
                    type: integer
                  name:
                    description: Menu name
                    type: string
            active:
              description: Display zones
              type: object
              properties:
                carousel:
                  description: In content (`false` means disabled, `true` means enabled)
                  type: boolean
                recom:
                  description: Recommendations on main page (`false` means disabled, `true` means enabled)
                  type: boolean
                booster:
                  description: Auction (`false` means disabled, `true` means enabled)
                  type: boolean
            nmCPM:
              type: array
              description: |
                Nomenclature bids (WB articles)
              items:
                type: object
                properties:
                  nm:
                    description: WB article
                    type: integer
                  cpm:
                    description: Bid
                    type: integer
            nms:
              description: WB articles (`nmId`)
              type: array
              items:
                type: integer
            cpm:
              description: |
                The bid specified when creating the campaign.<br>   The field is relevant only for campaigns created via API.
              type: integer
        name:
          description: Campaign name
          type: string
        dailyBudget:
          description: Not used
          type: integer
        advertId:
          description: Campaign ID
          type: integer
        status:
          description: |
            <dl> <dt>Campaign status:</dt> <dd><code>-1</code> - campaign
            in the process of removing </dd> <dd><code>4</code> - ready to run</dd> <dd><code>7</code> - is over</dd> <dd><code>8</code> - refused</dd> <dd><code>9</code> - active</dd> <dd><code>11</code> - paused</dd> </dl> Campaign in the process of removing.  The status means that the campaign has been deleted and in 3-10 minutes it will disappear from the method response.
          type: integer
        type:
          description: <dl> <dt>Type of campaign:</dt> <dd><code>8</code> - automatic campaign </dd> </dl>
          type: integer
        paymentType:
          type: string
          description: |
            Payment model:
            - `cpm` — for displays
    ResponseInfoAdvertType9:
      type: object
      properties:
        endTime:
          description: Campaign end date
          type: string
          format: date-time
        createTime:
          description: Campaign create time
          type: string
          format: date-time
        changeTime:
          description: Campaign last update time
          type: string
          format: date-time
        startTime:
          description: Campaign launch date
          type: string
          format: date-time
        searchPluseState:
          description: |
            Fixed phrase activity:
              - `false` — inactive
              - `true` — active
          type: boolean
        name:
          description: Campaign name
          type: string
        unitedParams:
          type: array
          items:
            type: object
            properties:
              subject:
                type: object
                description: Promoted subject
                properties:
                  id:
                    description: Subject ID
                    type: integer
                  name:
                    description: Subject name
                    type: string
              menus:
                type: array
                items:
                  type: object
                  description: Menu where the campaign is placed
                  properties:
                    id:
                      description: Menu ID
                      type: integer
                    name:
                      description: Menu name
                      type: string
              nms:
                description: WB articles (`nmId`)
                type: array
                items:
                  type: integer
              searchCPM:
                description: Bid in Search
                type: integer
              catalogCPM:
                description: Bid in Catalogue, if available
                type: integer
        dailyBudget:
          description: Not used
          type: integer
        advertId:
          description: Campaign ID
          type: integer
        status:
          description: |
            <dl> <dt>Campaign status:</dt> <dd><code>-1</code> - campaign
            in the process of removing </dd> <dd><code>4</code> - ready to run </dd> <dd><code>7</code> - is over</dd> <dd><code>8</code> - refused</dd> <dd><code>9</code> - active</dd> <dd><code>11</code> - paused</dd> </dl> Campaign in the process of removing.  The status means that the campaign has been deleted and in 3-10 minutes it will disappear from the method response.
          type: integer
        type:
          description: <dl> <dt>Type of campaign:</dt> <dd><code>9</code> - Auction </dd> </dl>
          type: integer
        paymentType:
          type: string
          description: |
            Payment model:
            - `cpm` — for displays
        auction_multibids:
          type: array
          description: WB articles bids
          items:
            type: object
            properties:
              nm:
                description: WB article
                type: integer
              cpm:
                description: Bid
                type: integer
    responseAdvError1:
      type: object
      properties:
        error:
          type: string
    PromotionGoodsList:
      type: object
      properties:
        id:
          type: integer
          example: 162579635
          description: Nomenclature ID
        inAction:
          type: boolean
          example: true
          description: |
            Participates in the promotion:
              - `true` — yes
              - `false` — no
        price:
          type: number
          format: float
          example: 1500
          description: Current retail price
        currencyCode:
          type: string
          example: RUB
          description: Currency in ISO 4217 format
        planPrice:
          type: number
          format: float
          example: 1000
          description: Planned price (price during the promotion)
        discount:
          type: integer
          example: 15
          description: Current discount
        planDiscount:
          type: integer
          example: 34
          description: Recommended discount for participating in the promotion
  responses:
    '401':
      description: Unauthorized
      content:
        application/json:
          schema:
            type: object
            properties:
              title:
                type: string
                description: Error title
              detail:
                type: string
                description: Error details
              code:
                type: string
                description: Internal error code
              requestId:
                type: string
                description: Unique request ID
              origin:
                type: string
                description: WB internal service ID
              status:
                type: number
                description: HTTP status code
              statusText:
                type: string
                description: Text of the HTTP status code
              timestamp:
                type: string
                format: RFC3339
                description: Request date and time
          example:
            title: unauthorized
            detail: 'token problem; token is malformed: could not base64 decode signature: illegal base64 data at input byte 84'
            code: 07e4668e--a53a3d31f8b0-[UK-oWaVDUqNrKG]; 03bce=277; 84bd353bf-75
            requestId: 7b80742415072fe8b6b7f7761f1d1211
            origin: s2s-api-auth-catalog
            status: 401
            statusText: Unauthorized
            timestamp: '2024-09-30T06:52:38Z'
    '429':
      description: Too many requests
      content:
        application/json:
          schema:
            type: object
            properties:
              title:
                type: string
                description: Error title
              detail:
                type: string
                description: Error details
              code:
                type: string
                description: Internal error code
              requestId:
                type: string
                description: Unique request ID
              origin:
                type: string
                description: WB internal service ID
              status:
                type: number
                description: HTTP status code
              statusText:
                type: string
                description: Text of the HTTP status code
              timestamp:
                type: string
                format: RFC3339
                description: Request date and time
          example:
            title: too many requests
            detail: limited by c122a060-a7fb-4bb4-abb0-32fd4e18d489
            code: 07e4668e-ac2242c5c8c5-[UK-4dx7JUdskGZ]
            requestId: 9d3c02cc698f8b041c661a7c28bed293
            origin: s2s-api-auth-catalog
            status: 429
            statusText: Too Many Requests
            timestamp: '2024-09-30T06:52:38Z'
    UploadSuccessResponse:
      description: Success
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: object
                properties:
                  alreadyExists:
                    description: Upload with this data already exists
                    type: boolean
                    example: false
                  uploadID:
                    description: Upload ID
                    type: integer
                    example: 11
    PromotionsGetByIDSuccessResponse:
      description: Success
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: object
                properties:
                  promotions:
                    type: array
                    description: Promotions list
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          description: Promotion ID
                          example: 123
                        name:
                          type: string
                          description: Promotion name
                          example: ХИТЫ ГОДА
                        description:
                          type: string
                          description: Promotion description
                          example: В акции принимают участие самые популярные товары 2023 года. Карточки товаров будут выделены плашкой «ХИТ ГОДА», чтобы покупатели замечали эти товары среди других. Также они будут размещены под баннерами на главной странице и примут участие в PUSH-уведомлениях. С ценами для вступления в акцию вы можете ознакомиться ниже.
                        advantages:
                          type: array
                          description: Promotion advantages
                          example:
                            - Badge
                            - Banner
                            - Top of product listings
                          items:
                            type: string
                        startDateTime:
                          type: string
                          example: '2023-06-05T21:00:00Z'
                          description: Promotion start
                        endDateTime:
                          type: string
                          example: '2023-06-05T21:00:00Z'
                          description: Promotion end
                        inPromoActionLeftovers:
                          description: Number of products with remaining stock participating in the promotion
                          type: integer
                          example: 45
                        inPromoActionTotal:
                          type: integer
                          description: Total number of products participating in the promotion
                          example: 123
                        notInPromoActionLeftovers:
                          type: integer
                          example: 3
                          description: Number of products with remaining stock that are not participating in the promotion
                        notInPromoActionTotal:
                          type: integer
                          example: 10
                          description: Total number of products that are not participating in the promotion
                        participationPercentage:
                          type: integer
                          description: Products already participating in the promotion, %. Calculation based on the products participating in the promotion and with the remaining stock
                          example: 10
                        type:
                          type: string
                          enum:
                            - regular
                            - auto
                          example: auto
                          description: |
                            Promotion type:
                              - `regular` — promotion
                              - `auto` — auto promotion
                        exceptionProductsCount:
                          type: integer
                          format: uint
                          example: 10
                          description: |
                            Number of products excluded from the auto promotion before it starts. Only for `"type": "auto"`.
                            <br>At the start of the promotion, these products will automatically be without a discount
                        ranging:
                          type: array
                          description: Ranking (if enabled)
                          items:
                            type: object
                            properties:
                              condition:
                                type: string
                                description: |
                                  Type of [ranking](https://seller.wildberries.ru/help-center/article/A-385):
                                    - `productsInPromotion` — only the seller's products participating in the promotion will be boosted
                                    - `calculateProducts` — any of the seller's products proposed for participation in the promotion will be boosted
                                    - `allProducts` — all of the seller's products will be boosted
                              participationRate:
                                type: integer
                                format: uint
                                minimum: 0
                                maximum: 100
                                description: Percentage of seller's products needed to advance to the next ranking level, %
                              boost:
                                type: integer
                                format: uint
                                description: Current search boost level, %
                          example:
                            - condition: productsInPromotion
                              participationRate: 10
                              boost: 7
                            - condition: calculateProducts
                              participationRate: 20
                              boost: 17
                            - condition: allProducts
                              participationRate: 35
                              boost: 30
    PromotionsSuccessResponse:
      description: Success
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: object
                properties:
                  promotions:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 123
                          description: Promotion ID
                        name:
                          type: string
                          example: скидки
                          description: Promotion name
                        startDateTime:
                          type: string
                          format: RFC3339
                          example: '2023-06-05T21:00:00Z'
                          description: Promotion start
                        endDateTime:
                          type: string
                          format: RFC3339
                          example: '2023-06-05T21:00:00Z'
                          description: Promotion end
                        type:
                          type: string
                          enum:
                            - regular
                            - auto
                          example: auto
                          description: |
                            Promotion type:
                              - `regular` — promotion
                              - `auto` — auto promotion
    ErrParameterValuesIncorrect:
      description: Error processing request parameters
      content:
        application/json:
          schema:
            type: object
            properties:
              errorText:
                type: string
                description: Error text
          examples:
            PromotionCompletedOrNotExist:
              description: The promotion has been completed, or the promotion with this id does not exist
              value:
                errorText: Unprocessable entity
    ErrorFailedParseData:
      description: Bad request
      content:
        application/json:
          schema:
            type: object
            properties:
              errorText:
                type: string
                description: Error text
                example: Failed to parse data
    ErrorWrongParameters:
      description: Bad request
      content:
        application/json:
          schema:
            type: object
            properties:
              errorText:
                type: string
                description: Error text
                example: Invalid query params
    UnprocessableEntity:
      description: Error processing request parameters
      content:
        application/json:
          schema:
            type: object
            properties:
              errorText:
                type: string
                example: Unprocessable entity
    ResponsePromotionGoodsLists:
      description: Success
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: object
                properties:
                  nomenclatures:
                    type: array
                    items:
                      $ref: '#/components/schemas/PromotionGoodsList'
  parameters:
    limitPromotion:
      in: query
      name: limit
      description: Number of requested promotions
      schema:
        type: integer
        format: uint
        minimum: 1
        maximum: 1000
        example: 10
    limitNomenclature:
      in: query
      name: limit
      description: Number of requested products
      schema:
        type: integer
        format: uint
        minimum: 1
        maximum: 1000
        example: 10
    startDateTime:
      in: query
      name: startDateTime
      description: Period start, format `YYYY-MM-DDTHH:MM:SSZ`
      required: true
      schema:
        type: string
        format: RFC3339
        example: '2023-09-01T00:00:00Z'
    endDateTime:
      in: query
      name: endDateTime
      description: Period end, format `YYYY-MM-DDTHH:MM:SSZ`
      required: true
      schema:
        type: string
        format: RFC3339
        example: '2024-08-01T23:59:59Z'
    allPromo:
      in: query
      name: allPromo
      description: |
        Show promotions:
          - `false` — available for participating
          - `true` — all promotion
      schema:
        type: boolean
        default: false
      required: true
    promotionID:
      in: query
      name: promotionID
      description: Promotion ID
      schema:
        type: integer
        example: 1
      required: true
    promotionIDs:
      in: query
      description: IDs of the promotions for which information should be returned
      name: promotionIDs
      required: true
      schema:
        type: string
        items:
          type: integer
        example:
          - 1
          - 3
          - 64
        uniqueItems: true
        minItems: 1
        maxItems: 100
    inAction:
      in: query
      name: inAction
      description: |
        Participates in the promotion:
          - `true` — yes
          - `false` — no
      schema:
        type: boolean
        example: true
        default: false
      required: true
    offset:
      in: query
      name: offset
      description: From which element to start outputting data
      schema:
        type: integer
        format: uint
        minimum: 0
        example: 0
  requestBodies:
    PromotionSupplierTaskRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: object
                properties:
                  promotionID:
                    type: integer
                    description: Promotion ID
                    example: 1
                    minimum: 1
                  uploadNow:
                    type: boolean
                    example: true
                    description: |
                      Set discount:
                        - `true` — now
                        - `false` — at the start of the promotion
                  nomenclatures:
                    type: array
                    description: Nomenclature IDs that can be added to the promotion
                    example:
                      - 1
                      - 3
                      - 642
                    items:
                      type: integer
                      minimum: 1
                    minItems: 1
                    maxItems: 1000
                    uniqueItems: true
  examples:
    CampaignIsNotUnique:
      description: Duplicate campaign ID. <br> The position of the campaign in the bids array of the request is indicated in `.bids[n]`
      value:
        errors:
          - detail: advert 1234567 is not unique
            field: .bids[2]
        request_id: 2c991dcab0fe971e8c0321c340a8c7fd
        status: 400
        title: invalid payload
        type: Bad Request
    CanNotDeserializeResponseBody:
      description: Invalid request content
      value:
        detail: can not deserialize response body
        origin: camp-api-public-cache
        request_id: 49d317c56e62373b52710251280cea76
        status: 400
        title: invalid payload
    CampaignNotFoundBids:
      description: Campaign not found. <br> The position of the campaign in the bids array of the request is indicated in `.bids[n]`
      value:
        errors:
          - detail: advert 22568519 not found
            field: .bids[0]
        request_id: 202707ea56b2e3ca7705ef00b1db4563
        status: 400
        title: invalid payload
        type: Bad Request
    WrongCampaignID:
      description: Incorrect campaign ID. <br> The position of the campaign in the bids array of the request is indicated in `.bids[n]`
      value:
        errors:
          - detail: 'Key: ''V0PatchAdvertsBidsJSONBody.Bids[0].AdvertId'' Error:Field validation for ''AdvertId'' failed on the ''gt'' tag'
            field: AdvertId
        request_id: af8a2f59dd49a037c2ed1f6dcb7abb99
        status: 400
        title: validation request params error
        type: bad request
    WrongCampaignStatus:
      description: Incorrect campaign status. <br> The position of the campaign in the bids array of the request is indicated in `.bids[n]`
      value:
        errors:
          - detail: 'wrong advert status: 7'
            field: .bids[0]
        request_id: 1dffc16267c1f9266a4d74e999f823b4
        status: 400
        title: invalid payload
        type: Bad Request
    WrongBidValue:
      description: Incorrect bid size. <br> The position of the campaign in the bids array of the request is indicated in `.bids[n]`, the position of the article with the incorrect bid is indicated in `.nm_bids[n]`. <br> The minimum and maximum bid values are specified in min and max, respectively.
      value:
        errors:
          - detail: 'wrong bid value: 100; min: 125 max:100000'
            field: .bids[0].nm_bids[0].bid
          - detail: 'wrong bid value: 100; min: 150 max:100000'
            field: .bids[1].nm_bids[0].bid
        request_id: dcf254d3eae270675cbaa1e2ff1cce60
        status: 400
        title: invalid payload
        type: Bad Request
    NmNotFound:
      description: Article not found. <br> The position of the campaign in the bids array of the request is indicated in `.bids[n]`, the position of the unfound article in this campaign is indicated in `.nm_bids[n]`.
      value:
        errors:
          - detail: nm 2406138149 not found or inactive
            field: .bids[0].nm_bids[0]
        request_id: 2ebb8590d15c6c70e74e9cc3a9dc260b
        status: 400
        title: invalid payload
        type: Bad Request
    ResponseWithReturn:
      description: Response when return is true
      value:
        total: 500
    SettingMinusPhrase:
      description: Setting minus phrases
      value:
        excluded:
          - first phrase
          - second phrase
    RemovingMinusPhrase:
      description: Removing minus phrases
      value:
        excluded: []
    RequestWithoutParam:
      description: Request without parameters
      value:
        - id: 107024
    RequestAggregate:
      description: Request with intervals and dates
      value:
        - id: 107024
          interval:
            begin: '2023-10-21'
            end: '2023-10-21'
        - id: 107024
          dates:
            - '2023-10-22'
            - '2023-10-26'
    RespStatMediaInterval:
      description: Response for interval queries
      value:
        - interval:
            begin: '2023-10-21'
            end: '2023-10-25'
          stats:
            - item_id: 62237
              item_name: Gloria Jeans
              category_name: Детям
              advert_type: 1
              place: 2
              views: 11849
              clicks: 209
              cr: 0.48
              ctr: 1.76
              date_from: '2023-10-21T00:00:00+03:00'
              date_to: '2023-10-27T23:59:59+03:00'
              subject_name: Одежда
              atbs: 4
              orders: 1
              price: 175000
              cpc: 837.32
              status: 6
              daily_stats:
                - date: '2023-10-21T00:00:00+03:00'
                  app_type_stats:
                    - app_type: 1
                      stats:
                        - views: 2017
                          clicks: 27
                          atbs: 1
                          ctr: 1.34
              expenses: 175000
              cr1: 1.91
              cr2: 25
    RespStatMediaDates:
      description: Response when requested with dates
      value:
        - dates:
            - '2023-10-26'
            - '2023-10-22'
          stats:
            - item_id: 62237
              item_name: Gloria Jeans
              category_name: Детям
              advert_type: 1
              place: 2
              views: 4584
              clicks: 74
              cr: 1.35
              ctr: 1.61
              date_from: '2023-10-21T00:00:00+03:00'
              date_to: '2023-10-27T23:59:59+03:00'
              subject_name: Одежда
              atbs: 2
              orders: 1
              price: 175000
              cpc: 2364.86
              status: 6
              daily_stats:
                - date: '2023-10-22T00:00:00+03:00'
                  app_type_stats:
                    - app_type: 1
                      stats:
                        - views: 2384
                          clicks: 33
                          atbs: 2
                          orders: 1
                          cr: 3.03
                          ctr: 1.38
              expenses: 175000
              cr1: 2.7
              cr2: 50
    RespStatMediaWithoutParam:
      description: Response to request without parameters
      value:
        - stats:
            - item_id: 62237
              item_name: Gloria Jeans
              category_name: Детям
              advert_type: 1
              place: 2
              views: 11849
              clicks: 209
              cr: 0.48
              ctr: 1.76
              date_from: '2023-10-21T00:00:00+03:00'
              date_to: '2023-10-27T23:59:59+03:00'
              subject_name: Одежда
              atbs: 4
              orders: 1
              price: 175000
              cpc: 837.32
              status: 6
              daily_stats:
                - date: '2023-10-21T00:00:00+03:00'
                  app_type_stats:
                    - app_type: 1
                      stats:
                        - views: 2017
                          clicks: 27
                          atbs: 1
                          ctr: 1.34
              expenses: 175000
              cr1: 1.91
              cr2: 25
    RespStatMediaAggregate:
      description: Response to requests with intervals and dates
      value:
        - interval:
            begin: '2023-10-21'
            end: '2023-10-25'
          stats:
            - item_id: 62237
              item_name: Gloria Jeans
              category_name: Детям
              advert_type: 1
              place: 2
              views: 11849
              clicks: 209
              cr: 0.48
              ctr: 1.76
              date_from: '2023-10-21T00:00:00+03:00'
              date_to: '2023-10-27T23:59:59+03:00'
              subject_name: Одежда
              atbs: 4
              orders: 1
              price: 175000
              cpc: 837.32
              status: 6
              daily_stats:
                - date: '2023-10-21T00:00:00+03:00'
                  app_type_stats:
                    - app_type: 1
                      stats:
                        - views: 2017
                          clicks: 27
                          atbs: 1
                          ctr: 1.34
              expenses: 175000
              cr1: 1.91
              cr2: 25
        - dates:
            - '2023-10-26'
            - '2023-10-22'
          stats:
            - item_id: 62237
              item_name: Gloria Jeans
              category_name: Детям
              advert_type: 1
              place: 2
              views: 4584
              clicks: 74
              cr: 1.35
              ctr: 1.61
              date_from: '2023-10-21T00:00:00+03:00'
              date_to: '2023-10-27T23:59:59+03:00'
              subject_name: Одежда
              atbs: 2
              orders: 1
              price: 175000
              cpc: 2364.86
              status: 6
              daily_stats:
                - date: '2023-10-22T00:00:00+03:00'
                  app_type_stats:
                    - app_type: 1
                      stats:
                        - views: 2384
                          clicks: 33
                          atbs: 2
                          orders: 1
                          cr: 3.03
                          ctr: 1.38
              expenses: 175000
              cr1: 2.7
              cr2: 50
        - stats:
            - item_id: 62237
              item_name: Gloria Jeans
              category_name: Детям
              advert_type: 1
              place: 2
              views: 11849
              clicks: 209
              cr: 0.48
              ctr: 1.76
              date_from: '2023-10-21T00:00:00+03:00'
              date_to: '2023-10-27T23:59:59+03:00'
              subject_name: Одежда
              atbs: 4
              orders: 1
              price: 175000
              cpc: 837.32
              status: 6
              daily_stats:
                - date: '2023-10-21T00:00:00+03:00'
                  app_type_stats:
                    - app_type: 1
                      stats:
                        - views: 2017
                          clicks: 27
                          atbs: 1
                          ctr: 1.34
              expenses: 175000
              cr1: 1.91
              cr2: 25
    CampaignNotFound:
      value: Campaign not found
    ChangingStatusToNewIsNotPossible:
      description: ''
      value: Changing status is not possible
    InsufficientFundsToResumeDisplays:
      value: Insufficient funds to resume displays. Please top up the campaign budget.
    DisableOrIncreaseDailyLimit:
      value: To resume displays, disable or increase the daily limit.
    TopUpCampaignBudget:
      value: Top up the campaign budget.
    ChooseSourceOfFundsDebiting:
      value: No source of funds debiting selected
    UnknownPaymentType:
      value: Unknown payment type
    InsufficientFunds:
      value: Insufficient funds
    CreatePlacement:
      value: Create a placement.
    TopUpCampaignBudgetToResumeDisplays2:
      value: Top up the campaign budget to resume displays
    ErrorInGettingItems:
      value: '"Error in getting items: {x}", Error details'
    TypePlacementShouldBeDisplays:
      value: The placement type must be based on displays
    ChangeOnlyPossibleForActiveCampaigns:
      value: Changes are only possible for active campaigns
    SmallCpm:
      value: '"The bid cannot be less than {x} rubles", Minimum bid'
    RequestWithDate:
      description: Request with dates
      value:
        - id: 8960367
          dates:
            - '2023-10-07'
            - '2023-10-06'
        - id: 9876543
          dates:
            - '2023-10-07'
            - '2023-12-06'
    RequestWithInterval:
      description: Request with intervals
      value:
        - id: 8960367
          interval:
            begin: '2023-10-08'
            end: '2023-10-10'
        - id: 78978565
          interval:
            begin: '2023-09-08'
            end: '2023-09-11'
    RequestWithoutIDParam:
      description: Request only with campaign IDs
      value:
        - id: 8960367
        - id: 9876543
    ResponseWithDate:
      description: Response for a request with the field `date`
      value:
        - views: 1052
          clicks: 2
          ctr: 0.19
          cpc: 0.09
          sum: 177.7
          atbs: 0
          orders: 0
          cr: 0
          shks: 0
          sum_price: 0
          dates:
            - '2023-10-07'
            - '2023-10-06'
          days:
            - date: '2023-10-06T03:00:00+03:00'
              views: 414
              clicks: 1
              ctr: 0.24
              cpc: 70
              sum: 70
              atbs: 0
              orders: 0
              cr: 0
              shks: 0
              sum_price: 0
              apps:
                - views: 228
                  clicks: 0
                  ctr: 0
                  cpc: 0
                  sum: 38.71
                  atbs: 0
                  orders: 0
                  cr: 0
                  shks: 0
                  sum_price: 0
                  nm:
                    - views: 25
                      clicks: 0
                      ctr: 0
                      cpc: 0
                      sum: 4
                      atbs: 0
                      orders: 0
                      cr: 0
                      shks: 0
                      sum_price: 0
                      name: Тапочки
                      nmId: **********11
                  appType: 1
          boosterStats:
            - date: '2023-10-07T00:00:00Z'
              nm: 170095908
              avg_position: 348
          advertId: 10524818
    ResponseWithInterval:
      description: Response for a request with the field `interval`
      value:
        - interval:
            begin: '2023-10-08'
            end: '2023-10-10'
          views: 1052
          clicks: 2
          ctr: 0.19
          cpc: 0.09
          sum: 177.7
          atbs: 0
          orders: 0
          cr: 0
          shks: 0
          sum_price: 0
          days:
            - date: '2023-10-08T03:00:00+03:00'
              views: 730
              clicks: 1
              ctr: 0.14
              cpc: 124.91
              sum: 124.91
              atbs: 0
              orders: 0
              cr: 0
              shks: 0
              sum_price: 0
              apps:
                - views: 424
                  clicks: 1
                  ctr: 0.24
                  cpc: 72.63
                  sum: 72.63
                  atbs: 0
                  orders: 0
                  cr: 0
                  shks: 0
                  sum_price: 0
                  nm:
                    - views: 424
                      clicks: 1
                      ctr: 0.24
                      cpc: 72.63
                      sum: 72.63
                      atbs: 0
                      orders: 0
                      cr: 0
                      shks: 0
                      sum_price: 0
                      name: Тапочки
                      nmId: **********111
                  appType: 1
          boosterStats:
            - date: '2023-10-08T00:00:00Z'
              nm: **********111
              avg_position: 395
          advertId: 10524818
    ResponseInvalidCampaignID:
      description: Invalid campaign identifier
      value:
        error: Invalid campaign identifier
    EmptyToken:
      description: Empty authorization header
      value:
        code: 401
        message: 07e4668e-a53a3d31f8b0-UKEhtT2359xje unauthorized, rejected; 32cf4a31-4760933a empty Authorization header
    InvalidToken:
      description: Invalid token
      value:
        code: 401
        message: 07e4668e-a53a3d31f8b0-UKPLv7jyL7H3F unauthorized, rejected; 84bd353bf bad token, malformed
    TokenScopeNotAllowedAdv:
      description: Token type not allowed for this method
      value:
        code: 401
        message: 07e4668e-a53a3d31f8b0-UK2u9L9CdmHVL unauthorized, rejected; 32cf4a31-506fc000 token scope not allowed for this API route
    StatusNoChangeAdv:
      value: Campaign status not changed
    IncorrectSupplierIdAdv:
      value: Incorrect seller identifier
    InvalidRcIdAdv:
      value: Incorrect campaign identifier (RC ID)
    IncorrectParamAdv:
      value: Incorrect value for parameter `param`
    IncorrectSubjectID:
      value: Incorrect value for parameter `subjectId`
    IncorrectTypeAdv:
      value: Incorrect value for parameter `type`
    IncorrectUsingMethods:
      description: Incorrect use of the method
      value: To obtain information, provide either a list of campaigns or a set of filters
    IncorrectStatusAdv:
      value: Incorrect value for parameter `status`
    IncorrectName:
      value: Incorrect name
    IncorrectCpmAdv:
      value: Incorrect value for parameter `cpm`
    AmountNotChanged:
      value: Bid size not changed
    ActivitySubjectGroupNotChanged:
      value: Activity of the subject group not changed
    CompanyNameChangeErr:
      value: Error changing the campaign name
    ErrorProcessRequestParam:
      value: Error processing request parameters
    RequestBodyProcessErrorAdv:
      value: Error processing request body
    CampaignNotBelongSeller:
      value: Campaign does not belong to the seller
    CampaignNotFoundAdv:
      value:
        error: Not found
    FailedGetAdvertTools:
      value: Failed to retrieve the activity of promotion tools
    TokenMissing:
      description: Token missing
      value: 'proxy: unauthorized'
    TokenInvalid:
      description: Invalid token
      value: 'proxy: invalid token'
    TokenNotFound:
      description: Token deleted
      value: 'proxy: not found'
    TopUpCampaignBudgetToResumeImpressions2:
      value: Top up the campaign budget to resume impressions
    TypePlacementShouldBeImpressions:
      value: The placement type should be based on impressions
    InsufficientFundsToResumeImpressions:
      value: Insufficient funds to resume impressions. Please top up the campaign budget
    ResponseInfoAdvert:
      value:
        - endTime: '2100-01-01 00:00:00+03:00'
          createTime: '2023-05-31 16:57:42.654141+03:00'
          changeTime: '2023-06-21 22:10:43.074183+03:00'
          startTime: '2023-07-21 21:17:42.872376+03:00'
          name: Носки_Шерстяные
          params:
            - intervals:
                - begin: 3
                  end: 5
              price: 400
              subjectId: 201
              subjectName: Носки
              nms:
                - nm: ********
                  active: true
              active: false
          dailyBudget: 0
          advertId: 12345
          status: 9
          type: 6
          paymentType: cpm
          searchPluseState: false
    ResponseInfoAdvertType8:
      value:
        - endTime: '2023-10-05T21:37:37.226021+03:00'
          createTime: '2023-08-21T13:45:31.121172+03:00'
          changeTime: '2023-08-21T14:59:33.622594+03:00'
          startTime: '2023-08-21T13:45:31.147601+03:00'
          autoParams:
            subject:
              name: Обложки
              id: 342
            sets:
              - name: для женщин
                id: 623
            nms:
              - 1234567
            active:
              carousel: true
              recom: true
              booster: true
            nmCPM:
              - nm: 1234567
                cpm: 150
          name: Кампания1
          dailyBudget: 0
          advertId: ********
          status: 7
          type: 8
          paymentType: cpm
    ResponseInfoAdvertType9:
      value:
        - endTime: '2100-01-01T00:00:00+03:00'
          createTime: '2023-07-09T10:56:02.150362+03:00'
          changeTime: '2023-07-11T02:48:44.767712+03:00'
          startTime: '2023-07-10T08:02:31.949155+03:00'
          searchPluseState: false
          name: Противовирусные препараты
          unitedParams:
            - subject:
                id: 3038
                name: Противовирусные препараты
              menus:
                - id: -1
                  name: ''
              nms:
                - 38995344
              searchCPM: 123
              catalogCPM: 321
          dailyBudget: 0
          advertId: 1234567
          status: 11
          type: 9
          paymentType: cpm
          auction_multibids:
            - nm: 38995344
              bid: 154
    ResponseInfoAdvertsAll:
      value:
        - endTime: '2100-01-01 00:00:00+03:00'
          createTime: '2023-05-31 16:57:42.654141+03:00'
          changeTime: '2023-06-21 22:10:43.074183+03:00'
          startTime: '2023-07-21 21:17:42.872376+03:00'
          name: Носки_Шерстяные
          params:
            - intervals:
                - begin: 3
                  end: 5
              price: 400
              subjectId: 201
              subjectName: Носки
              nms:
                - nm: ********
                  active: true
              active: false
          dailyBudget: 0
          advertId: 12345
          status: 9
          type: 6
          paymentType: cpm
          searchPluseState: false
        - endTime: '2023-10-05T21:37:37.226021+03:00'
          createTime: '2023-08-21T13:45:31.121172+03:00'
          changeTime: '2023-08-21T14:59:33.622594+03:00'
          startTime: '2023-08-21T13:45:31.147601+03:00'
          autoParams:
            subject:
              name: Обложки
              id: 342
            sets:
              - name: для женщин
                id: 623
            nms:
              - 1234567
            active:
              carousel: true
              recom: true
              booster: true
            nmCPM:
              - nm: 1234567
                cpm: 150
          name: Кампания1
          dailyBudget: 0
          advertId: ********
          status: 7
          type: 8
          paymentType: cpm
        - endTime: '2100-01-01T00:00:00+03:00'
          createTime: '2023-07-09T10:56:02.150362+03:00'
          changeTime: '2023-07-11T02:48:44.767712+03:00'
          startTime: '2023-07-10T08:02:31.949155+03:00'
          searchPluseState: false
          name: Противовирусные препараты
          unitedParams:
            - subject:
                id: 3038
                name: Противовирусные препараты
              menus:
                - id: -1
                  name: ''
              nms:
                - 38995344
              searchCPM: 123
              catalogCPM: 321
          dailyBudget: 0
          advertId: 1234567
          status: 11
          type: 9
          paymentType: cpm
  securitySchemes:
    HeaderApiKey:
      type: apiKey
      name: Authorization
      in: header
