openapi: 3.0.1
info:
  title: Analytics and Data
  version: analytics
  description: |
    Data on promotion statistics, and seller analytics.
  x-file-name: analytics
security:
  - HeaderApiKey: []
tags:
  - name: Promotion Statistics
    description: ''
  - name: Sales Funnel
    description: ''
  - name: Search Queries
    description: ''
  - name: Stocks Report
    description: ''
  - name: Seller Analytics CSV
    description: ''
paths:
  /adv/v2/fullstats:
    servers:
      - url: https://advert-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Campaigns statistics
      description: |
        Returns campaign statistics.
        <br>Data will be returned for campaigns with statuses 7, 9, and 11

        <div class="description_important">
          In the request, you can either pass the <code>dates</code> parameter or the <code>interval</code> parameter, but not both.<br> You can send a request with only the campaign ID. In this case, data for the last 24 hours will be returned, but not for the entire campaign period.
        </div>

        <div class="description_limit">  
          Maximum of 1 request per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account
        </div>
      tags:
        - Promotion Statistics
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/RequestWithDate'
                - $ref: '#/components/schemas/RequestWithInterval'
                - $ref: '#/components/schemas/RequestWithCampaignID'
            examples:
              RequestWithDate:
                $ref: '#/components/examples/RequestWithDate'
              RequestWithInterval:
                $ref: '#/components/examples/RequestWithInterval'
              RequestWithCampaignID:
                $ref: '#/components/examples/RequestWithoutIDParam'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/ResponseWithDate'
                  - $ref: '#/components/schemas/ResponseWithInterval'
              examples:
                ResponseWithDate:
                  $ref: '#/components/examples/ResponseWithDate'
                ResponseWithInterval:
                  $ref: '#/components/examples/ResponseWithInterval'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseAdvError1'
              examples:
                CampaignNotFoundAdv:
                  $ref: '#/components/examples/CampaignNotFoundAdv'
                responseIncorrectBeginDate:
                  $ref: '#/components/examples/responseIncorrectBeginDate'
                responseIncorrectEndDate:
                  $ref: '#/components/examples/responseIncorrectEndDate'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v2/auto/stat-words:
    servers:
      - url: https://advert-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Statistics of an automatic campaign by phrase clusters
      description: |
        <p>Returns clusters of key phrases (sets of similar ones) for which products were shown in the campaign, and the number of displays for them. Only those phrases for which products were shown at least once are included in the method's response.</p> <p>Information is updated every 15 minutes.</p>
        <div class="description_limit">  
          Maximum of 4 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Promotion Statistics
      parameters:
        - name: id
          in: query
          description: Campaign ID
          schema:
            type: integer
            example: 1234
          required: true
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  excluded:
                    description: Exclusions (negative phrases) for products from the campaign. These are phrases that you set using the "Set/Delete Negative Phrases" method or in the personal account, in the campaign settings.
                    type: array
                    items:
                      type: string
                    example:
                      - Samsung
                      - Xiaomi
                  clusters:
                    description: Key phrases clusters
                    type: array
                    items:
                      type: object
                      properties:
                        cluster:
                          description: Cluster — a set of similar key phrases
                          type: string
                          example: Phone
                        count:
                          description: The number of times products were shown for all phrases in the cluster
                          type: integer
                          example: 100
                        keywords:
                          description: Key phrases from the cluster for which products were shown at least once
                          type: array
                          items:
                            type: string
                          example:
                            - Телефон
                            - Мобильный телефон
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseAdvError1'
              examples:
                CampaignNotFoundAdv:
                  $ref: '#/components/examples/CampaignNotFoundAdv'
                AvailableOnlyForAutoCampaign:
                  $ref: '#/components/examples/AvailableOnlyForAutoCampaign'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/stat/words:
    servers:
      - url: https://advert-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Campaign statistics by key phrases
      description: |
        The method allows to get search campaign statistics by key phrases.<br>
        The information updates approximately every half hour.

        <div class="description_limit">  
          Maximum of 4 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Promotion Statistics
      parameters:
        - name: id
          in: query
          description: Campaign ID
          schema:
            type: integer
            example: 1
          required: true
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  words:
                    description: Key phrase information block"
                    type: object
                    properties:
                      phrase:
                        description: Phrase matching (minus phrases)
                        type: array
                        items:
                          type: string
                      strong:
                        description: Exact match (minus phrases)
                        type: array
                        items:
                          type: string
                      excluded:
                        description: Minus phrases from search
                        type: array
                        items:
                          type: string
                      pluse:
                        description: Fixed phrases
                        type: array
                        items:
                          type: string
                      keywords:
                        description: Dataset with statistics for key phrases
                        type: array
                        items:
                          type: object
                          properties:
                            keyword:
                              description: Key phrase
                              type: string
                            count:
                              description: Number of views by key phrase
                              type: integer
                      fixed:
                        description: |
                          Fixed key phrases (`true` — enabled, `false` — disabled)
                        type: boolean
                  stat:
                    description: |
                      An array of statistics.<br>
                      <b>The first element of the array</b> with `keyword: "Total by campaign"` contains summary information for all keyword phrases.<br>
                      <b>Each following element of the array</b> contains information on a separate key phrase.<br>
                      The 60 keyword phrases with the most number of views are displayed.
                    type: array
                    items:
                      type: object
                      properties:
                        advertId:
                          description: Campaign ID in the WB system
                          type: integer
                        keyword:
                          description: Key phrase
                          type: string
                        advertName:
                          description: Field permanently disabled
                          type: string
                        campaignName:
                          description: Name of campaign
                          type: string
                        begin:
                          description: Campaign launch date
                          type: string
                          format: date-time
                        end:
                          description: Campaign end date
                          type: string
                          format: date-time
                        views:
                          description: Number of views
                          type: integer
                        clicks:
                          description: Number of clicks
                          type: integer
                        frq:
                          description: Frequency (ratio of the number of views to the number of unique users)
                          type: integer
                        ctr:
                          description: |
                            "CTR (The ratio of clicks to displays. Expressed as a percentage)."
                          type: number
                        cpc:
                          description: CPC (Cost per click), RUB
                          type: number
                        duration:
                          description: Duration of the campaign, in seconds
                          type: integer
                        sum:
                          description: Expenses, RUB
                          type: number
              example:
                words:
                  phrase: []
                  strong: []
                  excluded: []
                  pluse:
                    - детское постельное белье для мальчика 1.5
                  keywords:
                    - keyword: постельное белье 1.5
                      count: 772
                  fixed: true
                stat:
                  - advertId: 7703570
                    keyword: Всего по кампании
                    advertName: ''
                    campaignName: Бельё
                    begin: '2023-07-03T15:15:38.287441+03:00'
                    end: '2023-07-03T15:15:38.287441+03:00'
                    views: 1846
                    clicks: 73
                    frq: 1.03
                    ctr: 3.95
                    cpc: 7.88
                    duration: 769159
                    sum: 575.6
                  - advertId: 7703570
                    keyword: постельное белье 1.5 детское
                    advertName: ''
                    campaignName: Бельё
                    begin: '2023-07-03T15:15:38.287441+03:00'
                    end: '2023-07-03T15:15:38.287441+03:00'
                    views: 1846
                    clicks: 73
                    frq: 1.03
                    ctr: 3.95
                    cpc: 7.88
                    duration: 769159
                    sum: 575.6
        '400':
          description: Bad request
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v0/stats/keywords:
    get:
      servers:
        - url: https://advert-api.wildberries.ru
      security:
        - HeaderApiKey: []
      tags:
        - Promotion Statistics
      summary: Statistics on keywords for Automatic campaigns and Auctions
      description: |
        Returns statistics on keywords for each day the campaign was active. 
        <br>
        Data can be retrieved for a maximum of 7 days in one request.
        <br> 
        Information is updated every hour.

        <div class="description_limit">  
          Maximum of 4 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      parameters:
        - in: query
          name: advert_id
          required: true
          description: Campaign ID
          schema:
            type: integer
          example: *********
        - in: query
          name: from
          required: true
          description: Period start
          schema:
            type: string
            format: date
          example: '2024-08-10'
        - in: query
          required: true
          name: to
          description: Period end
          schema:
            type: string
            format: date
          example: '2024-08-12'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/V0KeywordsStatisticsResponse'
              example:
                keywords:
                  - date: '2024-08-12'
                    stats:
                      - clicks: 68
                        ctr: 3.73
                        keyword: светильники
                        sum: 565.75
                        views: 1825
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                DateRangeExceeded:
                  $ref: '#/components/examples/DateRangeExceeded'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /adv/v1/stats:
    servers:
      - url: https://advert-media-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Media campaign statistics
      description: |
        The method allows to get statistics of media campaigns

        <div class="description_limit">  
          Maximum of 60 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">second</a> per one seller's account
        </div>
      tags:
        - Promotion Statistics
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/RequestWithDate'
                - $ref: '#/components/schemas/RequestWithInterval'
            examples:
              RequestWithDate:
                $ref: '#/components/examples/RequestWithDate'
              RequestWithInterval:
                $ref: '#/components/examples/RequestWithInterval'
              RequestWithoutParam:
                $ref: '#/components/examples/RequestWithoutParam'
              RequestAggregate:
                $ref: '#/components/examples/RequestAggregate'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  oneOf:
                    - $ref: '#/components/schemas/StatInterval'
                    - $ref: '#/components/schemas/StatDate'
                    - $ref: '#/components/schemas/Stat'
              examples:
                RespStatMediaInterval:
                  $ref: '#/components/examples/RespStatMediaInterval'
                RespStatMediaDates:
                  $ref: '#/components/examples/RespStatMediaDates'
                RespStatMediaWithoutParam:
                  $ref: '#/components/examples/RespStatMediaWithoutParam'
                RespStatMediaAggregate:
                  $ref: '#/components/examples/RespStatMediaAggregate'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/nm-report/detail:
    servers:
      - url: https://seller-analytics-api.wildberries.ru
    post:
      tags:
        - Sales Funnel
      summary: Retrieving product card (PC) statistics for a selected period, based on nmID/items/brands/labels
      description: |
        Retrieving statistics for product cards (PC) for a selected period, based on nmID/items/brands/labels.<br> The fields `brandNames`, `objectIDs`, `tagIDs`, `nmIDs` can be empty, in which case statistics for all product cards from the seller are included in the response.<br> When multiple fields are selected, the response includes data for cards that have all the selected fields. Pagination is supported.
        You can obtain a report for a maximum of one last year (365 days).<br>    
        Also, in the data where information about the previous period is provided:
          * In `previousPeriod`, the data is for the same period as in `selectedPeriod`.
          * If the start date of `previousPeriod` is earlier than a year ago from the current date, it will be adjusted as follows: `previousPeriod.start = current date — 365 days.`

        <div class="description_limit">  
          Maximum of 3 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account
        </div>
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NmReportDetailRequest'
        required: true
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NmReportDetailResponse'
              examples:
                NmReportDetailResponse:
                  value:
                    data:
                      page: 1
                      isNextPage: true
                      cards:
                        - nmID: 1234567
                          vendorCode: supplierVendor
                          brandName: Some
                          tags:
                            - id: 123
                              name: Sale
                          object:
                            id: 447
                            name: Кондиционеры для волос
                          statistics:
                            selectedPeriod:
                              begin: '2023-06-01 20:05:32'
                              end: '2024-03-01 20:05:32'
                              openCardCount: 0
                              addToCartCount: 0
                              ordersCount: 0
                              ordersSumRub: 0
                              buyoutsCount: 0
                              buyoutsSumRub: 0
                              cancelCount: 0
                              cancelSumRub: 0
                              avgPriceRub: 0
                              avgOrdersCountPerDay: 0
                              conversions:
                                addToCartPercent: 0
                                cartToOrderPercent: 0
                                buyoutsPercent: 0
                            previousPeriod:
                              begin: '2023-05-07 20:05:31'
                              end: '2023-06-01 20:05:31'
                              openCardCount: 0
                              addToCartCount: 0
                              ordersCount: 1
                              ordersSumRub: 1262
                              buyoutsCount: 1
                              buyoutsSumRub: 1262
                              cancelCount: 0
                              cancelSumRub: 0
                              avgPriceRub: 1262
                              avgOrdersCountPerDay: 0.04
                              conversions:
                                addToCartPercent: 0
                                cartToOrderPercent: 0
                                buyoutsPercent: 100
                            periodComparison:
                              openCardDynamics: 0
                              addToCartDynamics: 0
                              ordersCountDynamics: -100
                              ordersSumRubDynamics: -100
                              buyoutsCountDynamics: -100
                              buyoutsSumRubDynamics: -100
                              cancelCountDynamics: 0
                              cancelSumRubDynamics: 0
                              avgOrdersCountPerDayDynamics: 0
                              avgPriceRubDynamics: -100
                              conversions:
                                addToCartPercent: 0
                                cartToOrderPercent: 0
                                buyoutsPercent: -100
                          stocks:
                            stocksMp: 0
                            stocksWb: 0
                    error: true
                    errorText: ''
                    additionalErrors:
                      - field: string
                        description: string
                NmReportDetailResponseEmpty:
                  description: No product cards were found based on the request parameters
                  value:
                    data:
                      page: 0
                      isNextPage: false
                      cards: []
                    error: false
                    errorText: ''
                    additionalErrors: null
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseError'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseError'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/nm-report/detail/history:
    servers:
      - url: https://seller-analytics-api.wildberries.ru
    post:
      tags:
        - Sales Funnel
      summary: Retrieving product card statistics by days for selected nmID(s)
      description: |
        Retrieving product card statistics by days for selected `nmID`(s).
        You can obtain a report for a maximum of one last week. 
        To obtain reports for a period of up to one year, subscribe to [Jam extended analytics](https://seller.wildberries.ru/monetization/jam).
        <div class="description_limit">  
          Maximum of 3 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account
        </div>
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NmReportDetailHistoryRequest'
        required: true
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NmReportDetailHistoryResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseError'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseError'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/nm-report/grouped/history:
    servers:
      - url: https://seller-analytics-api.wildberries.ru
    post:
      tags:
        - Sales Funnel
      summary: Retrieving product card statistics by days for a period, grouped by items, brands, and tags
      description: |
        Retrieving product card statistics by days for a period, grouped by items, brands, and labels.<br> The fields `brandNames`, `objectIDs`, `tagIDs` can be left empty, in which case grouping is done for all seller's product cards.<br> In the request, the product, brand, and label count should not exceed 16.
        You can obtain a report for a maximum of one last week.
        To obtain reports for a period of up to one year, subscribe to [Jam extended analytics](https://seller.wildberries.ru/monetization/jam).
        <div class="description_limit">  
          Maximum of 3 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account
        </div>
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NmReportGroupedHistoryRequest'
        required: true
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NmReportGroupedHistoryResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseError'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseError'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/nm-report/downloads:
    servers:
      - url: https://seller-analytics-api.wildberries.ru
    post:
      tags:
        - Seller Analytics CSV
      security:
        - HeaderApiKey: []
      summary: Create the report
      description: |
        The method creates a task for generating a report with advanced seller analytics.
        <br><br>
        You can create a CSV-version of [sales funnel](/openapi/analytics#tag/Sales-Funnel) or [search parameters](/openapi/analytics#tag/Search-Queries) report with grouping:

          * by WB articles
          * by categories, brands, and labels

        In each of reports on sales funnel, you can group data by days, weeks, or months.
        <br><br>
        Also you can create a CSV-version of [search texts](/openapi/analytics#tag/Search-Queries/paths/~1api~1v2~1search-report~1product~1search-texts/post) or [stocks](/openapi/analytics#tag/Stocks-Report) report.
        <br><br>
        If it was not possible to [obtain report](/openapi/analytics#tag/Seller-Analytics-CSV/paths/~1api~1v2~1nm-report~1downloads~1file~1%7BdownloadId%7D/get), you can create a [repeat generation task](/openapi/analytics#tag/Seller-Analytics-CSV/paths/~1api~1v2~1nm-report~1downloads~1retry/post). You can also [get a list and check the statuses](/openapi/analytics#tag/Seller-Analytics-CSV/paths/~1api~1v2~1nm-report~1downloads/get) of reports.

        <div class="description_important">  
          <a href="https://seller.wildberries.ru/content-analytics/history-remains">Stocks report</a> — the <code>StocksReportReq</code> model — can be created without <a href="https://seller.wildberries.ru/monetization/jam">Jam</a> subscription
        </div>        

        <div class="description_limit">  
          Maximum of 3 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account, and maximum of 20 reports can be generated per day (only successful generations are counted)
        </div>
      requestBody:
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/SalesFunnelProductReq'
                - $ref: '#/components/schemas/SalesFunnelGroupReq'
                - $ref: '#/components/schemas/SearchReportGroupReq'
                - $ref: '#/components/schemas/SearchReportProductReq'
                - $ref: '#/components/schemas/SearchReportTextReq'
                - $ref: '#/components/schemas/StocksReportReq'
            examples:
              SalesFunnelProductReq:
                description: Sales funnel report. By WB articles
                value:
                  id: 06eae887-9d9f-491f-b16a-bb1766fcb8d2
                  reportType: DETAIL_HISTORY_REPORT
                  userReportName: Card report
                  params:
                    nmIDs:
                      - 1234567
                    subjectIDs:
                      - 1234567
                    brandNames:
                      - Name
                    tagIDs:
                      - 1234567
                    startDate: '2024-06-21'
                    endDate: '2024-06-23'
                    timezone: Europe/Moscow
                    aggregationLevel: day
                    skipDeletedNm: false
              SalesFunnelGroupReq:
                description: Sales funnel report. By categories, brands and labels
                value:
                  id: 06eea887-9d9f-491f-b16a-bb1766fcb8d2
                  reportType: GROUPED_HISTORY_REPORT
                  userReportName: Subject report
                  params:
                    subjectIDs:
                      - 1234567
                    brandNames:
                      - Name
                    tagIDs:
                      - 1234567
                    startDate: '2024-06-21'
                    endDate: '2024-06-23'
                    timezone: Europe/Moscow
                    aggregationLevel: day
                    skipDeletedNm: false
              SearchReportGroupReq:
                description: Search parameters report. By categories, brands, and labels
                value:
                  id: 06eae887-9d9f-491f-b16a-bb1766fcb8d2
                  reportType: SEARCH_QUERIES_PREMIUM_REPORT_GROUP
                  userReportName: Subject report
                  params:
                    currentPeriod:
                      start: '2024-02-10'
                      end: '2024-02-10'
                    pastPeriod:
                      start: '2024-02-08'
                      end: '2024-02-08'
                    nmIds:
                      - 162579635
                      - 166699779
                    subjectIds:
                      - 64
                      - 334
                    brandNames:
                      - nikkle
                      - abikas
                    tagIds:
                      - 32
                      - 53
                    orderBy:
                      field: avgPosition
                      mode: asc
                    positionCluster: all
              SearchReportProductReq:
                description: Search parameters report. By WB articles
                value:
                  id: 06eea887-9d9f-491f-b16a-bb1766fcb8d2
                  reportType: SEARCH_QUERIES_PREMIUM_REPORT_PRODUCT
                  userReportName: Card report
                  params:
                    currentPeriod:
                      start: '2024-02-10'
                      end: '2024-02-10'
                    pastPeriod:
                      start: '2024-02-08'
                      end: '2024-02-08'
                    subjectId: 123
                    brandName: Abble
                    tagId: 45
                    nmIds:
                      - 162579635
                      - 166699779
                    orderBy:
                      field: avgPosition
                      mode: asc
                    positionCluster: all
              SearchReportTextReq:
                description: Search text report
                value:
                  id: 06eae887-9d9f-491f-b16a-bb1766fcb8d2
                  reportType: SEARCH_QUERIES_PREMIUM_REPORT_TEXT
                  userReportName: Text report
                  params:
                    currentPeriod:
                      start: '2024-02-10'
                      end: '2024-02-10'
                    pastPeriod:
                      start: '2024-02-08'
                      end: '2024-02-08'
                    nmIds:
                      - 162579635
                      - 166699779
                    subjectIds:
                      - 64
                      - 334
                    brandNames:
                      - nikkle
                      - abikas
                    tagIds:
                      - 32
                      - 53
                    topOrderBy: openCard
                    orderBy:
                      field: avgPosition
                      mode: asc
                    limit: 30
              StocksReportReq:
                description: Stocks Report
                value:
                  id: 06eae887-9d9f-491f-b16a-bb1766fcb8d2
                  reportType: STOCK_HISTORY_REPORT_CSV
                  userReportName: Stocks report
                  params:
                    nmIDs:
                      - 111222333
                      - 444555666
                    subjectIDs:
                      - 123
                      - 456
                    brandNames:
                      - niikle
                      - zhikle
                    tagIDs:
                      - 3
                      - 4
                      - 5
                    currentPeriod:
                      start: '2024-02-10'
                      end: '2024-02-10'
                    stockType: mp
                    skipDeletedNm: true
                    availabilityFilters:
                      - deficient
                      - balanced
                    orderBy:
                      field: avgOrders
                      mode: asc
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NmReportCreateReportResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  title:
                    type: string
                    description: Error title
                  detail:
                    type: string
                    description: Error details
                  requestId:
                    type: string
                    description: Unique request ID
                  origin:
                    type: string
                    description: WB internal service ID
                required:
                  - title
                  - detail
                  - requestId
                  - origin
              examples:
                errorExample:
                  value:
                    title: Invalid request body
                    detail: userReportName can not contain emojis
                    requestId: 51b7828b-e298-4dfa-b7cd-45ab179921d3
                    origin: analytics-open-api
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                type: object
                properties:
                  title:
                    type: string
                    description: Error title
                  detail:
                    type: string
                    description: Error details
                  requestId:
                    type: string
                    description: Unique request ID
                  origin:
                    type: string
                    description: WB internal service ID
              examples:
                errorExample:
                  value:
                    title: Authorization error
                    detail: Authorization error
                    requestId: 51b7828b-e298-4dfa-b7cd-45ab179921d3
                    origin: analytics-open-api
        '429':
          description: Too many requests
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/response429Download'
                  - $ref: '#/components/schemas/response429DownloadDaily'
    get:
      tags:
        - Seller Analytics CSV
      security:
        - HeaderApiKey: []
      summary: Get the reports list
      description: |
        The method provides a list of reports with advanced seller analytics. The response contains [report IDs](/openapi/analytics#tag/Seller-Analytics-CSV/paths/~1api~1v2~1nm-report~1downloads/post) and generation statuses.

        <div class="description_limit">  
          Maximum of of 3 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account
        </div>
      parameters:
        - in: query
          name: filter[downloadIds]
          description: Report ID
          schema:
            type: array
            items:
              type: string
              format: uuid
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NmReportGetReportsResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  title:
                    type: string
                    description: Error title
                  detail:
                    type: string
                    description: Error details
                  requestId:
                    type: string
                    description: Unique request ID
                  origin:
                    type: string
                    description: WB internal service ID
                required:
                  - title
                  - detail
                  - requestId
                  - origin
              examples:
                errorExample:
                  value:
                    title: Invalid request body
                    detail: download id was not found
                    requestId: 51b7828b-e298-4dfa-b7cd-45ab179921d3
                    origin: analytics-open-api
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                type: object
                properties:
                  title:
                    type: string
                    description: Error title
                  detail:
                    type: string
                    description: Error details
                  requestId:
                    type: string
                    description: Unique request ID
                  origin:
                    type: string
                    description: WB internal service ID
              examples:
                errorExample:
                  value:
                    title: Authorization error
                    detail: Authorization error
                    requestId: 51b7828b-e298-4dfa-b7cd-45ab179921d3
                    origin: analytics-open-api
        '429':
          $ref: '#/components/responses/429'
  /api/v2/nm-report/downloads/retry:
    servers:
      - url: https://seller-analytics-api.wildberries.ru
    post:
      tags:
        - Seller Analytics CSV
      security:
        - HeaderApiKey: []
      summary: Regenerate the report
      description: |
        The method creates a [repeated generation task](/openapi/analytics#tag/Seller-Analytics-CSV/paths/~1api~1v2~1nm-report~1downloads/post) of report with advanced seller analytics. This is necessary if you [received the status](/openapi/analytics#tag/Seller-Analytics-CSV/paths/~1api~1v2~1nm-report~1downloads/get) `FAILED` when generating the report.

        <div class="description_limit">  
          Maximum of 3 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account
        </div>
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NmReportRetryReportRequest'
        required: true
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NmReportRetryReportResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  title:
                    type: string
                    description: Error title
                  detail:
                    type: string
                    description: Error details
                  requestId:
                    type: string
                    description: Unique request ID
                  origin:
                    type: string
                    description: WB internal service ID
                required:
                  - title
                  - detail
                  - requestId
                  - origin
              examples:
                errorExample:
                  value:
                    title: Invalid request body
                    detail: download id was not found
                    requestId: 51b7828b-e298-4dfa-b7cd-45ab179921d3
                    origin: analytics-open-api
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                type: object
                properties:
                  title:
                    type: string
                    description: Error title
                  detail:
                    type: string
                    description: Error details
                  requestId:
                    type: string
                    description: Unique request ID
                  origin:
                    type: string
                    description: WB internal service ID
              examples:
                errorExample:
                  value:
                    title: Authorization error
                    detail: Authorization error
                    requestId: 51b7828b-e298-4dfa-b7cd-45ab179921d3
                    origin: analytics-open-api
        '429':
          $ref: '#/components/responses/429'
  /api/v2/nm-report/downloads/file/{downloadId}:
    servers:
      - url: https://seller-analytics-api.wildberries.ru
    get:
      tags:
        - Seller Analytics CSV
      security:
        - HeaderApiKey: []
      summary: Get the report
      description: |
        The method provides a report with advanced seller analytics by [generation task](/openapi/analytics#tag/Seller-Analytics-CSV/paths/~1api~1v2~1nm-report~1downloads/post) ID.
        <br><br>
        You can get a report that was generated within the last 48 hours.<br>The report will be downloaded inside a ZIP archive in CSV format.<br>

        <div class="description_limit">  
          Maximum of 3 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account
        </div>
      parameters:
        - in: path
          name: downloadId
          description: Report ID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Success
          content:
            application/zip:
              schema:
                type: string
                format: binary
                description: "Fields description in the CSV file:\n<br>\n<br>\n**Sales funnel reports**\n\n| Name | Type | Format | Description |\n|-----------------| --- | --- | --- |\n| nmID (only `DETAIL_HISTORY_REPORT`) | integer | int32 | WB article |\n| dt | string | date | Date |\n| openCardCount | integer | int32 | Product page views |\n| addToCartCount | integer | int32 | Added to cart, quantity |\n| ordersCount | integer | int32 | Ordered products, quantity |\n| ordersSumRub | integer | int32 | Ordered amount, ₽ |\n| buyoutsCount | integer | int32 | Purchased products, quantity |\n| buyoutsSumRub | integer | int32 | Purchased amount, ₽ |\n| cancelCount | integer | int32 | Cancelled products, quantity |\n| cancelSumRub | integer | int32 | Cancelled amount, ₽ |\n| addToCartConversion | number | int32 | Cart conversion rate, % (Percentage of visitors who added the product to the cart after opening the product page) |\n| cartToOrderConversion | integer | int32 | Order conversion rate, % (Percentage of visitors who made an order after adding the product to the cart) |\n| buyoutPercent | integer | int32 | Purchase rate, % (Percentage of visitors who ordered the product and purchased it. Excluding products still being delivered to the buyer) |\n\n\n**Search parameters report. By categories, brands, and labels**\n\n| Name | Type | Format | Description |\n|-----------------| --- | --- | --- |\n| SubjectName |\tstring | string | Subject name |\n| SubjectID | integer | int32 | Subject ID |\n| BrandName | string | string | Brand |\n| TagID | integer | int64 | Label ID |\n| AveragePosition | integer | uint64 | Average product position in search results in the current period |\n| OpenCard | integer | uint64 | Number of transitions to the product card from search in the current period |\n| AddToCart | integer | uint64 | How many times the product from search was added to the cart in the current period |\n| OpenToCart | integer | uint64\t| Conversion to cart from search in the current period |\n| Orders | integer | uint64 | How many times products from search were ordered in the current period |\n| CartToOrder | integer | uint64 | Conversion to order from search in the current period |\n| Visibility  |integer | uint64 | Visibility in search results in the current period. Probability percentage that a user will see the product card. Depends on the average position |\n| AveragePositionPast | integer | uint64 | Average product position in search results in the past period (filled in if the previous period is specified) |\n| OpenCardPast | integer | uint64 | Number of transitions to the product card from search in the past period (filled in if the previous period is specified) |\n| AddToCartPast | integer | uint64 | How many times the product from search was added to the cart in the past period (filled in if the previous period is specified) |\n| OpenToCartPast | integer | uint64 | Conversion to cart from search in the past period filled in if the previous period is specified) |\n| OrdersPast | integer | uint64 | How many times products from search were ordered in the past period (filled in if the previous period is specified) |\n| CartToOrderPast | integer | uint64 | Conversion to order from search in the past period (filled in if the previous period is specified) |\n| VisibilityPast | integer | uint64 | Visibility in search results in the past period. Probability percentage that a user will see the product card. Depends on the average position (filled in if the previous period is specified) |\n\n\n**Search parameters report. By WB articles**\n\n| Name | Type | Format | Description |\n|-----------------| --- | --- | --- |     \n| NmID | integer | int64 | WB article |\n| VendorCode | string | string | Seller's article |\n| Name | string\t| string | Product name |\n| SubjectName | string\t| string | Subject name |\n| BrandName | string | string  | Brand |\n| IsAdvertised | boolean | bool\t| Is the product being promoted in search results |\n| IsRated | boolean | bool | Is there an opportunity to rate the product card quality |\n| Rating | float | float64 | Product card rating |\n| FeedbackRating | float | float64 | Feedbacks rating |\n| MinPrice | integer | uint64 | Minimal seller's price with seller's discount (excluding WB Club discount) |\n| MaxPrice | integer | uint64 | Maximal seller's price with seller's discount (excluding WB Club discount) |\n| AveragePosition | integer | uint64 | Average product position in search results in the current period |\n| OpenCard | integer | uint64 | Number of transitions to the product card from search in the current period |\n| AddToCart | integer | uint64 | How many times the product from search was added to the cart in the current period |\n| OpenToCart | integer | uint64\t| Conversion to cart from search in the current period |\n| Orders | integer | uint64 | How many times products from search were ordered in the current period |\n| CartToOrder | integer | uint64 | Conversion to order from search in the current period |\n| Visibility  |integer | uint64 | Visibility in search results in the current period. Probability percentage that a user will see the product card. Depends on the average position |\n| AveragePositionPast | integer | uint64 | Average product position in search results in the past period (filled in if the previous period is specified) |\n| OpenCardPast | integer | uint64 | Number of transitions to the product card from search in the past period (filled in if the previous period is specified) |\n| AddToCartPast | integer | uint64 | How many times the product from search was added to the cart in the past period (filled in if the previous period is specified) |\n| OpenToCartPast | integer | uint64 | Conversion to cart from search in the past period (filled in if the previous period is specified) |\n| OrdersPast | integer | uint64 | How many times products from search were ordered in the past period (filled in if the previous period is specified) |\n| CartToOrderPast | integer | uint64 | Conversion to order from search in the past period (filled in if the previous period is specified) |\n| VisibilityPast | integer | uint64 | Visibility in search results in the past period. Probability percentage that a user will see the product card. Depends on the average position (filled in if the previous period is specified) |\n\n\n**Search texts report**\n\n| Name | Type | Format | Description |\n|-----------------| --- | --- | --- |\n| Text | string\t| string | Search text |                         \n| NmID | integer | int64 | WB article |\n| SubjectName | string\t| string | Subject name |\n| BrandName | string | string  | Brand |                    \n| VendorCode | string | string | Seller's article |\n| Name | string\t| string | Product name |\n| Rating | float | float64 | Product card rating. If there is no rating, the value will be `no rating` |\n| FeedbackRating | float | float64 | Feedbacks rating |\n| MinPrice | integer | uint64 | Minimal seller's price with seller's discount (excluding WB Club discount) |\n| MaxPrice | integer | uint64 | Maximal seller's price with seller's discount (excluding WB Club discount) |\n| Frequency | integer | uint64 | Number of the search text requests in the current period |\n| MedianPosition | float | float64 | Median position of the product in search in the current period. Only positions from which users added the product to the cart or visited its page are considered. It is the middle value of the position in search results, which excludes significant deviations from the average |\n| AveragePosition | integer | uint64 | Average product position in search results in the current period. Only positions from which users added the product to the cart or visited its page are considered |\n| OpenCard | integer | uint64 | Number of views to the product card from search in the current period |\n| OpenCardPercentile | float | float64 | The percentage by which the number of product page views is higher than that of competitors' pages for the search text |\n| AddToCart | integer | uint64 | How many times the product in search results was added to the cart in the current period |\n| AddToCartPercentile | float | float64 | The percentage by which the number of addings to the cart is higher than that of competitors' pages for the search text |\n| OpenToCart | integer | uint64\t| Conversion to cart from search in the current period, % |\n| OpenToCartPercentile\t| float\t| float64\t| The percentage by which conversion to cart is higher than that of competitors' pages for the search text |\n| Orders | integer | uint64 | How many times products from search were ordered in the current period |\n| OrdersPercentile | float | float64 | The percentage by which the number of orders is higher than that of competitors' pages for the search text |\n| CartToOrder | integer | uint64 | Conversion to order from search in the current period, % |\n| CartToOrderPercentile | float | float64 | The percentage by which conversion to order is higher than that of competitors' pages for the search text |\n| Visibility  |integer | uint64 | Visibility in search results in the current period. Probability percentage that a user will see the product card. Depends on the average position |\n| FrequencyPast | integer | uint64 | Number of the search text requests in the past period (filled in if the previous period is specified) |\n| MedianPositionPast | float | float64 | Median position of the product in search in the past period (filled in if the previous period is specified) |\n| AveragePositionPast | integer | uint64 | Average product position in search results in the past period (filled in if the previous period is specified) |\n| OpenCardPast | integer | uint64 | Number of views to the product card from search in the past period (filled in if the previous period is specified) |\n| AddToCartPast | integer | uint64 | How many times the product from search was added to the cart in the past period (filled in if the previous period is specified) |\n| OpenToCartPast | integer | uint64 | Conversion to cart from search in the past period (filled in if the previous period is specified) |\n| OrdersPast | integer | uint64 | How many times products from search were ordered in the past period (filled in if the previous period is specified) |\n| CartToOrderPast | integer | uint64 | Conversion to order from search in the past period (filled in if the previous period is specified), % |\n| VisibilityPast | integer | uint64 | Visibility in search results in the past period. Probability percentage that a user will see the product card. Depends on the average position (filled in if the previous period is specified) |\n\n\n**Stocks report**\n\n| Name | Type | Format | Description |\n|-----------------| --- | --- | --- |\n| VendorCode | string | string | Seller's article |\n| Name | string\t| string | Product name |\n| NmID | integer | int64 | WB article |\n| SubjectName | string\t| string | Subject name |\n| BrandName | string | string  | Brand |\n| SizeName | string\t| string | Size name |\n| RegionName | string\t| string | Region name |\n| OfficeName | string\t| string | Warehouse name |\n| Availability | string\t| enum | Item availability status |\n| OrdersCount | integer\t| uint64 | Orders |\n| OrdersSum | integer\t| uint64 | Orders value |\n| BuyoutCount | integer\t| uint64 | Kept items |\n| BuyoutSum | integer\t| uint64 | Kept item value |\n| BuyoutPercent | integer\t| uint32 | Kept item rate |\n| AvgOrders | number\t| float64 | Average daily orders |\n| StockCount | integer\t| uint64 | Current inventory |\n| StockSum | integer\t| uint64 | Current inventory value |\n| SaleRate | integer\t| int32 |  Current DSI in hours |\n| AvgStockTurnover | integer\t| int32 | Average DSI in hours |\n| ToClientCount | integer\t| uint64 | On the way to user |\n| FromClientCount | integer\t| uint64 | On the way from user |\n| Price | integer\t| uint64 | Current seller's price with seller's discount (excluding WB Club discount) |\n| OfficeMissingTime | integer\t| int32 | Out-of-stock time in hours |\n| LostOrdersCount | number\t| float64 | Lost orders |\n| LostOrdersSum | number\t| float64 | Lost order value |\n| LostBuyoutsCount | number\t| float64 | Lost sales |\n| LostBuyoutsSum | number\t| float64 | Lost sale value |\n| AvgOrdersByMonth_MM.YYYY | number\t| float64 | The average number of orders by month. The columns are formed dynamically depending on the transmitted current period. Each month of the current period has one column. If the product did not exist at the time of a particular month, the value will be skipped. |\n"
              examples:
                SalesFunnelProductRes:
                  $ref: '#/components/examples/SalesFunnelProductRes'
                SalesFunnelGroupRes:
                  $ref: '#/components/examples/SalesFunnelGroupRes'
                SearchReportGroupRes:
                  $ref: '#/components/examples/SearchReportGroupRes'
                SearchReportProductRes:
                  $ref: '#/components/examples/SearchReportProductRes'
                SearchReportTextRes:
                  $ref: '#/components/examples/SearchReportTextRes'
                StocksReportRes:
                  $ref: '#/components/examples/StocksReportRes'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  title:
                    type: string
                    description: Error title
                  detail:
                    type: string
                    description: Error details
                  requestId:
                    type: string
                    description: Unique request ID
                  origin:
                    type: string
                    description: WB internal service ID
                required:
                  - title
                  - detail
                  - requestId
                  - origin
              examples:
                errorExample:
                  value:
                    title: Invalid request body
                    detail: download id was not found
                    requestId: 51b7828b-e298-4dfa-b7cd-45ab179921d3
                    origin: analytics-open-api
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                type: object
                properties:
                  title:
                    type: string
                    description: Error title
                  detail:
                    type: string
                    description: Error details
                  requestId:
                    type: string
                    description: Unique request ID
                  origin:
                    type: string
                    description: WB internal service ID
              examples:
                errorExample:
                  value:
                    title: Authorization error
                    detail: Authorization error
                    requestId: 51b7828b-e298-4dfa-b7cd-45ab179921d3
                    origin: analytics-open-api
        '429':
          $ref: '#/components/responses/429'
  /api/v2/search-report/report:
    servers:
      - url: https://seller-analytics-api.wildberries.ru
    post:
      tags:
        - Search Queries
      summary: Main Page
      description: |
        Forms a dataset for the main report page with:
          - General information
          - Product positions
          - Data on visibility and transitions to the product card
          - Data for the table by groups

        To obtain additional data in the table, use a separate request for:
          - Pagination by groups
          - Retrieval of products within a group

        Additional parameters for selecting the list of products in the table:
          - `positionCluster` — average position in search
            
        <div class="description_limit">  
          Maximum of 3 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account
        </div>
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MainRequest'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CommonResponseProperties'
                  - type: object
                    required:
                      - data
                    properties:
                      data:
                        $ref: '#/components/schemas/MainResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorObject400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorObject403'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/search-report/table/groups:
    servers:
      - url: https://seller-analytics-api.wildberries.ru
    post:
      tags:
        - Search Queries
      summary: Pagination by Groups
      description: |
        Pagination by groups in the report. It is possible only if there is a filter by brand, subject, or tag.

        Additional parameters for selecting the list of products in the table:
          - `positionCluster` — average position in search
            
        <div class="description_limit">  
          Maximum of 3 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account
        </div>         
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TableGroupRequest'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CommonResponseProperties'
                  - type: object
                    required:
                      - data
                    properties:
                      data:
                        $ref: '#/components/schemas/TableGroupResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorObject400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorObject403'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/search-report/table/details:
    servers:
      - url: https://seller-analytics-api.wildberries.ru
    post:
      tags:
        - Search Queries
      summary: Pagination by Products Within a Group
      description: |
        Pagination by products within a group. It is possible regardless of the presence of filters.

        Filters for pagination by products within a group or without filters:
          - tuple `subjectId`, `brandName`, `tagId` — filter for the group
          - `nmIds` — filter by nomenclature

        Additional parameters for selecting the list of products in the table:
          - `positionCluster` — average position in search
            
        <div class="description_limit">  
          Maximum of 3 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account
        </div>           
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TableDetailsRequest'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CommonResponseProperties'
                  - type: object
                    required:
                      - data
                    properties:
                      data:
                        $ref: '#/components/schemas/TableDetailsResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorObject400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorObject403'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/search-report/product/search-texts:
    servers:
      - url: https://seller-analytics-api.wildberries.ru
    post:
      tags:
        - Search Queries
      summary: Search Texts by Product
      description: |
        Forms the top search texts by product.

        Search text selection parameters:
          - `limit` — number of queries, maximum 30
          - `topOrderBy` — method for selecting the top queries
            
        <div class="description_limit">  
          Maximum of 3 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account
        </div>     
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductSearchTextsRequest'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CommonResponseProperties'
                  - type: object
                    required:
                      - data
                    properties:
                      data:
                        $ref: '#/components/schemas/ProductSearchTextsResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorObject400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorObject403'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/search-report/product/orders:
    servers:
      - url: https://seller-analytics-api.wildberries.ru
    post:
      tags:
        - Search Queries
      summary: Orders and Positions by Product Search Texts
      description: |
        Forms data for a table on the number of orders and positions by queries. The data is specified within a period for a specific product

        <div class="description_limit">  
          Maximum of 3 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account
        </div>     
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductOrdersRequest'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CommonResponseProperties'
                  - type: object
                    required:
                      - data
                    properties:
                      data:
                        $ref: '#/components/schemas/ProductOrdersResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorObject400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorObject403'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/stocks-report/products/groups:
    servers:
      - url: https://seller-analytics-api.wildberries.ru
    post:
      tags:
        - Stocks Report
      summary: Group data
      description: |
        Forms a dataset for inventory by product group.
        <br><br>
        The product group is described by a tuple of `subjectID, brandName, tagID`.
            
        <div class="description_limit">  
          Maximum of 3 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account
        </div>
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TableGroupRequestSt'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/TableGroupResponseSt'
                required:
                  - data
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorObject400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorObject403'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/stocks-report/products/products:
    servers:
      - url: https://seller-analytics-api.wildberries.ru
    post:
      tags:
        - Stocks Report
      summary: Product data
      description: |
        Forms a dataset for inventory by products.
        <br><br>
        You can get data for individual products as well as for the entire report if there are no filters in the query: `nmIDs`, `subjectID`, `brandName`, `tagID`.

        <div class="description_limit">  
          Maximum of 3 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account
        </div>
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TableProductRequest'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/TableProductResponse'
                required:
                  - data
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorObject400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorObject403'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/stocks-report/products/sizes:
    servers:
      - url: https://seller-analytics-api.wildberries.ru
    post:
      tags:
        - Stocks Report
      summary: Size data
      description: |
        Forms a dataset for inventory by the size of the product.
        <br><br>
        Possible cases:
        1. The product has dimensions and `"includeOffice":true`, then the response body will contain data on the inventory for each of the sizes with nested details by warehouse.
        2. The product has dimensions and `"includeOffice":false`, then the response body will contain data on the inventory for each of the sizes without nested details by warehouse.
        3. The product has no size and `"include Office":true`, then the response body will contain details by warehouse without data on the inventory for each of the sizes.
        4. The product has no size and `"include Office":false`, then the response body will be empty.<br></br>
        <code>The product has no size</code> means the size of the product is the same and has `"techSize":"0"`. In responses of the method for obtaining data on [products](/openapi/analytics#tag/Stocks-Report/paths/~1api~1v2~1stocks-report~1products~1products/post), such products have `hasSizes':false`.
        <br><br>
        The data on the seller's warehouses (FBS) are in an aggregated form — for all of them together without detailing specific warehouses —  and responses contain `"regionName":"Маркетплейс"` and `"officeName":""` in such cases.

        <div class="description_limit">  
          Maximum of 3 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account
        </div>
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TableSizeRequest'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/TableSizeResponse'
                required:
                  - data
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorObject400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorObject403'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/stocks-report/offices:
    servers:
      - url: https://seller-analytics-api.wildberries.ru
    post:
      tags:
        - Stocks Report
      summary: Warehouse data
      description: |
        Forms a dataset for inventory by warehouses.
        <br><br>
        The data on the seller's warehouses (FBS) are in an aggregated form — for all of them together without detailing specific warehouses —  and responses contain `"regionName":"Маркетплейс"` and `"offices":[]`.
        <div class="description_limit">  
          Maximum of 3 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account
        </div>        
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TableShippingOfficeRequest'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/TableShippingOfficeResponse'
                required:
                  - data
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorObject400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorObject403'
        '429':
          $ref: '#/components/responses/429'
components:
  schemas:
    MainRequest:
      type: object
      description: |
        Request parameters for forming the main page:
          - `currentPeriod` — current period
          - `pastPeriod` — past period for comparison
      properties:
        currentPeriod:
          $ref: '#/components/schemas/Period'
        pastPeriod:
          $ref: '#/components/schemas/pastPeriod'
        nmIds:
          type: array
          example:
            - 162579635
            - 166699779
          description: List of WB article numbers for filtering
          items:
            type: integer
            format: int32
        subjectIds:
          type: array
          example:
            - 32
            - 64
          description: List of subject IDs for filtering
          items:
            type: integer
            format: int32
        brandNames:
          example:
            - Adidas
            - Nike
          type: array
          description: List of brands for filtering
          items:
            type: string
        tagIds:
          example:
            - 3
            - 5
            - 6
          description: List of label IDs for filtering
          type: array
          items:
            type: integer
            format: int64
        positionCluster:
          $ref: '#/components/schemas/PositionCluster'
        orderBy:
          $ref: '#/components/schemas/OrderBy'
        limit:
          example: 130
          type: integer
          description: Number of product groups in the response
          format: uint32
          maximum: 1000
        offset:
          example: 50
          type: integer
          description: From which element to start outputting data
          format: uint32
      required:
        - currentPeriod
        - orderBy
        - positionCluster
        - limit
        - offset
    MainResponse:
      type: object
      properties:
        commonInfo:
          $ref: '#/components/schemas/CommonInfo'
        positionInfo:
          $ref: '#/components/schemas/PositionInfo'
        visibilityInfo:
          $ref: '#/components/schemas/VisibilityInfo'
        groups:
          description: |
            Table elements list
          type: array
          items:
            $ref: '#/components/schemas/TableGroupItem'
      required:
        - commonInfo
        - positionInfo
        - visibilityInfo
    CommonInfo:
      type: object
      properties:
        supplierRating:
          description: Seller rating
          type: object
          properties:
            current:
              type: number
              format: float64
              example: 5.3
              description: Current seller rating
            dynamics:
              type: number
              format: float64
              example: 5.4
              description: Dynamics compared to the previous period, %
          required:
            - current
        advertisedProducts:
          description: Number of advertised products
          type: object
          properties:
            current:
              example: 5
              description: Current number of advertised products
              type: integer
            dynamics:
              example: 50
              description: Dynamics compared to the previous period, %
              type: integer
          required:
            - current
        totalProducts:
          example: 150
          description: Total number of products
          type: integer
          format: uint64
      required:
        - supplierRating
        - advertisedProducts
        - totalProducts
    PositionInfo:
      type: object
      description: Product position information
      properties:
        average:
          description: Average product position in search results
          type: object
          properties:
            current:
              example: 5
              description: Current average product position
              type: integer
            dynamics:
              example: 50
              description: Dynamics compared to the previous period, %
              type: integer
          required:
            - current
        median:
          description: Median product position in search results
          type: object
          properties:
            current:
              example: 5
              description: Current median product position
              type: integer
            dynamics:
              example: 50
              description: Dynamics compared to the previous period, %
              type: integer
          required:
            - current
        chartItems:
          type: array
          description: Data for the chart on the average and median position of the product in search results
          items:
            $ref: '#/components/schemas/SearchReportPositionChartItem'
        clusters:
          $ref: '#/components/schemas/SearchReportPositionClusters'
      required:
        - average
        - median
        - chartItems
        - clusters
    SearchReportPositionChartItem:
      type: object
      properties:
        dt:
          example: '2024-10-19'
          type: string
          description: Data
        average:
          example: 1
          type: integer
          description: Average product position in search results
          format: uint64
        median:
          example: 1
          type: integer
          description: Median product position in search results
          format: uint64
      required:
        - dt
        - average
        - median
    SearchReportPositionClusters:
      type: object
      description: |
        Number of products with an average search position:
          - `firstHundred` — from 1 to 100
          - `secondHundred` — from 101 to 200
          - `below` — from 201 and below
      properties:
        firstHundred:
          description: from 1 to 100
          type: object
          properties:
            current:
              example: 5
              description: Current number of products
              type: integer
            dynamics:
              example: 50
              description: Dynamics compared to the previous period, %
              type: integer
          required:
            - current
        secondHundred:
          description: from 101 to 200
          type: object
          properties:
            current:
              example: 5
              description: Current number of products
              type: integer
            dynamics:
              example: 50
              description: Dynamics compared to the previous period, %
              type: integer
          required:
            - current
        below:
          description: from 201 and below
          type: object
          properties:
            current:
              example: 5
              description: Current number of products
              type: integer
            dynamics:
              example: 50
              description: Dynamics compared to the previous period, %
              type: integer
          required:
            - current
      required:
        - firstHundred
        - secondHundred
        - below
    VisibilityInfo:
      type: object
      description: Visibility of cards and transitions to cards. By days, weeks, months
      properties:
        visibility:
          description: Probability percentage that a user will see the product card. Depends on the average position
          type: object
          properties:
            current:
              example: 5
              description: Visibility in search results in the current period
              type: integer
            dynamics:
              example: 50
              description: Dynamics compared to the previous period, %
              type: integer
          required:
            - current
        openCard:
          description: Number of transitions to the product card from search
          type: object
          properties:
            current:
              example: 5
              description: Current number of transitions
              type: integer
            dynamics:
              example: 50
              description: Dynamics compared to the previous period, %
              type: integer
          required:
            - current
        byDay:
          type: array
          description: Data for plotting a chart in the personal account on visibility and transitions to cards by day
          items:
            type: object
            properties:
              dt:
                $ref: '#/components/schemas/Date'
              visibility:
                example: 100
                description: Card visibility in search results, %
                type: integer
                format: uint64
              open:
                example: 124
                description: Number of transitions to the product card
                type: integer
                format: uint64
            required:
              - dt
              - visibility
              - open
        byWeek:
          type: array
          description: Data for plotting a chart in the personal account on visibility and transitions to cards by week
          items:
            type: object
            properties:
              dt:
                $ref: '#/components/schemas/Date'
              visibility:
                example: 100
                description: Card visibility in search results, %
                type: integer
                format: uint64
              open:
                example: 124
                description: Number of transitions to the product card
                type: integer
                format: uint64
            required:
              - dt
              - visibility
              - open
        byMonth:
          type: array
          description: Data for plotting a chart in the personal account on visibility and transitions to cards by month
          items:
            type: object
            properties:
              dt:
                $ref: '#/components/schemas/Date'
              visibility:
                example: 100
                description: Card visibility in search results, %
                type: integer
                format: uint64
              open:
                example: 124
                description: Number of transitions to the product card
                type: integer
                format: uint64
            required:
              - dt
              - visibility
              - open
      required:
        - visibility
        - openCard
        - chartItems
    TableGroupItem:
      type: object
      description: |
        A group of products includes all cards that match at least one of the following parameters:
          - `subjectName` — name of the subject
          - `brandName` — name of the brand
          - `tagName` — name of the tag
      properties:
        subjectName:
          example: Phones
          description: Subject name
          type: string
        subjectId:
          example: 50
          description: Subject ID
          type: integer
          format: uint64
        brandName:
          example: Apple
          description: Brand
          type: string
        tagName:
          example: phones
          description: Label name
          type: string
        tagId:
          example: 65
          description: Label ID
          type: integer
          format: int64
        metrics:
          type: object
          description: Product metrics in the table
          properties:
            avgPosition:
              description: Average product position in search results
              type: object
              properties:
                current:
                  example: 5
                  description: Current average position
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
              required:
                - current
            openCard:
              description: Number of transitions to the product card from search
              type: object
              properties:
                current:
                  example: 5
                  description: Current number of transitions
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
              required:
                - current
            addToCart:
              description: How many times the product from search was added to the cart
              type: object
              properties:
                current:
                  example: 5
                  description: Current number
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
              required:
                - current
            openToCart:
              description: Conversion to cart from search — the ratio of product additions to the cart compared to all transitions to the product card from the search
              type: object
              properties:
                current:
                  example: 5
                  description: Current conversion
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
              required:
                - current
            orders:
              description: How many times products from search were ordered
              type: object
              properties:
                current:
                  example: 5
                  description: Current number
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
              required:
                - current
            cartToOrder:
              description: Conversion to order from search — the ratio of product orders to all additions of the product from search to the cart
              type: object
              properties:
                current:
                  example: 5
                  description: Current conversion
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
              required:
                - current
            visibility:
              description: Product visibility percent in search results
              type: object
              properties:
                current:
                  example: 5
                  description: Current visibility percent
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
              required:
                - current
          required:
            - avgPosition
            - openCard
            - addToCart
            - openToCart
            - orders
            - cartToOrder
            - visibility
        items:
          type: array
          description: List of the group products
          items:
            $ref: '#/components/schemas/TableProductItem'
      required:
        - metrics
        - items
    TableProductItem:
      type: object
      allOf:
        - type: object
          properties:
            nmId:
              example: 268913787
              description: WB article
              type: integer
              format: int64
            name:
              example: iPhone 13 256 ГБ Серебристый
              description: Product name
              type: string
            vendorCode:
              example: wb3ha2668w
              description: Seller's article
              type: string
            subjectName:
              example: Смартфоны
              description: Subject name
              type: string
            brandName:
              example: Apple
              description: Brand
              type: string
            mainPhoto:
              example: https://basket-12.wbbasket.ru/vol1788/part178840/178840836/images/c246x328/1.webp
              description: URL of the main photo of the product card
              type: string
            isAdvertised:
              example: false
              description: Is the product being promoted in search results
              type: boolean
            isCardRated:
              example: true
              description: If the product card has a rating
              type: boolean
            rating:
              example: 6
              description: Product card rating
              type: number
              format: float64
            feedbackRating:
              example: 1
              description: Feedbacks rating
              type: number
              format: float64
            price:
              description: Price
              type: object
              properties:
                minPrice:
                  example: 150
                  description: Minimal price
                  type: integer
                  format: uint64
                maxPrice:
                  example: 300
                  description: Maximal price
                  type: integer
                  format: uint64
              required:
                - minPrice
                - maxPrice
            avgPosition:
              description: Average product position in search results
              type: object
              properties:
                current:
                  example: 5
                  description: Current average position
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
              required:
                - current
            openCard:
              description: Number of transitions to the product card from search
              type: object
              properties:
                current:
                  example: 5
                  description: Current number of transitions
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
              required:
                - current
            addToCart:
              description: How many times the product from search was added to the cart
              type: object
              properties:
                current:
                  example: 5
                  description: Current number
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
              required:
                - current
            openToCart:
              description: Conversion to cart from search — the ratio of product additions to the cart compared to all transitions to the product card from the search
              type: object
              properties:
                current:
                  example: 5
                  description: Current conversion
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
              required:
                - current
            orders:
              description: How many times products from search were ordered
              type: object
              properties:
                current:
                  example: 5
                  description: Current number
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
              required:
                - current
            cartToOrder:
              description: Conversion to order from search — the ratio of product orders to all additions of the product from search to the cart
              type: object
              properties:
                current:
                  example: 5
                  description: Current conversion
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
              required:
                - current
            visibility:
              description: Product visibility percent in search results
              type: object
              properties:
                current:
                  example: 5
                  description: Current visibility percent
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
              required:
                - current
      required:
        - nmId
        - vendorCode
        - isAdvertised
        - isCardRated
        - rating
        - feedbackRating
        - price
        - avgPosition
        - openCard
        - addToCart
        - openToCart
        - orders
        - cartToOrder
        - visibility
    TableGroupRequest:
      type: object
      description: |
        Request parameters for pagination by groups:
          - `currentPeriod` — current period
          - `pastPeriod` — past period for comparison
      properties:
        currentPeriod:
          $ref: '#/components/schemas/Period'
        pastPeriod:
          $ref: '#/components/schemas/pastPeriod'
        nmIds:
          example:
            - 162579635
            - 166699779
          type: array
          description: List of WB article numbers for filtering
          items:
            type: integer
            format: int32
        subjectIds:
          example:
            - 64
            - 334
          type: array
          description: List of subject IDs for filtering
          items:
            type: integer
            format: int32
        brandNames:
          example:
            - nike
            - adidas
          type: array
          description: List of brands for filtering
          items:
            type: string
        tagIds:
          example:
            - 32
            - 53
          description: List of label IDs for filtering
          type: array
          items:
            type: integer
            format: int64
        orderBy:
          $ref: '#/components/schemas/OrderByGrTe'
        positionCluster:
          $ref: '#/components/schemas/PositionCluster'
        limit:
          example: 130
          type: integer
          description: Number of product groups in the response
          format: uint32
          maximum: 1000
        offset:
          example: 50
          type: integer
          description: From which element to start outputting data
          format: uint32
      required:
        - currentPeriod
        - orderBy
        - positionCluster
        - limit
        - offset
    TableGroupResponse:
      type: object
      properties:
        groups:
          description: |
            List of product groups for the table
          type: array
          items:
            $ref: '#/components/schemas/TableGroupItem'
      required:
        - groups
    TableDetailsRequest:
      description: |
        Request parameters for pagination by products within a group:
          - `currentPeriod` — current period
          - `pastPeriod` — past period for comparison
      type: object
      properties:
        currentPeriod:
          $ref: '#/components/schemas/Period'
        pastPeriod:
          $ref: '#/components/schemas/pastPeriod'
        subjectId:
          example: 123
          description: Subject ID
          type: integer
          format: int32
        brandName:
          example: Apple
          description: Product name
          type: string
        tagId:
          example: 45
          description: Label ID
          type: integer
          format: int64
        nmIds:
          example:
            - 162579635
            - 166699779
          type: array
          maxItems: 50
          description: WB article numbers list
          items:
            type: integer
            format: uint64
        orderBy:
          $ref: '#/components/schemas/OrderBy'
        positionCluster:
          description: |
            Which average search position of products to display in the report:
              - `all` — all
              - `firstHundred` — from 1 to 100
              - `secondHundred` — from 101 to 200
              - `below` — from 201 and below
          type: string
          enum:
            - all
            - firstHundred
            - secondHundred
            - below
        limit:
          example: 150
          description: Number of products in the response
          type: integer
          format: uint32
          maximum: 1000
        offset:
          example: 100
          description: From which element to start outputting data
          type: integer
          format: uint32
      required:
        - currentPeriod
        - orderBy
        - positionCluster
        - limit
        - offset
    TableDetailsResponse:
      type: object
      properties:
        products:
          description: |
            List of products in a group by filter
          type: array
          items:
            $ref: '#/components/schemas/TableProductItem'
      required:
        - products
    ProductSearchTextsRequest:
      description: |
        Parameters for the search text ranking request:
          - `currentPeriod` — current period
          - `pastPeriod` — past period for comparison
      type: object
      properties:
        currentPeriod:
          $ref: '#/components/schemas/Period'
        pastPeriod:
          $ref: '#/components/schemas/pastPeriod'
        nmIds:
          example:
            - 162579635
            - 166699779
          type: array
          description: WB article numbers list
          maxItems: 50
          items:
            type: integer
            format: uint64
        topOrderBy:
          example: openToCart
          description: |
            Sorting by field:
              - `openCard` — transitioned to the product card from search
              - `addToCart` — added to the cart from search
              - `openToCart` — conversion to cart from search
              - `orders` — ordered products from search
              - `cartToOrder` — conversion to order from search
          type: string
          enum:
            - openCard
            - addToCart
            - openToCart
            - orders
            - cartToOrder
        orderBy:
          $ref: '#/components/schemas/OrderByGrTe'
        limit:
          $ref: '#/components/schemas/TextLimit'
      required:
        - currentPeriod
        - nmIds
        - limit
        - topOrderBy
        - orderBy
    ProductSearchTextsResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/TableSearchTextItem'
      required:
        - items
    TableSearchTextItem:
      type: object
      allOf:
        - type: object
          properties:
            text:
              description: Search text
              example: костюм
              type: string
            nmId:
              example: 211131895
              description: WB article
              type: integer
              format: uint64
            subjectName:
              example: Phones
              description: Subject name
              type: string
            brandName:
              example: Apple
              description: Brand
              type: string
            vendorCode:
              example: wb3ha2668w
              description: Seller's article
              type: string
            name:
              example: iPhone 13 256 ГБ Серебристый
              description: Product name
              type: string
            isCardRated:
              example: true
              description: If the product card has a rating
              type: boolean
            rating:
              example: 6
              description: Product card rating
              type: number
              format: float64
            feedbackRating:
              example: 1
              description: Feedbacks rating
              type: number
              format: float64
            price:
              description: Price
              type: object
              properties:
                minPrice:
                  example: 150
                  description: Minimal seller's price with seller's discount (excluding WB Club discount)
                  type: integer
                  format: uint64
                maxPrice:
                  example: 300
                  description: Maximal seller's price with seller's discount (excluding WB Club discount)
                  type: integer
                  format: uint64
              required:
                - minPrice
                - maxPrice
            frequency:
              description: Number of the search text requests
              type: object
              properties:
                current:
                  example: 5
                  description: Current number
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
              required:
                - current
            weekFrequency:
              description: Number of the search text requests per week
              example: 140
              type: integer
              format: uint64
            medianPosition:
              description: Median position. Only positions from which users added the product to the cart or visited its page are considered. It is the middle value of the position in search results, which excludes significant deviations from the average
              type: object
              properties:
                current:
                  example: 5
                  description: Current number
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
              required:
                - current
            avgPosition:
              description: Average product position in search results
              type: object
              properties:
                current:
                  example: 5
                  description: Current average position
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
              required:
                - current
            openCard:
              description: Number of transitions to the product card from search
              type: object
              properties:
                current:
                  example: 5
                  description: Current number of transitions
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
                percentile:
                  example: 50
                  description: The percentage by which the number of product page views is higher than that of competitors' pages for the search text
                  type: integer
                  format: uint64
              required:
                - current
                - percentile
            addToCart:
              description: How many times the product from search was added to the cart
              type: object
              properties:
                current:
                  example: 5
                  description: Current number
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
                percentile:
                  example: 50
                  description: The percentage by which the number of addings to the cart is higher than that of competitors' pages for the search text
                  type: integer
                  format: uint64
              required:
                - current
                - percentile
            openToCart:
              description: Conversion to cart from search — the ratio of product additions to the cart compared to all transitions to the product card from the search
              type: object
              properties:
                current:
                  example: 5
                  description: Current conversion
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
                percentile:
                  example: 50
                  description: The percentage by which conversion to cart is higher than that of competitors' pages for the search text
                  type: integer
                  format: uint64
              required:
                - current
                - percentile
            orders:
              description: How many times products from search were ordered
              type: object
              properties:
                current:
                  example: 5
                  description: Current number
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
                percentile:
                  example: 50
                  description: The percentage by which the number of orders is higher than that of competitors' pages for the search text
                  type: integer
                  format: uint64
              required:
                - current
                - percentile
            cartToOrder:
              description: Conversion to order from search — the ratio of product orders to all additions of the product from search to the cart
              type: object
              properties:
                current:
                  example: 5
                  description: Current conversion
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
                percentile:
                  example: 50
                  description: The percentage by which conversion to order is higher than that of competitors' pages for the search text
                  type: integer
                  format: uint64
              required:
                - current
                - percentile
            visibility:
              description: Product visibility percent in search results
              type: object
              properties:
                current:
                  example: 5
                  description: Current percent
                  type: integer
                dynamics:
                  example: 50
                  description: Dynamics compared to the previous period, %
                  type: integer
              required:
                - current
          required:
            - text
            - nmId
            - subjectName
            - brandName
            - vendorCode
            - name
            - isCardRated
            - rating
            - feedbackRating
            - price
            - frequency
            - weekFrequency
            - medianPosition
            - avgPosition
            - openCard
            - addToCart
            - openToCart
            - orders
            - cartToOrder
            - visibility
    ProductOrdersRequest:
      type: object
      properties:
        period:
          $ref: '#/components/schemas/PeriodOrdersRequest'
        nmId:
          example: 211131895
          description: WB article
          type: integer
          format: uint64
        searchTexts:
          example:
            - костюм
            - пиджак
          description: Search texts
          type: array
          items:
            type: string
          minItems: 1
          maxItems: 30
      required:
        - period
        - nmId
        - searchTexts
    ProductOrdersResponse:
      type: object
      properties:
        total:
          description: Total for products
          type: array
          items:
            $ref: '#/components/schemas/ProductOrdersMetrics'
        items:
          description: Table elements
          type: array
          items:
            $ref: '#/components/schemas/ProductOrdersTextItem'
      required:
        - total
        - items
    ProductOrdersTextItem:
      type: object
      properties:
        text:
          description: Search text
          type: string
        frequency:
          description: Number of the search text requests
          type: integer
          format: uint64
        dateItems:
          description: Statistics by dates
          type: array
          items:
            $ref: '#/components/schemas/ProductOrdersMetrics'
      required:
        - text
        - frequency
        - dateItems
    ProductOrdersMetrics:
      type: object
      properties:
        dt:
          type: string
          description: Date of statistics collection
          format: date
          example: '2024-02-10'
        avgPosition:
          example: 10
          description: Average product position in search results
          type: integer
          format: uint64
        orders:
          example: 20
          description: How many times products from search were ordered
          type: integer
          format: uint64
      required:
        - dt
        - avgPosition
        - orders
    PositionCluster:
      example: all
      description: |
        Which average search position of products to display in the report:
          - `all` — all
          - `firstHundred` — from 1 to 100
          - `secondHundred` — from 101 to 200
          - `below` — from 201 and below
      type: string
      enum:
        - all
        - firstHundred
        - secondHundred
        - below
    OrderBy:
      type: object
      description: Sorting parameters
      properties:
        field:
          description: |
            Field for sorting:
              - `avgPosition` — by average position
              - `addToCart` — by cart additions
              - `openCard` — by card openings (transitions to product page)
              - `orders` — by number of orders
              - `cartToOrder` — by conversion to order from search
              - `openToCart` — by conversion to cart from search
              - `visibility` — by product visibility
              - `minPrice` — by minimum price
              - `maxPrice` — by maximum price
          example: avgPosition
          type: string
          enum:
            - avgPosition
            - openCard
            - addToCart
            - openToCart
            - orders
            - cartToOrder
            - visibility
            - minPrice
            - maxPrice
        mode:
          type: string
          example: asc
          description: |
            Sorting order:
              - `asc` — ascending
              - `desc` — descending
          enum:
            - asc
            - desc
      required:
        - field
        - mode
    OrderByGrTe:
      type: object
      description: Soring parameters
      properties:
        field:
          description: |
            Field for sorting:
              - `avgPosition` — by average position
              - `addToCart` — by cart additions
              - `openCard` — by card openings (transitions to product page)
              - `orders` — by number of orders
              - `cartToOrder` — by conversion to order from search
              - `openToCart` — by conversion to cart from search
              - `visibility` — by product visibility
              - `minPrice` — by minimum price
              - `maxPrice` — by maximum price
          example: avgPosition
          type: string
          enum:
            - avgPosition
            - openCard
            - addToCart
            - openToCart
            - orders
            - cartToOrder
            - visibility
            - minPrice
            - maxPrice
        mode:
          type: string
          example: asc
          description: |
            Order of sorting:
              - `asc` — ascending
              - `desc` — descending
          enum:
            - asc
            - desc
      required:
        - field
        - mode
    TextLimit:
      description: Number of search texts for the product
      type: integer
      format: uint64
      example: 20
      minimum: 1
      maximum: 30
    Date:
      type: string
      description: Date
      format: date
      example: '2024-02-10'
    PeriodOrdersRequest:
      type: object
      description: Current period. Maximum of 7 days
      required:
        - start
        - end
      properties:
        start:
          type: string
          description: Start date of the period. No later than `end`. No earlier than 365 days from today
          format: date
          example: '2024-02-10'
        end:
          type: string
          description: End date of the period. No earlier than 365 days from today
          format: date
          example: '2024-02-10'
    Period:
      type: object
      description: Current period
      required:
        - start
        - end
      properties:
        start:
          type: string
          description: Start date of the period. No later than `end`. No earlier than 365 days from today
          format: date
          example: '2024-02-10'
        end:
          type: string
          description: End date of the period. No earlier than 365 days from today
          format: date
          example: '2024-02-10'
    pastPeriod:
      type: object
      description: Previous period for comparison. Number of days — less than or equal to `currentPeriod`
      required:
        - start
        - end
      properties:
        start:
          type: string
          description: Start date of the period. No later than `end`. No earlier than 365 days from today
          format: date
          example: '2024-02-08'
        end:
          type: string
          description: End date of the period. No later than the day before the start date `currentPeriod`. No earlier than 365 days from today
          format: date
          example: '2024-02-08'
    CommonResponseProperties:
      type: object
      description: Request result
      properties:
        data:
          type: object
    ErrorObject400:
      type: object
      properties:
        title:
          type: string
          description: Error title
          example: Invalid request body
        detail:
          type: string
          description: Error details
          example: 'code=400, message=invalid: positionCluster (field required), limit (field required), offset (field required), internal=invalid: positionCluster (field required), limit (field required), offset (field required'
        requestId:
          type: string
          description: Unique request ID
          example: fb25c9e9-cae8-52db-b68e-736c1466a3f5
        origin:
          type: string
          description: Internal WB service ID
          example: analytic-open-api
      required:
        - title
        - detail
        - requestId
        - origin
    ErrorObject403:
      type: object
      properties:
        title:
          type: string
          description: Error title
          example: Authorization error
        detail:
          type: string
          description: Error details
          example: Authorization error
        requestId:
          type: string
          description: Unique request ID
          example: fb25c9e9-cae8-52db-b68e-736c1466a3f5
        origin:
          type: string
          description: Internal WB service ID
          example: analytic-open-api
      required:
        - title
        - detail
        - requestId
        - origin
    responseError:
      type: object
      properties:
        data:
          type: array
          items:
            type: object
          example: null
        error:
          description: Error flag
          type: boolean
          example: true
        errorText:
          description: Error description
          type: string
          example: Error Text
        additionalErrors:
          description: Additional errors
          nullable: true
          type: array
          items:
            type: object
            properties:
              field:
                description: The structure where the error occurred
                type: string
              description:
                description: Description
                type: string
    NmReportDetailRequest:
      type: object
      required:
        - page
        - period
      properties:
        brandNames:
          type: array
          description: Brands
          items:
            type: string
            example: Some
        objectIDs:
          type: array
          description: Item Identifiers
          items:
            type: integer
            format: int32
            example: 358
        tagIDs:
          type: array
          description: Numeric label identifiers
          items:
            type: integer
            format: int32
            example: 123
        nmIDs:
          type: array
          description: WB articles
          items:
            type: integer
            format: int32
            example: 1234567
        timezone:
          type: string
          description: Timezone.<br> If not specified, the default is Europe/Moscow
          example: Europe/Moscow
        period:
          type: object
          description: Period
          properties:
            begin:
              description: Start of the Period
              type: string
              format: time-date
              example: '2023-06-01 20:05:32'
            end:
              description: End of the Period
              type: string
              format: time-date
              example: '2024-03-01 20:05:32'
        orderBy:
          description: Sorting parameters. If not specified, the default is "openCard" with descending order. <dl> <dt>All sorting options <code>field</code>:</dt> <dd><code>openCard</code> — by card opening (transition to the product page)</dd> <dd><code>addToCart</code> — by additions to the cart</dd> <dd><code>orders</code> — by the number of orders</dd> <dd><code>avgRubPrice</code> — by average price in rubles</dd> <dd><code>ordersSumRub</code> — by the total order amount in rubles</dd> <dd><code>stockMpQty</code> — by the quantity of marketplace stock (pieces)</dd> <dd><code>stockWbQty</code> — by the quantity of warehouse stock (pieces)</dd> <dd><code>cancelSumRub</code> — by the sum of returns in rubles</dd> <dd><code>cancelCount</code> — by the number of returns</dd> <dd><code>buyoutCount</code> — by the number of buyouts</dd> <dd><code>buyoutSumRub</code> — by the total buyout amount in rubles</dd> </dl>
          type: object
          properties:
            field:
              description: Sort type
              type: string
              example: ordersSumRub
            mode:
              description: '`asc` — ascending, `desc` — descending'
              type: string
              example: asc
        page:
          description: Page
          type: integer
          format: int32
          example: 1
    NmReportDetailHistoryRequest:
      type: object
      required:
        - nmIDs
        - period
      properties:
        nmIDs:
          description: WB article (maximum 20)
          type: array
          items:
            type: integer
            format: int32
            example: 1234567
        period:
          description: Period
          type: object
          properties:
            begin:
              description: Start of the period
              type: string
              format: date
              example: '2023-06-20'
            end:
              description: End of the Period
              type: string
              format: date
              example: '2023-06-22'
        timezone:
          type: string
          description: Timezone.<br> If not specified, the default is Europe/Moscow
          example: Europe/Moscow
        aggregationLevel:
          description: |
            Aggregation Type. If not specified, the default is aggregation
            by days. <br> Available aggregation levels: `day`, `week`, `month`
          type: string
          example: day
    NmReportGroupedHistoryRequest:
      type: object
      required:
        - period
      properties:
        objectIDs:
          type: array
          description: Item Identifiers
          items:
            type: integer
            format: int32
            example: 358
        brandNames:
          description: Brands
          type: array
          items:
            type: string
            example: Some
        tagIDs:
          description: Numeric label identifiers
          type: array
          items:
            type: integer
            format: int32
            example: 123
        period:
          description: Period
          type: object
          properties:
            begin:
              description: Start of the Period
              type: string
              format: date
              example: '2023-06-21'
            end:
              description: End of the Period
              type: string
              format: date
              example: '2023-06-23'
        timezone:
          description: Timezone.<br> If not specified, the default is Europe/Moscow
          type: string
          example: Europe/Moscow
        aggregationLevel:
          description: |
            Aggregation Type. If not specified, the default is aggregation
          type: string
          example: day
    NmReportDetailResponse:
      type: object
      properties:
        data:
          type: object
          properties:
            page:
              description: Page
              type: integer
              format: int32
              example: 1
            isNextPage:
              description: Is there a next page (false — no, true — yes)
              type: boolean
            cards:
              type: array
              items:
                type: object
                properties:
                  nmID:
                    description: WB article
                    type: integer
                    format: int32
                    example: 1234567
                  vendorCode:
                    description: Seller's SKU
                    type: string
                    example: supplierVendor
                  brandName:
                    description: Brand
                    type: string
                    example: Some
                  tags:
                    description: Labels
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          description: Numeric label identifier
                          type: integer
                          format: int32
                          example: 123
                        name:
                          description: Label name
                          type: string
                          example: Sale
                  object:
                    description: Item
                    type: object
                    properties:
                      id:
                        description: Item Identifier
                        type: integer
                        format: int32
                        example: 447
                      name:
                        description: Item Name
                        type: string
                        example: Hair Conditioners
                      statistics:
                        description: Statistics
                        type: object
                        properties:
                          selectedPeriod:
                            description: Requested period
                            type: object
                            properties:
                              begin:
                                description: Start of the Period
                                type: string
                                example: '2023-06-01 20:05:32'
                              end:
                                description: End of the Period
                                type: string
                                example: '2024-03-01 20:05:32'
                              openCardCount:
                                description: Number of visits to the product page
                                type: integer
                                format: int32
                                example: 0
                              addToCartCount:
                                description: Added to cart, quantity
                                type: integer
                                format: int32
                                example: 0
                              ordersCount:
                                description: Ordered items, quantity
                                type: integer
                                format: int32
                                example: 0
                              ordersSumRub:
                                description: Ordered amount in rubles
                                type: integer
                                format: int32
                                example: 0
                              buyoutsCount:
                                description: Items bought, quantity
                                type: integer
                                format: int32
                                example: 0
                              buyoutsSumRub:
                                description: Bought amount in rubles
                                type: integer
                                format: int32
                                example: 0
                              cancelCount:
                                description: Cancelled items, quantity
                                type: integer
                                format: int32
                                example: 0
                              cancelSumRub:
                                description: Cancelled amount in rubles
                                type: integer
                                format: int32
                                example: 0
                              avgPriceRub:
                                description: Average price in rubles
                                type: integer
                                format: int32
                                example: 0
                              avgOrdersCountPerDay:
                                description: Average daily order quantity, quantity
                                type: integer
                                format: int32
                                example: 0
                              conversions:
                                description: Conversions
                                type: object
                                properties:
                                  addToCartPercent:
                                    description: Conversion to Cart, % (The percentage of visitors who, after opening the product card, added the item to the cart)
                                    type: integer
                                    format: int32
                                    example: 0
                                  cartToOrderPercent:
                                    description: Conversion to Order, % (The percentage of visitors who, after adding an item to the cart, completed the purchase)
                                    type: integer
                                    format: int32
                                    example: 0
                                  buyoutsPercent:
                                    description: Buyout Percentage, % (The percentage of visitors who ordered a product and successfully completed the purchase. Excludes items still in transit to the buyer.)
                                    type: integer
                                    format: int32
                                    example: 0
                      previousPeriod:
                        description: Statistics for the previous period
                        type: object
                        properties:
                          begin:
                            type: string
                            description: Start of the period
                            example: '2023-05-07 20:05:31'
                          end:
                            type: string
                            description: End of the period
                            example: '2023-06-01 20:05:31'
                          openCardCount:
                            description: Number of product card views
                            type: integer
                            format: int32
                            example: 0
                          addToCartCount:
                            description: Added to cart, count
                            type: integer
                            format: int32
                            example: 0
                          ordersCount:
                            description: Number of items ordered
                            type: integer
                            format: int32
                            example: 1
                          ordersSumRub:
                            description: Total order amount in rubles
                            type: integer
                            format: int32
                            example: 1262
                          buyoutsCount:
                            description: Number of items bought
                            type: integer
                            format: int32
                            example: 1
                          buyoutsSumRub:
                            description: Total buyout amount in rubles
                            type: integer
                            format: int32
                            example: 1262
                          cancelCount:
                            description: Number of items canceled
                            type: integer
                            format: int32
                            example: 0
                          cancelSumRub:
                            description: Total canceled amount in rubles
                            type: integer
                            format: int32
                            example: 0
                          avgPriceRub:
                            description: Average price in rubles
                            type: integer
                            format: int32
                            example: 1262
                          avgOrdersCountPerDay:
                            description: Average daily order count
                            type: number
                            example: 0.04
                          conversions:
                            description: Conversions
                            type: object
                            properties:
                              addToCartPercent:
                                description: Conversion to Cart, % (Percentage of visitors who, after opening the product card, added the item to the cart)
                                type: integer
                                format: int32
                                example: 0
                              cartToOrderPercent:
                                description: Conversion to Order, % (Percentage of completed the purchase)
                                type: integer
                                format: int32
                                example: 0
                              buyoutsPercent:
                                description: Buyout Percentage, % (Percentage of visitors who ordered a product and successfully completed the purchase. Excludes items still in transit to the buyer.)
                                type: integer
                                format: int32
                                example: 100
                      periodComparison:
                        description: Comparison of two periods in percentages
                        type: object
                        properties:
                          openCardDynamics:
                            description: Dynamics of product card views
                            type: integer
                            format: int32
                            example: 0
                          addToCartDynamics:
                            description: Dynamics of additions to the cart
                            type: integer
                            format: int32
                            example: 0
                          ordersCountDynamics:
                            description: Dynamics of order count
                            type: integer
                            format: int32
                            example: -100
                          ordersSumRubDynamics:
                            description: Dynamics of order sum in rubles
                            type: integer
                            format: int32
                            example: -100
                          buyoutsCountDynamics:
                            description: Dynamics of buyouts count
                            type: integer
                            format: int32
                            example: -100
                          buyoutsSumRubDynamics:
                            description: Dynamics of buyout sum in rubles
                            type: integer
                            format: int32
                            example: -100
                          cancelCountDynamics:
                            description: Dynamics of canceled items count
                            type: integer
                            format: int32
                            example: 0
                          cancelSumRubDynamics:
                            description: Dynamics of canceled items sum in rubles
                            type: integer
                            format: int32
                            example: 0
                          avgOrdersCountPerDayDynamics:
                            type: integer
                            format: int32
                            example: 0
                            description: Dynamics of average daily order count
                          avgPriceRubDynamics:
                            type: integer
                            format: int32
                            example: -100
                            description: Dynamics of average price for products. Discounts for promotions and WB discounts are considered.
                          conversions:
                            description: Conversions
                            type: object
                            properties:
                              addToCartPercent:
                                description: Conversion to Cart, % (Percentage of visitors who, after opening the product card, added the item to the cart)
                                type: integer
                                format: int32
                                example: 0
                              cartToOrderPercent:
                                description: Conversion to Order, % (Percentage of completed the purchase)
                                type: integer
                                format: int32
                                example: 0
                              buyoutsPercent:
                                description: Buyout Percentage, % (Percentage of visitors who ordered a product and successfully completed the purchase. Excludes items still in transit to the buyer.)
                                type: integer
                                format: int32
                                example: -100
                  stocks:
                    description: Inventory
                    type: object
                    properties:
                      stocksMp:
                        description: MP Inventory, units (Total quantity of inventory at the seller's warehouse)
                        type: integer
                        format: int32
                        example: 0
                      stocksWb:
                        description: Wildberries Warehouse Inventory (Total quantity of inventory at Wildberries warehouses)
                        type: integer
                        format: int32
                        example: 0
        error:
          type: boolean
          description: Error flag
        errorText:
          description: Error description
          type: string
          example: ''
        additionalErrors:
          description: Additional errors
          nullable: true
          type: array
          items:
            type: object
            properties:
              field:
                description: The structure where the error occurred
                type: string
              description:
                description: Description
                type: string
    NmReportDetailHistoryResponse:
      type: object
      properties:
        data:
          type: array
          items:
            type: object
            properties:
              nmID:
                type: integer
                format: int32
                example: 1234567
                description: WB article
              imtName:
                type: string
                example: Product Name
                description: Product Name
              vendorCode:
                type: string
                example: supplierVendor
                description: Vendor SKU
              history:
                type: array
                items:
                  type: object
                  properties:
                    dt:
                      type: string
                      format: date
                      example: '2023-06-20'
                      description: Date
                    openCardCount:
                      type: integer
                      format: int32
                      example: 26
                      description: Number of clicks on the product card
                    addToCartCount:
                      type: integer
                      format: int32
                      example: 1
                      description: Added to cart, pieces
                    ordersCount:
                      type: integer
                      format: int32
                      example: 0
                      description: Number of ordered items, pieces
                    ordersSumRub:
                      type: integer
                      format: int32
                      example: 0
                      description: Ordered amount in rubles
                    buyoutsCount:
                      type: integer
                      format: int32
                      example: 0
                      description: Number of purchased items, pieces
                    buyoutsSumRub:
                      type: integer
                      format: int32
                      example: 0
                      description: Purchase amount in rubles
                    buyoutPercent:
                      type: integer
                      format: int32
                      example: 0
                      description: Buyout Percentage, % (Percentage of visitors who ordered a product and successfully completed the purchase. Excludes items still in transit to the buyer.)
                    addToCartConversion:
                      type: number
                      example: 3.8
                      description: Conversion to cart, % (What percentage of visitors who opened the product card, added the product to the cart)
                    cartToOrderConversion:
                      type: integer
                      format: int32
                      example: 0
                      description: Conversion to order, % (What percentage of visitors who added the product to the cart, placed an order)
        error:
          type: boolean
          description: Error flag
        errorText:
          description: Error description
          type: string
          example: ''
        additionalErrors:
          description: Additional errors
          nullable: true
          type: array
          items:
            type: object
            properties:
              field:
                description: The structure where the error occurred
                type: string
              description:
                description: Description
                type: string
    NmReportGroupedHistoryResponse:
      type: object
      properties:
        data:
          type: array
          items:
            type: object
            properties:
              object:
                description: Item
                type: object
                properties:
                  id:
                    type: integer
                    format: int32
                    example: 358
                    description: Item Identifier
                  name:
                    type: string
                    example: Shampoos
                    description: Item Name
              brandName:
                type: string
                example: Some
                description: Brand
              tag:
                description: Tag
                type: object
                properties:
                  id:
                    type: integer
                    format: int32
                    example: 123
                    description: Numeric label identifier
                  name:
                    type: string
                    example: Sale
                    description: Label name
              history:
                type: array
                items:
                  type: object
                  properties:
                    dt:
                      type: string
                      format: date
                      example: '2023-06-21'
                      description: Date
                    openCardCount:
                      type: integer
                      format: int32
                      example: 0
                      description: Number of clicks on the product card
                    addToCartCount:
                      type: integer
                      format: int32
                      example: 0
                      description: Added to cart, pieces
                    ordersCount:
                      type: integer
                      format: int32
                      example: 0
                      description: Number of ordered items, pieces
                    ordersSumRub:
                      type: integer
                      format: int32
                      example: 0
                      description: Ordered amount in rubles
                    buyoutsCount:
                      type: integer
                      format: int32
                      example: 0
                      description: Number of purchased items, pieces
                    buyoutsSumRub:
                      type: integer
                      format: int32
                      example: 0
                      description: Purchase amount in rubles
                    buyoutPercent:
                      type: integer
                      format: int32
                      example: 0
                      description: Buyout Percentage, % (Percentage of visitors who ordered a product and successfully completed the purchase. Excludes items still in transit to the buyer.)
                    addToCartConversion:
                      type: integer
                      format: int32
                      example: 0
                      description: Conversion to cart, % (What percentage of visitors who opened the product card, added the product to the cart)
                    cartToOrderConversion:
                      type: integer
                      format: int32
                      example: 0
                      description: Conversion to order, % (What percentage of visitors who added the product to the cart, placed an order)
        error:
          type: boolean
          description: Error flag
        errorText:
          description: Error description
          type: string
          example: ''
        additionalErrors:
          description: Additional errors
          nullable: true
          type: array
          items:
            type: object
            properties:
              field:
                description: The structure where the error occurred
                type: string
              description:
                description: Description
                type: string
    SalesFunnelProductReq:
      type: object
      required:
        - id
        - reportType
        - params
      properties:
        id:
          description: Report ID in UUID format. Generated by the seller independently
          type: string
          format: uuid
        reportType:
          description: Report type — `DETAIL_HISTORY_REPORT`
          type: string
        userReportName:
          type: string
          description: Report name. If not specified, it will be generated automatically
        params:
          description: Report parameters
          type: object
          required:
            - startDate
            - endDate
          properties:
            nmIDs:
              type: array
              minItems: 0
              maxItems: 1000
              description: |
                `nmID ` to include in the report. Leave empty to get a report for all products
              items:
                type: integer
                format: int64
            subjectIDs:
              type: array
              description: Subjects IDs
              items:
                type: integer
                format: int64
            brandNames:
              type: array
              description: Brands
              items:
                type: string
            tagIDs:
              type: array
              description: Label IDs
              items:
                type: integer
                format: int64
            startDate:
              type: string
              description: Period start
              format: date
            endDate:
              type: string
              description: Period end
              format: date
            timezone:
              type: string
              description: |
                Timezone, by default Europe/Moscow
            aggregationLevel:
              type: string
              description: |
                How to group data (default is by days):

                  * `day` — by days
                  * `week` — by weeks
                  * `month` — by months
              enum:
                - day
                - week
                - month
            skipDeletedNm:
              description: Hide deleted nomenclatures
              type: boolean
    SalesFunnelGroupReq:
      type: object
      required:
        - id
        - reportType
        - params
      properties:
        id:
          description: Report ID in UUID format. Generated by the seller independently
          type: string
          format: uuid
        reportType:
          description: Report type — `GROUPED_HISTORY_REPORT`
          type: string
        userReportName:
          type: string
          description: Report name. If not specified, it will be generated automatically
        params:
          description: Report parameters
          type: object
          required:
            - startDate
            - endDate
          properties:
            subjectIDs:
              type: array
              description: Subject IDs
              items:
                type: integer
                format: int64
            brandNames:
              type: array
              description: Brands
              items:
                type: string
            tagIDs:
              type: array
              description: Label IDs
              items:
                type: integer
                format: int64
            startDate:
              type: string
              description: Period start
              format: date
            endDate:
              type: string
              description: Period end
              format: date
            timezone:
              type: string
              description: |
                Timezone, by default Europe/Moscow
            aggregationLevel:
              type: string
              description: |
                How to group data (default is by days):

                  * `day` — by days
                  * `week` — by weeks
                  * `month` — by months
            skipDeletedNm:
              description: Hide deleted `nmID`
              type: boolean
    SearchReportGroupReq:
      type: object
      required:
        - id
        - reportType
        - params
      properties:
        id:
          description: Report ID in UUID format. Generated by the seller independently
          type: string
          format: uuid
        reportType:
          description: Report type — `SEARCH_QUERIES_PREMIUM_REPORT_GROUP`
          type: string
        userReportName:
          type: string
          description: Report name. If not specified, it will be generated automatically
        params:
          description: Report parameters
          type: object
          required:
            - currentPeriod
            - subjectIds
            - orderBy
            - positionCluster
          properties:
            currentPeriod:
              $ref: '#/components/schemas/Period'
            pastPeriod:
              $ref: '#/components/schemas/pastPeriod'
            nmIds:
              type: array
              minItems: 0
              maxItems: 1000
              description: |
                WB articles to include in the report. Leave empty to get a report for all products
              items:
                type: integer
                format: int64
            subjectIds:
              example:
                - 64
                - 334
              type: array
              description: Subject IDs
              items:
                type: integer
                format: int32
            brandNames:
              example:
                - nike
                - adidas
              type: array
              description: Brands
              items:
                type: string
            tagIds:
              example:
                - 32
                - 53
              description: Label IDs
              type: array
              items:
                type: integer
                format: int64
            orderBy:
              $ref: '#/components/schemas/OrderByGrTe'
            positionCluster:
              $ref: '#/components/schemas/PositionCluster'
    SearchReportProductReq:
      type: object
      required:
        - id
        - reportType
        - params
      properties:
        id:
          description: Report ID in UUID format. Generated by the seller independently
          type: string
          format: uuid
        reportType:
          description: Report type — `SEARCH_QUERIES_PREMIUM_REPORT_PRODUCT`
          type: string
        userReportName:
          type: string
          description: Report name. If not specified, it will be generated automatically
        params:
          description: Report parameters
          type: object
          required:
            - currentPeriod
            - orderBy
            - positionCluster
          properties:
            currentPeriod:
              $ref: '#/components/schemas/Period'
            pastPeriod:
              $ref: '#/components/schemas/pastPeriod'
            subjectId:
              example: 132
              description: Subject ID
              type: integer
              format: int32
            brandName:
              example: Abikas
              description: Brand
              type: string
            tagId:
              example: 3
              description: Label ID
              type: integer
              format: int64
            nmIds:
              type: array
              minItems: 0
              maxItems: 1000
              description: |
                WB articles to include in the report. Leave empty to get a report for all products
              items:
                type: integer
                format: int64
            positionCluster:
              $ref: '#/components/schemas/PositionCluster'
            orderBy:
              $ref: '#/components/schemas/OrderBy'
    SearchReportTextReq:
      type: object
      required:
        - id
        - reportType
        - params
      properties:
        id:
          description: Report ID in UUID format. Generated by the seller independently
          type: string
          format: uuid
        reportType:
          description: Report type — `SEARCH_QUERIES_PREMIUM_REPORT_TEXT`
          type: string
        userReportName:
          type: string
          description: Report name. If not specified, it will be generated automatically
        params:
          description: Report parameters
          type: object
          required:
            - currentPeriod
            - limit
            - topOrderBy
            - orderBy
          properties:
            currentPeriod:
              $ref: '#/components/schemas/Period'
            pastPeriod:
              $ref: '#/components/schemas/pastPeriod'
            nmIds:
              type: array
              minItems: 0
              maxItems: 1000
              description: |
                WB articles to include in the report. Leave empty to get a report for all products
            subjectIds:
              example:
                - 64
                - 334
              type: array
              description: Subject IDs
              items:
                type: integer
                format: int32
            brandNames:
              example:
                - nike
                - adidas
              type: array
              description: Brands
              items:
                type: string
            tagIds:
              example:
                - 32
                - 53
              description: Label IDs
              type: array
              items:
                type: integer
                format: int64
            topOrderBy:
              example: openToCart
              description: |
                Sorting by field:
                  - `openCard` — transitioned to the product card from search
                  - `addToCart` — added to the cart from search
                  - `openToCart` — conversion to cart from search
                  - `orders` — ordered products from search
                  - `cartToOrder` — conversion to order from search
              type: string
              enum:
                - openCard
                - addToCart
                - openToCart
                - orders
                - cartToOrder
            orderBy:
              $ref: '#/components/schemas/OrderByGrTe'
            limit:
              $ref: '#/components/schemas/TextLimit'
    StocksReportReq:
      type: object
      required:
        - id
        - reportType
        - params
      properties:
        id:
          description: Report ID in UUID format. Generated by the seller independently
          type: string
          format: uuid
        reportType:
          description: Report type — `STOCK_HISTORY_REPORT_CSV`
          type: string
        userReportName:
          type: string
          description: Report name. If not specified, it will be generated automatically
        params:
          description: Report parameters
          allOf:
            - $ref: '#/components/schemas/CommonReportFilters'
    NmReportRetryReportRequest:
      properties:
        downloadId:
          type: string
          description: Report ID
          format: uuid
          example: 06eea887-9d9f-491f-b16a-bb1766fcb8d2
    NmReportCreateReportResponse:
      type: object
      properties:
        data:
          type: string
          description: Notification that report generation has started.
          example: Created
      required:
        - data
    NmReportGetReportsResponse:
      type: object
      required:
        - data
      properties:
        data:
          type: array
          items:
            type: object
            required:
              - id
              - status
              - name
              - size
              - startDate
              - endDate
              - createdAt
            properties:
              id:
                description: Report ID
                type: string
                format: uuid
                example: 06eae887-9d9f-491f-b16a-bb1766fcb8d2
              createdAt:
                type: string
                description: Date and time of generation completion
                format: date-time
                example: '2024-06-26 20:05:32'
              status:
                type: string
                description: |
                  Report status:

                  * `WAITING` — in the queue for processing
                  * `PROCESSING` — generating
                  * `SUCCESS —` ready
                  * `RETRY` — awaiting reprocessing
                  * `FAILED` — failed to generate, please regenerate
                example: SUCCESS
              name:
                description: Report name
                type: string
                example: Card report
              size:
                description: Report size, B
                type: integer
                example: 123
              startDate:
                description: Period start
                type: string
                format: date
                example: '2024-06-21'
              endDate:
                description: Period end
                type: string
                format: date
                example: '2023-04-23'
    NmReportRetryReportResponse:
      type: object
      properties:
        data:
          type: string
          description: Notification that report re-generation has started
          example: Retry
      required:
        - data
    response429Download:
      type: object
      properties:
        title:
          type: string
          description: Error title
        detail:
          type: string
          description: Error details
        code:
          type: string
          description: Internal error code
        requestId:
          type: string
          description: Unique request ID
        origin:
          type: string
          description: WB internal service ID
        status:
          type: number
          description: HTTP status code
        statusText:
          type: string
          description: Text of the HTTP status code
        timestamp:
          type: string
          format: RFC3339
          description: Request date and time
      example:
        title: too many requests
        detail: limited by c122a060-a7fb-4bb4-abb0-32fd4e18d489
        code: 07e4668e-ac2242c5c8c5-[UK-4dx7JUdskGZ]
        requestId: 9d3c02cc698f8b041c661a7c28bed293
        origin: s2s-api-auth-stat
        status: 429
        statusText: Too Many Requests
        timestamp: '2024-09-30T06:52:38Z'
    response429DownloadDaily:
      type: object
      properties:
        title:
          type: string
          description: Error title
        detail:
          type: string
          description: Error details
        requestId:
          type: string
          description: Unique request ID
        origin:
          type: string
          description: WB internal service ID
      required:
        - title
        - detail
        - requestId
        - origin
      example:
        title: Too many requests
        detail: The daily response limit has been exceeded. The maximum number is 20.
        requestId: 51b7828b-e298-4dfa-b7cd-45ab179921d393
        origin: analytics-open-api
    TableGroupRequestSt:
      allOf:
        - $ref: '#/components/schemas/CommonReportFilters'
        - type: object
          properties:
            limit:
              example: 150
              description: Number of groups in the response
              type: integer
              format: uint32
              maximum: 1000
              default: 100
            offset:
              example: 100
              description: From which element to start outputting data
              type: integer
              format: uint32
          required:
            - offset
    CommonReportFilters:
      description: General filters for the report
      type: object
      properties:
        nmIDs:
          type: array
          description: List of WB article numbers for filtering
          example:
            - 111222333
            - 444555666
          items:
            type: integer
            format: int64
        subjectIDs:
          type: array
          description: List of subject IDs for filtering
          example:
            - 123
            - 456
          items:
            type: integer
            format: int32
        brandNames:
          type: array
          description: List of brands for filtering
          example:
            - nibble
            - DecC
          items:
            type: string
        tagIDs:
          type: array
          description: List of label IDs for filtering
          example:
            - 3
            - 4
            - 5
          items:
            type: integer
            format: int64
        currentPeriod:
          $ref: '#/components/schemas/PeriodSt'
        stockType:
          $ref: '#/components/schemas/StockType'
        skipDeletedNm:
          type: boolean
          description: To skip deleted product cards
          example: true
        availabilityFilters:
          $ref: '#/components/schemas/AvailabilityFilters'
        orderBy:
          $ref: '#/components/schemas/TableOrderBy'
      required:
        - availabilityFilters
        - currentPeriod
        - stockType
        - skipDeletedNm
        - orderBy
    PeriodSt:
      type: object
      description: Period
      required:
        - start
        - end
      properties:
        start:
          type: string
          description: Start date of the period. No later than `end`. No earlier than 3 months from today
          format: date
          example: '2024-02-10'
        end:
          type: string
          description: End date of the period. No earlier than 3 months from today
          format: date
          example: '2024-02-10'
    pastPeriodSt:
      type: object
      description: Previous period for comparison. Number of days — less than or equal to `currentPeriod`
      required:
        - start
        - end
      properties:
        start:
          type: string
          description: Start date of the period. No later than `end`. No earlier than 3 months from today
          format: date
          example: '2024-02-08'
        end:
          type: string
          description: End date of the period. No later than the day before the start date `currentPeriod`. No earlier than 3 months from today
          format: date
          example: '2024-02-08'
    StockType:
      type: string
      description: |
        Type of products storage warehouse:
          - `""` — all
          - `wb` — WB warehouses
          - `mp` — seller's warehouses (FBS)
      example: mp
      enum:
        - ''
        - wb
        - mp
    AvailabilityFilters:
      type: array
      description: |
        Доступность товара:
          - `deficient` — Low stock
          - `actual` — Selling well
          - `balanced` — Selling steadily
          - `nonActual` — Selling poorly
          - `nonLiquid` — Struggling
          - `invalidData` — Not calculated
      example:
        - deficient
        - balanced
      items:
        type: string
        enum:
          - deficient
          - actual
          - balanced
          - nonActual
          - nonLiquid
          - invalidData
    TableOrderBy:
      type: object
      description: Sorting parameters
      properties:
        field:
          $ref: '#/components/schemas/TableGroupField'
        mode:
          $ref: '#/components/schemas/OrderByMode'
      required:
        - field
        - mode
    TableGroupField:
      type: string
      description: |
        Sorting by field:
          - `ordersCount` — Orders
          - `ordersSum` — Orders value
          - `avgOrders` — Average daily orders
          - `buyoutCount` — Kept items
          - `buyoutSum` — Kept item value
          - `buyoutPercent` — Kept item rate
          - `stockCount` — Current inventory
          - `stockSum` — Current inventory value
          - `saleRate` — Current DSI
          - `avgStockTurnover` — Average DSI
          - `toClientCount` — On the way to user
          - `fromClientCount` — On the way from user
          - `minPrice` — Minimal seller's price with seller's discount (excluding WB Club discount)
          - `maxPrice` — Maximal seller's price with seller's discount (excluding WB Club discount)
          - `officeMissingTime` — Out-of-stock time
          - `lostOrdersCount` — Lost orders
          - `lostOrdersSum` — Lost order value
          - `lostBuyoutsCount` — Lost sales
          - `lostBuyoutsSum` — Lost sales value
      enum:
        - ordersCount
        - ordersSum
        - avgOrders
        - buyoutCount
        - buyoutSum
        - buyoutPercent
        - stockCount
        - stockSum
        - saleRate
        - avgStockTurnover
        - toClientCount
        - fromClientCount
        - minPrice
        - maxPrice
        - officeMissingTime
        - lostOrdersCount
        - lostOrdersSum
        - lostBuyoutsCount
        - lostBuyoutsSum
      example: avgOrders
    OrderByMode:
      type: string
      description: |
        Sorting order:
          - `asc` — ascending
          - `desc` — descending
      enum:
        - asc
        - desc
      example: asc
    TableGroupResponseSt:
      type: object
      properties:
        groups:
          $ref: '#/components/schemas/TableGroups'
      required:
        - groups
    TableGroups:
      type: array
      description: Set of data by groups
      items:
        $ref: '#/components/schemas/TableGroupItemSt'
    TableGroupItemSt:
      type: object
      description: Group data
      properties:
        subjectID:
          type: integer
          format: int32
          description: Subject ID
          example: *********
        subjectName:
          type: string
          description: Subject name
          example: Кружка
        brandName:
          type: string
          description: Brand
          example: Крутая посуда
        tagID:
          type: integer
          format: int64
          description: Tag ID
          example: 12345
        tagName:
          type: string
          description: Tag name
          example: Человек-Паук
        metrics:
          description: Group metrics
          allOf:
            - $ref: '#/components/schemas/TableCommonMetrics'
        items:
          type: array
          description: Group products
          items:
            $ref: '#/components/schemas/TableProductItemSt'
      required:
        - subjectID
        - subjectName
        - brandName
        - tagID
        - tagName
        - metrics
        - items
    TableCommonMetrics:
      type: object
      description: Metrics
      properties:
        ordersCount:
          type: integer
          format: uint64
          description: Orders
          example: 100
        ordersSum:
          type: integer
          format: uint64
          description: Orders value
          example: 100000
        avgOrders:
          type: number
          format: float64
          description: Average daily orders
          example: 200
        avgOrdersByMonth:
          type: array
          description: Average number of orders by month
          items:
            $ref: '#/components/schemas/FloatGraphByPeriodItem'
        buyoutCount:
          type: integer
          format: uint64
          description: Kept items
          example: 150
        buyoutSum:
          type: integer
          format: uint64
          description: Kept item value
          example: 150000
        buyoutPercent:
          type: integer
          format: uint32
          description: Kept item rate
          example: 5
        stockCount:
          type: integer
          format: uint64
          description: Current inventory
          example: 50
        stockSum:
          type: integer
          format: uint64
          description: Current inventory value
          example: 50000
        saleRate:
          description: |
            Current DSI. Special cases:
              1) `"hours":-1` — infinite duration
              2) `"hours":-2` — zero duration
              3) `"hours":-3` — uncalculated duration
          type: object
          properties:
            days:
              type: integer
              format: int32
              example: 5
              description: Number of days
            hours:
              type: integer
              format: int32
              example: 15
              description: Number of hours
          required:
            - days
            - hours
        avgStockTurnover:
          description: |
            Average DSI. Special cases:
              1) `"hours":-1` — infinite duration
              2) `"hours":-2` — zero duration
              3) `"hours":-3` — uncalculated duration
          type: object
          properties:
            days:
              type: integer
              format: int32
              example: 5
              description: Number of days
            hours:
              type: integer
              format: int32
              example: 15
              description: Number of hours
          required:
            - days
            - hours
        toClientCount:
          type: integer
          format: uint64
          description: On the way to user
          example: 20
        fromClientCount:
          type: integer
          format: uint64
          description: On the way from user
          example: 30
        officeMissingTime:
          description: |
            Out-of-stock time. Special cases:
              1) `"hours":-1` — infinite duration
              2) `"hours":-2` — zero duration
              3) `"hours":-3` — uncalculated duration
              4) `"hours":-4` — absence during the entire period
          type: object
          properties:
            days:
              type: integer
              format: int32
              example: 5
              description: Number of days
            hours:
              type: integer
              format: int32
              example: 15
              description: Number of hours
          required:
            - days
            - hours
        lostOrdersCount:
          type: number
          format: float64
          description: |
            Lost orders. Special cases:
              1. Value less than `0` and is not `-2` — the value is not calculated
              2. Value is `-2` — zero value
          example: 1550.52
        lostOrdersSum:
          type: number
          format: float64
          description: |
            Lost order value. Special cases:
              1. Value less than `0` and is not `-2` — the value is not calculated
              2. Value is `-2` — zero value
          example: 155000.25
        lostBuyoutsCount:
          type: number
          format: float64
          description: |
            Lost sales. Special cases:
              1. Value less than `0` and is not `-2` — the value is not calculated
              2. Value is `-2` — zero value
          example: 123.55
        lostBuyoutsSum:
          type: number
          format: float64
          description: |
            Lost sales value. Special cases:
              1. Value less than `0` and is not `-2` — the value is not calculated
              2. Value is `-2` — zero value
          example: 225555.15
      required:
        - ordersCount
        - ordersSum
        - buyoutCount
        - buyoutSum
        - buyoutPercent
        - avgOrders
        - avgOrdersByMonth
        - stockCount
        - stockSum
        - saleRate
        - avgStockTurnover
        - toClientCount
        - fromClientCount
        - officeMissingTime
        - lostOrdersCount
        - lostOrdersSum
        - lostBuyoutsCount
        - lostBuyoutsSum
    FloatGraphByPeriodItem:
      type: object
      description: Average number of orders by month
      properties:
        start:
          type: string
          format: date
          description: Beginning of the month
          example: '2025-01-01'
        end:
          type: string
          format: date
          description: End of the month
          example: '2025-01-31'
        value:
          type: number
          format: float64
          description: Average number of orders
          example: 25.55
      required:
        - start
        - end
        - value
    TableProductItemSt:
      type: object
      description: Product data
      properties:
        nmID:
          type: integer
          description: WB article
          example: *********
          format: int64
        isDeleted:
          type: boolean
          description: Is the product deleted
          example: false
        subjectName:
          type: string
          description: Subject name
          example: Принтеры
        name:
          type: string
          description: Product name
          example: Печатник 3000
        vendorCode:
          type: string
          description: Seller's article
          example: pechatnik3000
        brandName:
          type: string
          description: Brand
          example: Компик
        mainPhoto:
          type: string
          description: Link to the main photo
          example: https://basket-12.wbbasket.ru/vol1788/part178840/178840836/images/c246x328/1.webp
        hasSizes:
          type: boolean
          description: Does the product have sizes. The product has no size variations when it is represented by '"techSize":"0"', indicating a single, uniform size for the product.
          example: true
        metrics:
          description: Product metrics
          allOf:
            - $ref: '#/components/schemas/TableCommonMetrics'
            - type: object
              properties:
                currentPrice:
                  type: object
                  description: Current price
                  properties:
                    minPrice:
                      type: integer
                      format: uint64
                      description: Minimal seller's price with seller's discount (excluding WB Club discount)
                      example: 50
                    maxPrice:
                      type: integer
                      format: uint64
                      description: Maximal seller's price with seller's discount (excluding WB Club discount)
                      example: 100
                  required:
                    - minPrice
                    - maxPrice
                availability:
                  type: string
                  description: |
                    Product availability:
                      - `deficient` — Low stock
                      - `actual` — Selling well
                      - `balanced` — Selling steadily
                      - `nonActual` — Selling poorly
                      - `nonLiquid` — Struggling
                      - `invalidData` — Struggling
                  example: deficient
                  enum:
                    - deficient
                    - actual
                    - balanced
                    - nonActual
                    - nonLiquid
                    - invalidData
              required:
                - currentPrice
                - availability
      required:
        - nmID
        - isDeleted
        - subjectName
        - name
        - vendorCode
        - brandName
        - mainPhoto
        - hasSizes
        - metrics
    TableProductRequest:
      description: Request parameters for inventory by products
      allOf:
        - $ref: '#/components/schemas/CommonProductFilters'
        - type: object
          properties:
            limit:
              example: 150
              description: Number of groups in the response
              type: integer
              format: uint32
              maximum: 1000
              default: 100
            offset:
              example: 100
              description: From which element to start outputting data
              type: integer
              format: uint32
          required:
            - offset
    CommonProductFilters:
      type: object
      description: General product filters
      properties:
        nmIDs:
          type: array
          description: List of WB article numbers for filtering
          example:
            - 111222333
            - 444555666
          items:
            type: integer
            format: int64
        subjectID:
          type: integer
          description: Subject ID
          example: 123456
          format: int32
        brandName:
          type: string
          description: Brand
          example: Спортик
        tagID:
          type: integer
          description: Tag ID
          example: 25345
          format: int64
        currentPeriod:
          $ref: '#/components/schemas/PeriodSt'
        stockType:
          $ref: '#/components/schemas/StockType'
        skipDeletedNm:
          type: boolean
          description: To skip deleted product cards
          example: true
        orderBy:
          $ref: '#/components/schemas/TableOrderBy'
        availabilityFilters:
          $ref: '#/components/schemas/AvailabilityFilters'
      required:
        - currentPeriod
        - stockType
        - skipDeletedNm
        - orderBy
        - AvailabilityFilters
    TableProductResponse:
      type: object
      properties:
        items:
          type: array
          description: Set of product data
          items:
            $ref: '#/components/schemas/TableProductItemSt'
      required:
        - items
    TableSizeRequest:
      description: Request parameters for inventory by the size of product
      $ref: '#/components/schemas/CommonSizeFilters'
    CommonSizeFilters:
      type: object
      description: General filters by size
      properties:
        nmID:
          type: integer
          format: int64
          description: WB article
          example: *********
        currentPeriod:
          $ref: '#/components/schemas/PeriodSt'
        stockType:
          $ref: '#/components/schemas/StockType'
        orderBy:
          $ref: '#/components/schemas/TableOrderBy'
        includeOffice:
          type: boolean
          description: Include warehouse details
          example: true
      required:
        - nmID
        - currentPeriod
        - stockType
        - orderBy
        - includeOffice
    TableSizeResponse:
      type: object
      properties:
        offices:
          type: array
          description: Set of warehouse data
          items:
            $ref: '#/components/schemas/TableOfficeItem'
        sizes:
          type: array
          description: Set of data on the size of product
          items:
            type: object
            properties:
              name:
                type: string
                description: Size name
                example: 50
              chrtID:
                type: integer
                format: int64
                description: Size ID
                example: 123321
              offices:
                type: array
                description: Warehouses
                items:
                  $ref: '#/components/schemas/TableOfficeItem'
              metrics:
                description: Size metrics
                allOf:
                  - $ref: '#/components/schemas/TableCommonMetrics'
                  - type: object
                    properties:
                      currentPrice:
                        type: object
                        description: Current price
                        properties:
                          minPrice:
                            type: integer
                            format: uint64
                            description: Minimal seller's price with seller's discount (excluding WB Club discount)
                            example: 50
                          maxPrice:
                            type: integer
                            format: uint64
                            description: Maximal seller's price with seller's discount (excluding WB Club discount)
                            example: 100
                        required:
                          - minPrice
                          - maxPrice
                    required:
                      - currentPrice
            required:
              - name
              - chrtID
              - metrics
    TableOfficeItem:
      type: object
      description: Warehouse data
      properties:
        regionName:
          type: string
          description: Shipping region
          example: Центральный
        officeID:
          type: integer
          format: int64
          description: Warehouse ID
          example: 123456
        officeName:
          type: string
          description: Warehouse name
          example: Коледино
        metrics:
          description: Warehouse metrics
          allOf:
            - $ref: '#/components/schemas/TableCommonMetrics'
      required:
        - regionName
        - officeID
        - officeName
        - metrics
    TableShippingOfficeRequest:
      description: Request parameters for inventory by warehouses
      $ref: '#/components/schemas/CommonShippingOfficeFilters'
    CommonShippingOfficeFilters:
      type: object
      description: General filters by shipping region
      properties:
        nmIDs:
          type: array
          description: List of WB article numbers for filtering
          example:
            - 111222333
            - 444555666
          items:
            type: integer
            format: int64
        subjectIDs:
          type: array
          description: List of subject IDs for filtering
          example:
            - 123
            - 456
          items:
            type: integer
            format: int32
        brandNames:
          type: array
          description: List of brands for filtering
          example:
            - Enc
            - ЗлатА
            - Ercs
            - Loik
          items:
            type: string
        tagIDs:
          type: array
          description: List of label IDs for filtering
          example:
            - 123
            - 456
            - 789
          items:
            type: integer
            format: int64
        currentPeriod:
          $ref: '#/components/schemas/PeriodSt'
        stockType:
          $ref: '#/components/schemas/StockType'
        skipDeletedNm:
          type: boolean
          description: To skip deleted product cards
          example: false
      required:
        - currentPeriod
        - stockType
        - skipDeletedNm
    TableShippingOfficeResponse:
      type: object
      properties:
        regions:
          type: array
          description: Set of data on shipping regions
          items:
            $ref: '#/components/schemas/TableShippingOfficeItem'
    TableShippingOfficeItem:
      type: object
      description: Shipping region data
      properties:
        regionName:
          type: string
          description: Shipping region
          example: Центральный
        metrics:
          description: Metrics by region
          allOf:
            - $ref: '#/components/schemas/TableShippingOfficeMetrics'
        offices:
          type: array
          description: Warehouse data
          items:
            type: object
            properties:
              officeID:
                type: integer
                format: int64
                description: Warehouse ID
                example: 123456
              officeName:
                type: string
                description: Warehouse name
                example: Коледино
              metrics:
                description: Warehouse metrics
                allOf:
                  - $ref: '#/components/schemas/TableShippingOfficeMetrics'
            required:
              - officeID
              - officeName
              - metrics
      required:
        - regionName
        - metrics
        - offices
    TableShippingOfficeMetrics:
      type: object
      description: General metrics by region/shipping warehouses
      properties:
        stockCount:
          type: integer
          format: uint64
          description: Current inventory
          example: 20
        stockSum:
          type: integer
          format: uint64
          description: Current inventory value
          example: 20000
        saleRate:
          description: |
            Current DSI. Special cases:
              1) `"hours":-1` — infinite duration
              2) `"hours":-2` — zero duration
              3) `"hours":-3` — uncalculated duration
          type: object
          properties:
            days:
              type: integer
              format: int32
              example: 5
              description: Number of days
            hours:
              type: integer
              format: int32
              example: 15
              description: Number of hours
          required:
            - days
            - hours
        toClientCount:
          type: integer
          format: uint64
          description: On the way to user
          example: 30
        fromClientCount:
          type: integer
          format: uint64
          description: On the way from user
          example: 40
      required:
        - stockCount
        - stockSum
        - saleRate
        - toClientCount
        - fromClientCount
    StatInterval:
      type: object
      properties:
        interval:
          description: Requested time range
          type: object
          properties:
            begin:
              description: Beginning of the requested period
              type: string
              format: date
            end:
              description: End of the requested period
              type: string
              format: date
        stats:
          description: Statistics data
          type: array
          items:
            $ref: '#/components/schemas/StatsBlok1'
    StatDate:
      type: object
      properties:
        dates:
          description: Dates for which information is to be provided
          type: array
          items:
            type: string
            format: date
        stats:
          description: Statistics data
          type: array
          items:
            $ref: '#/components/schemas/StatsBlok2'
    Stat:
      type: object
      properties:
        stats:
          description: Statistics data
          type: array
          items:
            $ref: '#/components/schemas/StatsBlok1'
    StatsBlok1:
      type: object
      properties:
        item_id:
          description: Banner ID
          type: integer
        item_name:
          description: Brand
          type: string
        category_name:
          description: Category name
          type: string
        advert_type:
          description: |
            <dl> <dt>Media Campaign Type:</dt>  <dd><code>1</code> — daily placement</dd>  <dd><code>2</code> — views placement</dd> </dl>
          type: integer
        place:
          description: Placement on the page
          type: integer
        views:
          description: Number of views
          type: integer
        clicks:
          description: Number of clicks
          type: integer
        cr:
          description: |
            CR(conversion rate) is the ratio of the number of orders to the total number of visits to the campaign
          type: number
        ctr:
          description: |
            CTR (click-through rate) — clickability metric, the ratio of the number of clicks to the number of displays within a media campaign
          type: number
        date_from:
          description: The placement start date
          type: string
          format: date-time
        date_to:
          description: The placement stop time
          type: string
          format: date-time
        subject_name:
          description: Parental category of the item
          type: string
        atbs:
          description: Number of products added to the basket
          type: integer
        orders:
          description: Orders number
          type: integer
        price:
          description: Placement costs
          type: integer
        cpc:
          description: (cost per click) — CPC, click price for the promoted product
          type: number
        status:
          description: Campaign Status
          type: integer
        daily_stats:
          $ref: '#/components/schemas/DailyStats1'
        expenses:
          description: Cost of banner placement
          type: integer
        cr1:
          description: Ratio of the number of additions to basket to the number of clicks
          type: number
        cr2:
          description: Ratio of the number of orders to the number of additions to basket
          type: integer
    DailyStats1:
      type: array
      items:
        type: object
        properties:
          date:
            description: Date
            type: string
            format: date-time
          app_type_stats:
            description: Statistics by platform
            type: array
            items:
              type: object
              properties:
                app_type:
                  description: |
                    <dl>
                    <dt>Platform type:</dt>
                    <dd><code>1</code> — website</dd>
                    <dd><code>32</code> — Android</dd>
                    <dd><code>64</code> — IOS</dd>
                    </dl>
                  type: integer
                stats:
                  $ref: '#/components/schemas/Stats1'
    Stats1:
      type: array
      items:
        type: object
        properties:
          views:
            description: Number of views
            type: integer
          clicks:
            description: Number of clicks
            type: integer
          atbs:
            description: Number of products added to the basket
            type: integer
          ctr:
            description: |
              CTR (click-through rate) — clickability metric, the ratio of the number of clicks to the number of displays within a media campaign
            type: number
    StatsBlok2:
      type: object
      properties:
        item_id:
          description: Banner ID
          type: integer
        item_name:
          description: Brand
          type: string
        category_name:
          description: Category name
          type: string
        advert_type:
          description: |
            <dl> <dt>Media campaign type:</dt> <dd><code>1</code> — daily placement</dd> <dd><code>2</code> — views placement</dd> </dl>
          type: integer
        place:
          description: Placement on the page
          type: integer
        views:
          description: Number of views
          type: integer
        clicks:
          description: Number of clicks
          type: integer
        cr:
          description: |
            CR(conversion rate) is the ratio of the number of orders to the total number of visits to the campaign
          type: number
        ctr:
          description: |
            CTR (click-through rate) — clickability metric, the ratio of the number of clicks to the number of displays within a media campaign
          type: number
        date_from:
          description: The placement start date
          type: string
          format: date-time
        date_to:
          description: The placement stop time
          type: string
          format: date-time
        subject_name:
          description: Parental category of the item
          type: string
        atbs:
          description: Number of products added to the basket
          type: integer
        orders:
          description: Orders number
          type: integer
        price:
          description: Placement costs
          type: integer
        cpc:
          description: (cost per click) — CPC, click price for the promoted product
          type: number
        status:
          description: Media campaign status
          type: integer
        daily_stats:
          $ref: '#/components/schemas/DailyStats2'
        expenses:
          description: Cost of banner placement
          type: integer
        cr1:
          description: Ratio of the number of additions to basket to the number of clicks
          type: number
        cr2:
          description: Ratio of the number of orders to the number of additions to basket
          type: integer
    DailyStats2:
      type: array
      items:
        type: object
        properties:
          date:
            description: Date
            type: string
            format: date-time
          app_type_stats:
            description: Statistics by platform
            type: array
            items:
              type: object
              properties:
                app_type:
                  description: |
                    <dl>
                    <dt>Platform type:</dt>
                    <dd><code>1</code> — website</dd>
                    <dd><code>32</code> — Android</dd>
                    <dd><code>64</code> — IOS</dd>
                    </dl>
                  type: integer
                stats:
                  $ref: '#/components/schemas/Stats2'
    Stats2:
      type: array
      items:
        type: object
        properties:
          views:
            description: Number of views
            type: integer
          clicks:
            description: Number of clicks
            type: integer
          atbs:
            description: Number of products added to the basket
            type: integer
          orders:
            description: Orders number
            type: integer
          cr:
            description: |
              CR(conversion rate) is the ratio of the number of orders to the total number of visits to the campaign           
            type: number
          ctr:
            description: |
              CTR (click-through rate) — clickability metric, the ratio of the number of clicks to the number of displays within a media campaign
            type: number
    RequestWithDate:
      type: array
      minItems: 1
      maxItems: 100
      items:
        type: object
        required:
          - id
          - dates
        properties:
          id:
            description: Campaign ID
            type: integer
          dates:
            description: Dates for which information needs to be provided
            type: array
            items:
              type: string
              format: date
    RequestWithCampaignID:
      type: array
      minItems: 1
      maxItems: 100
      items:
        type: object
        additionalProperties: false
        required:
          - id
        properties:
          id:
            description: Campaign ID
            type: integer
    RequestWithInterval:
      type: array
      minItems: 1
      maxItems: 100
      items:
        type: object
        required:
          - id
          - interval
        properties:
          id:
            description: Campaign ID
            type: integer
          interval:
            description: The time period for which information needs to be provided
            type: object
            properties:
              begin:
                description: Beginning of the requested period
                type: string
                format: date
              end:
                description: End of the requested period
                type: string
                format: date
    Days:
      description: Statistics by days
      type: array
      items:
        type: object
        properties:
          date:
            description: Date for which the data is presented
            type: string
            format: date-time
          views:
            type: integer
            description: Number of views
          clicks:
            type: integer
            description: Number of clicks
          ctr:
            type: number
            description: "Click-Through Rate, ratio of clicks to displays,\_%\n"
          cpc:
            type: number
            description: Average cost per click, ₽.
          sum:
            type: number
            description: Expenses, ₽
          atbs:
            type: integer
            description: Number of products added to the basket
          orders:
            type: integer
            description: Orders number
          cr:
            type: number
            description: |
              CR(conversion rate) is the ratio of the number of orders to the total number of visits to the campaign.
          shks:
            type: integer
            description: Number of ordered products, pcs
          sum_price:
            type: number
            description: Orders amount, ₽
          apps:
            description: Information block about the platform
            type: array
            items:
              type: object
              properties:
                views:
                  type: integer
                  description: Number of views
                clicks:
                  type: integer
                  description: Number of clicks
                ctr:
                  type: number
                  description: "Click-Through Rate, ratio of clicks to displays,\_%\n"
                cpc:
                  type: number
                  description: Average cost per click, ₽
                sum:
                  type: number
                  description: Expenses, ₽
                atbs:
                  type: integer
                  description: Number of products added to the basket
                orders:
                  type: integer
                  description: Orders number
                cr:
                  type: number
                  description: |
                    CR(conversion rate) is the ratio of the number of orders to the total number of visits to the campaign
                shks:
                  type: integer
                  description: Number of ordered products, pcs
                sum_price:
                  type: number
                  description: Orders amount, ₽
                apps:
                  description: Information block about WB articles
                  type: array
                  items:
                    type: object
                    properties:
                      views:
                        type: integer
                        description: Number of views
                      clicks:
                        type: integer
                        description: Number of clicks
                      ctr:
                        type: number
                        description: "Click-Through Rate, ratio of clicks to displays,\_%    \n"
                      cpc:
                        type: number
                        description: Average cost per click, ₽
                      sum:
                        type: number
                        description: Expenses, ₽
                      atbs:
                        type: integer
                        description: Number of products added to the basket
                      orders:
                        type: integer
                        description: Orders number
                      cr:
                        type: number
                        description: |
                          CR(conversion rate) is the ratio of the number of orders to the total number of visits to the campaign
                      shks:
                        type: integer
                        description: Number of ordered products, pcs
                      sum_price:
                        type: number
                        description: Orders amount, ₽
                      name:
                        description: Product name
                        type: string
                      nmId:
                        description: WB article ID
                        type: integer
                appType:
                  type: integer
                  description: Platform type (`1` — website, `32` — Android, `64` — IOS)
    BoosterStats:
      description: Statistics on the average position of the product on search results pages and catalog pages (for automatic campaigns)
      type: array
      items:
        type: object
        properties:
          date:
            description: Date for which information is to be provided
            type: string
            format: date-time
          nm:
            description: WB article
            type: integer
          avg_position:
            description: Average position of the product on search results pages and catalog pages
            type: integer
    ResponseWithInterval:
      description: Response for a request with the field `interval`
      type: array
      items:
        type: object
        additionalProperties: false
        properties:
          interval:
            description: Requested time range
            type: object
            properties:
              begin:
                description: Beginning of the requested period
                type: string
                format: date
              end:
                description: End of the requested period
                type: string
                format: date
          views:
            type: integer
            description: |
              Number of views. <br>
              For all days of the requested range, across all WB articles and platforms
          clicks:
            type: integer
            description: |
              Number of clicks. <br>
              For all days of the requested range, across all WB articles and platforms
          ctr:
            type: number
            description: |
              Clickability metric.<br>
              The ratio of clicks to displays. Expressed as a percentage<br>
              For all days of the requested range, across all WB articles and platforms
          cpc:
            type: number
            description: |
              Average cost per click, ₽.<br>
              For all days of the requested range, across all WB articles and platforms
          sum:
            type: number
            description: |
              Expenses, ₽.<br>
              For all days of the requested range, across all WB articles and platforms
          atbs:
            type: integer
            description: |
              Number of products added to the basket.<br>
              For all days of the requested range, across all WB articles and platforms
          orders:
            type: integer
            description: |
              Orders number.<br>
              For all days of the requested range, across all WB articles and platforms
          cr:
            type: number
            description: |
              CR(conversion rate) is the ratio of the number of orders to the total number of visits to the campaign.<br>
              For all days of the requested range, across all WB articles and platforms
          shks:
            type: integer
            description: |
              Number of ordered products, pcs.<br>
              For all days of the requested range, across all WB articles and platforms
          sum_price:
            type: number
            description: |
              Orders amount, ₽<br>
              For all days of the requested range, across all WB articles and platforms
          days:
            $ref: '#/components/schemas/Days'
          boosterStats:
            $ref: '#/components/schemas/BoosterStats'
          advertId:
            description: Campaign ID
            type: integer
    ResponseWithDate:
      description: Response for a request with the field `dates`
      type: array
      nullable: true
      items:
        type: object
        additionalProperties: false
        properties:
          dates:
            description: Dates for which information is to be provided
            type: array
            items:
              type: string
              format: date
          views:
            type: integer
            description: |
              Number of views. <br>
              For all days, across all WB articles and platforms
          clicks:
            type: integer
            description: |
              Number of clicks.<br>
              For all days, across all WB articles and platforms
          ctr:
            type: number
            description: |
              Clickability metric.<br>
              The ratio of clicks to displays. Expressed as a percentage<br>
              For all days, across all WB articles and platforms
          cpc:
            type: number
            description: |
              Average cost per click, ₽.<br>
              For all days, across all WB articles and platforms
          sum:
            type: number
            description: |
              Expenses, ₽.<br>
              For all days, across all WB articles and platforms
          atbs:
            type: integer
            description: |
              Number of products added to the basket.<br>
              For all days, across all WB articles and platforms
          orders:
            type: integer
            description: |
              Orders number.<br>
              For all days, across all WB articles and platforms
          cr:
            type: number
            description: |
              CR(conversion rate) is the ratio of the number of orders to the total number of visits to the campaign.<br>
              For all days, across all WB articles and platforms
          shks:
            type: integer
            description: |
              Number of ordered products, pcs.<br>
              For all days, across all WB articles and platforms
          sum_price:
            type: number
            description: |
              Orders amount, ₽<br>
              For all days, across all WB articles and platforms
          days:
            $ref: '#/components/schemas/Days'
          boosterStats:
            $ref: '#/components/schemas/BoosterStats'
          advertId:
            description: Campaign ID
            type: integer
    responseAdvError1:
      type: object
      properties:
        error:
          type: string
    V0KeywordsStatistic:
      type: object
      required:
        - keyword
        - views
        - clicks
        - ctr
        - sum
      properties:
        clicks:
          type: integer
          description: Number of clicks
        ctr:
          type: number
          description: CTR (Click-Through Rate) — clickability metric
        keyword:
          type: string
          description: Keyword
        sum:
          type: number
          description: Total cost per keyword
        views:
          type: integer
          description: Number of displays
    V0KeywordsStatistics:
      type: object
      required:
        - date
        - stats
      properties:
        date:
          type: string
          format: date
          description: Date
        stats:
          type: array
          items:
            $ref: '#/components/schemas/V0KeywordsStatistic'
    V0KeywordsStatisticsResponse:
      type: object
      required:
        - keywords
      properties:
        keywords:
          type: array
          items:
            $ref: '#/components/schemas/V0KeywordsStatistics'
    ErrorResponse:
      type: object
      required:
        - type
        - message
      properties:
        type:
          type: string
        message:
          type: string
  examples:
    DateRangeExceeded:
      description: Data can be retrieved for a maximum of 7 days
      value:
        message: the period of stats cannot be more than one week
        type: bad request
    TokenMissing:
      description: Token missing
      value: 'proxy: unauthorized'
    TokenInvalid:
      description: Invalid token
      value: 'proxy: invalid token'
    TokenNotFound:
      description: Token deleted
      value: 'proxy: not found'
    FeedbackErr400:
      description: Bad request
      value:
        data: null
        error: true
        errorText: Something went wrong
        additionalErrors: null
        requestId: 734c9ea8-39e5-45c9-8cad-f03c13f733e9
    FeedbackErr403:
      description: ''
      value:
        data: null
        error: true
        errorText: Access denied
        additionalErrors: null
        requestId: 734c9ea8-39e5-45c9-8cad-f03c13f733e9
    SalesFunnelProductRes:
      description: ''
      value: |
        nmID, dt, openCardCount, addToCartCount, ordersCount, ordersSumRub, buyoutsCount, buyoutsSumRub, cancelCount, cancelSumRub, addToCartConversion, cartToOrderConversion, buyoutPercent
        70027655,2024-11-21,1,0,0,0,0,0,0,0,0,0,0
        ...
        ...
        150317666,2024-11-21,2,0,0,0,0,0,0,0,0,0,0
    SalesFunnelGroupRes:
      description: ''
      value: |
        dt, openCardCount, addToCartCount, ordersCount, ordersSumRub, buyoutsCount, buyoutsSumRub, cancelCount, cancelSumRub, addToCartConversion, cartToOrderConversion, buyoutPercent
        2024-11-21,1,0,0,0,0,0,0,0,0,0,0
        ...
        ...
        2024-11-21,2,0,0,0,0,0,0,0,0,0,0
    SearchReportGroupRes:
      description: ''
      value: |
        SubjectName,SubjectID,BrandName,TagID,AveragePosition,OpenCard,AddToCart,OpenToCart,Orders,CartToOrder,Visibility,AveragePositionPast,OpenCardPast,AddToCartPast,OpenToCartPast,OrdersPast,CartToOrderPast,VisibilityPast
        Смартфоны,0,Abble,0,1,4,0,0,0,0,100,1,8,0,0,0,0,100
        Смартфоны,0,abble,0,1,63,0,0,0,0,100,1,91,0,0,0,0,100
    SearchReportProductRes:
      description: ''
      value: |
        NmID,VendorCode,Name,Subject,Brand,IsAdvertised,IsRated,Rating,FeedbackRating,MinPrice,MaxPrice,AveragePosition,OpenCard,AddToCart,OpenToCart,Orders,CartToOrder,Visibility,AveragePositionPast,OpenCardPast,AddToCartPast,OpenToCartPast,OrdersPast,CartToOrderPast,VisibilityPast
        268913787,wb3ha2668w,iPhone 13 256 ГБ Серебристый,Смартфоны,abble,false,true,10,0,140000,140000,1,51,0,0,0,0,100,1,91,0,0,0,0,100
        246935327,wb729wy604,,Бирки для ключей,,false,true,1.5,0,89,89,1,37,19,51,6,32,100,1,14,21,150,3,14,100
    SearchReportTextRes:
      description: ''
      value: |
        Text,NmID,SubjectName,BrandName,VendorCode,Name,Rating,FeedbackRating,MinPrice,MaxPrice,Frequency,MedianPosition,AveragePosition,OpenCard,OpenCardPercentile,AddToCart,AddToCartPercentile,OpenToCart,OpenToCartPercentile,Orders,OrdersPercentile,CartToOrder,CartToOrderPercentile,Visibility,FrequencyPast,MedianPositionPast,AveragePositionPast,OpenCardPast,AddToCartPast,OpenToCartPast,OrdersPast,CartToOrderPast,VisibilityPast
        267945415,267945415,Термокомплекты для малышей,Lopsa,wb44h5ku68,1,5.5,0.0,47,47,156,1,1,235,100,98,100,42,100,0,100,0,100,100,15,1,1,19,16,84,0,0,100
        термобелье мужское,267945415,Термокомплекты для малышей,Lopsa,wb44h5ku68,1,5.5,0.0,47,47,52633,2,2,5,0,0,0,0,0,0,0,0,0,100,49975,0,0,0,0,0,0,0,0
        267945415,296070764,Термокомплекты для малышей,Lopsa,wb51k31eyg,2,1.5,0.0,88,88,156,1,1,82,100,22,100,27,100,0,100,0,100,100,15,5,5,1,1,100,0,0,100
        термобелье мужское,296070764,Термокомплекты для малышей,Lopsa,wb51k31eyg,2,1.5,0.0,88,88,52633,3,3,2,0,0,0,0,0,0,0,0,0,100,49975,0,0,0,0,0,0,0,0
        211131895,211131895,Костюмы,H&M,wb51k31eyg!!!,костюм,10.0,5.0,102,102,36,1,1,44,100,6,100,14,0,5,100,83,100,100,0,0,0,0,0,0,0,0,0
        221411786,211131895,Костюмы,H&M,wb51k31eyg!!!,костюм,10.0,5.0,102,102,19,1,1,2,11,0,0,0,0,0,0,0,0,100,3,0,0,0,0,0,0,0,0
        женские блузки,211131895,Костюмы,H&M,wb51k31eyg!!!,костюм,10.0,5.0,102,102,38383,14,14,1,0,0,0,0,0,0,0,0,0,79,29764,0,0,0,0,0,0,0,0
    StocksReportRes:
      description: ''
      value: |
        VendorCode,Name,NmID,SubjectName,BrandName,SizeName,RegionName,OfficeName,Availability,OrdersCount,OrdersSum,BuyoutCount,BuyoutSum,BuyoutPercent,AvgOrders,StockCount,StockSum,SaleRate,AvgStockTurnover,ToClientCount,FromClientCount,Price,OfficeMissingTime,LostOrdersCount,LostOrdersSum,LostBuyoutsCount,LostBuyoutsSum,AvgOrdersByMonth_04.2014,AvgOrdersByMonth_05.2014,AvgOrdersByMonth_06.2014,AvgOrdersByMonth_07.2014
        "12345","Product A","456789","Clothing","Brand X","M","Central","Office1",deficient,5432,2678921,2456,154321,9,48.3,76321,674321678,270,310,15,17,1000,45,3210.5,4578123.56,134.2,196512.78,62412.64,29857.97,0.00,72465.85
        "12346","Product B","456790","Shoes","Brand Y","L","North","Office2",deficient,3876,1978456,1238,98231,7,32.1,59123,487321456,265,300,12,16,1200,30,2458.8,3567821.45,98.4,157843.21,48664.95,0.00,0.00,74502.72
        "12347","Product C","456791","Electronics","Brand Z","S","South","Office3",deficient,4210,2431456,2158,145632,8,35.7,68954,523214789,275,315,18,20,2500,50,3187.6,4978123.87,153.2,214512.54,56237.24,2172.75,74665.30,14296.76
        "12348","Product D","456792","Toys","Brand W","XL","East","Office4",deficient,2943,1394521,987,65432,5,26.4,48213,396412356,260,298,13,15,800,20,1923.4,2345123.12,76.5,112348.12,26053.02,0.00,93456.00,63705.26
        "12349","Product E","456793","Furniture","Brand V","XXL","West","Office5",deficient,1874,987432,543,32123,3,15.6,38217,287943215,255,290,10,12,1500,25,1023.2,1543210.45,48.9,85743.56,38448.89,48599.20,28445.90,90146.29
    RequestWithoutParam:
      description: Request without parameters
      value:
        - id: 107024
    RequestAggregate:
      description: Request with intervals and dates
      value:
        - id: 107024
          interval:
            begin: '2023-10-21'
            end: '2023-10-21'
        - id: 107024
          dates:
            - '2023-10-22'
            - '2023-10-26'
    RespStatMediaInterval:
      description: Response for interval queries
      value:
        - interval:
            begin: '2023-10-21'
            end: '2023-10-25'
          stats:
            - item_id: 62237
              item_name: Gloria Jeans
              category_name: Детям
              advert_type: 1
              place: 2
              views: 11849
              clicks: 209
              cr: 0.48
              ctr: 1.76
              date_from: '2023-10-21T00:00:00+03:00'
              date_to: '2023-10-27T23:59:59+03:00'
              subject_name: Одежда
              atbs: 4
              orders: 1
              price: 175000
              cpc: 837.32
              status: 6
              daily_stats:
                - date: '2023-10-21T00:00:00+03:00'
                  app_type_stats:
                    - app_type: 1
                      stats:
                        - views: 2017
                          clicks: 27
                          atbs: 1
                          ctr: 1.34
              expenses: 175000
              cr1: 1.91
              cr2: 25
    RespStatMediaDates:
      description: Response when requested with dates
      value:
        - dates:
            - '2023-10-26'
            - '2023-10-22'
          stats:
            - item_id: 62237
              item_name: Gloria Jeans
              category_name: Детям
              advert_type: 1
              place: 2
              views: 4584
              clicks: 74
              cr: 1.35
              ctr: 1.61
              date_from: '2023-10-21T00:00:00+03:00'
              date_to: '2023-10-27T23:59:59+03:00'
              subject_name: Одежда
              atbs: 2
              orders: 1
              price: 175000
              cpc: 2364.86
              status: 6
              daily_stats:
                - date: '2023-10-22T00:00:00+03:00'
                  app_type_stats:
                    - app_type: 1
                      stats:
                        - views: 2384
                          clicks: 33
                          atbs: 2
                          orders: 1
                          cr: 3.03
                          ctr: 1.38
              expenses: 175000
              cr1: 2.7
              cr2: 50
    RespStatMediaWithoutParam:
      description: Response to request without parameters
      value:
        - stats:
            - item_id: 62237
              item_name: Gloria Jeans
              category_name: Детям
              advert_type: 1
              place: 2
              views: 11849
              clicks: 209
              cr: 0.48
              ctr: 1.76
              date_from: '2023-10-21T00:00:00+03:00'
              date_to: '2023-10-27T23:59:59+03:00'
              subject_name: Одежда
              atbs: 4
              orders: 1
              price: 175000
              cpc: 837.32
              status: 6
              daily_stats:
                - date: '2023-10-21T00:00:00+03:00'
                  app_type_stats:
                    - app_type: 1
                      stats:
                        - views: 2017
                          clicks: 27
                          atbs: 1
                          ctr: 1.34
              expenses: 175000
              cr1: 1.91
              cr2: 25
    RespStatMediaAggregate:
      description: Response to requests with intervals and dates
      value:
        - interval:
            begin: '2023-10-21'
            end: '2023-10-25'
          stats:
            - item_id: 62237
              item_name: Gloria Jeans
              category_name: Детям
              advert_type: 1
              place: 2
              views: 11849
              clicks: 209
              cr: 0.48
              ctr: 1.76
              date_from: '2023-10-21T00:00:00+03:00'
              date_to: '2023-10-27T23:59:59+03:00'
              subject_name: Одежда
              atbs: 4
              orders: 1
              price: 175000
              cpc: 837.32
              status: 6
              daily_stats:
                - date: '2023-10-21T00:00:00+03:00'
                  app_type_stats:
                    - app_type: 1
                      stats:
                        - views: 2017
                          clicks: 27
                          atbs: 1
                          ctr: 1.34
              expenses: 175000
              cr1: 1.91
              cr2: 25
        - dates:
            - '2023-10-26'
            - '2023-10-22'
          stats:
            - item_id: 62237
              item_name: Gloria Jeans
              category_name: Детям
              advert_type: 1
              place: 2
              views: 4584
              clicks: 74
              cr: 1.35
              ctr: 1.61
              date_from: '2023-10-21T00:00:00+03:00'
              date_to: '2023-10-27T23:59:59+03:00'
              subject_name: Одежда
              atbs: 2
              orders: 1
              price: 175000
              cpc: 2364.86
              status: 6
              daily_stats:
                - date: '2023-10-22T00:00:00+03:00'
                  app_type_stats:
                    - app_type: 1
                      stats:
                        - views: 2384
                          clicks: 33
                          atbs: 2
                          orders: 1
                          cr: 3.03
                          ctr: 1.38
              expenses: 175000
              cr1: 2.7
              cr2: 50
        - stats:
            - item_id: 62237
              item_name: Gloria Jeans
              category_name: Детям
              advert_type: 1
              place: 2
              views: 11849
              clicks: 209
              cr: 0.48
              ctr: 1.76
              date_from: '2023-10-21T00:00:00+03:00'
              date_to: '2023-10-27T23:59:59+03:00'
              subject_name: Одежда
              atbs: 4
              orders: 1
              price: 175000
              cpc: 837.32
              status: 6
              daily_stats:
                - date: '2023-10-21T00:00:00+03:00'
                  app_type_stats:
                    - app_type: 1
                      stats:
                        - views: 2017
                          clicks: 27
                          atbs: 1
                          ctr: 1.34
              expenses: 175000
              cr1: 1.91
              cr2: 25
    RequestWithDate:
      description: Request with dates
      value:
        - id: 8960367
          dates:
            - '2023-10-07'
            - '2023-10-06'
        - id: 9876543
          dates:
            - '2023-10-07'
            - '2023-12-06'
    RequestWithInterval:
      description: Request with intervals
      value:
        - id: 8960367
          interval:
            begin: '2023-10-08'
            end: '2023-10-10'
        - id: 78978565
          interval:
            begin: '2023-09-08'
            end: '2023-09-11'
    RequestWithoutIDParam:
      description: Request only with campaign IDs
      value:
        - id: 8960367
        - id: 9876543
    ResponseWithDate:
      description: Response for a request with the field `date`
      value:
        - views: 1052
          clicks: 2
          ctr: 0.19
          cpc: 0.09
          sum: 177.7
          atbs: 0
          orders: 0
          cr: 0
          shks: 0
          sum_price: 0
          dates:
            - '2023-10-07'
            - '2023-10-06'
          days:
            - date: '2023-10-06T03:00:00+03:00'
              views: 414
              clicks: 1
              ctr: 0.24
              cpc: 70
              sum: 70
              atbs: 0
              orders: 0
              cr: 0
              shks: 0
              sum_price: 0
              apps:
                - views: 228
                  clicks: 0
                  ctr: 0
                  cpc: 0
                  sum: 38.71
                  atbs: 0
                  orders: 0
                  cr: 0
                  shks: 0
                  sum_price: 0
                  nm:
                    - views: 25
                      clicks: 0
                      ctr: 0
                      cpc: 0
                      sum: 4
                      atbs: 0
                      orders: 0
                      cr: 0
                      shks: 0
                      sum_price: 0
                      name: Тапочки
                      nmId: 111111111111
                  appType: 1
          boosterStats:
            - date: '2023-10-07T00:00:00Z'
              nm: 170095908
              avg_position: 348
          advertId: 10524818
    ResponseWithInterval:
      description: Response for a request with the field `interval`
      value:
        - interval:
            begin: '2023-10-08'
            end: '2023-10-10'
          views: 1052
          clicks: 2
          ctr: 0.19
          cpc: 0.09
          sum: 177.7
          atbs: 0
          orders: 0
          cr: 0
          shks: 0
          sum_price: 0
          days:
            - date: '2023-10-08T03:00:00+03:00'
              views: 730
              clicks: 1
              ctr: 0.14
              cpc: 124.91
              sum: 124.91
              atbs: 0
              orders: 0
              cr: 0
              shks: 0
              sum_price: 0
              apps:
                - views: 424
                  clicks: 1
                  ctr: 0.24
                  cpc: 72.63
                  sum: 72.63
                  atbs: 0
                  orders: 0
                  cr: 0
                  shks: 0
                  sum_price: 0
                  nm:
                    - views: 424
                      clicks: 1
                      ctr: 0.24
                      cpc: 72.63
                      sum: 72.63
                      atbs: 0
                      orders: 0
                      cr: 0
                      shks: 0
                      sum_price: 0
                      name: Тапочки
                      nmId: 1111111111111
                  appType: 1
          boosterStats:
            - date: '2023-10-08T00:00:00Z'
              nm: 1111111111111
              avg_position: 395
          advertId: 10524818
    IncorrectSupplierIdAdv:
      value: Incorrect seller identifier
    CampaignNotFoundAdv:
      value:
        error: Campaign not found
    responseIncorrectBeginDate:
      value:
        error: Incorrect start date
    responseIncorrectEndDate:
      value:
        error: Incorrect end date
    AvailableOnlyForAutoCampaign:
      description: Available only for automatic campaigns
      value:
        error: Доступно только для автоматических кампаний
    AvailableOnlyActiveCampaign:
      description: Available only for active campaigns
      value:
        error: Доступно только для активных кампаний
  securitySchemes:
    HeaderApiKey:
      type: apiKey
      name: Authorization
      in: header
  responses:
    '401':
      description: Unauthorized
      content:
        application/json:
          schema:
            type: object
            properties:
              title:
                type: string
                description: Error title
              detail:
                type: string
                description: Error details
              code:
                type: string
                description: Internal error code
              requestId:
                type: string
                description: Unique request ID
              origin:
                type: string
                description: WB internal service ID
              status:
                type: number
                description: HTTP status code
              statusText:
                type: string
                description: Text of the HTTP status code
              timestamp:
                type: string
                format: RFC3339
                description: Request date and time
          example:
            title: unauthorized
            detail: 'token problem; token is malformed: could not base64 decode signature: illegal base64 data at input byte 84'
            code: 07e4668e--a53a3d31f8b0-[UK-oWaVDUqNrKG]; 03bce=277; 84bd353bf-75
            requestId: 7b80742415072fe8b6b7f7761f1d1211
            origin: s2s-api-auth-catalog
            status: 401
            statusText: Unauthorized
            timestamp: '2024-09-30T06:52:38Z'
    '429':
      description: Too many requests
      content:
        application/json:
          schema:
            type: object
            properties:
              title:
                type: string
                description: Error title
              detail:
                type: string
                description: Error details
              code:
                type: string
                description: Internal error code
              requestId:
                type: string
                description: Unique request ID
              origin:
                type: string
                description: WB internal service ID
              status:
                type: number
                description: HTTP status code
              statusText:
                type: string
                description: Text of the HTTP status code
              timestamp:
                type: string
                format: RFC3339
                description: Request date and time
          example:
            title: too many requests
            detail: limited by c122a060-a7fb-4bb4-abb0-32fd4e18d489
            code: 07e4668e-ac2242c5c8c5-[UK-4dx7JUdskGZ]
            requestId: 9d3c02cc698f8b041c661a7c28bed293
            origin: s2s-api-auth-catalog
            status: 429
            statusText: Too Many Requests
            timestamp: '2024-09-30T06:52:38Z'
