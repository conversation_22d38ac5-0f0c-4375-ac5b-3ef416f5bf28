package wbapi

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/util/gconv"
	"time"

	"github.com/go-resty/resty/v2"
)

const (
	promotionBaseURL = "https://advert-api.wildberries.ru" // 推广服务基础 URL
)

// Campaign 广告活动状态
const (
	CampaignStatusDeleting = -1 // 正在删除中
	CampaignStatusReady    = 4  // 准备启动
	CampaignStatusComplete = 7  // 活动完成
	CampaignStatusDeclined = 8  // 已拒绝
	CampaignStatusRunning  = 9  // 正在展示
	CampaignStatusPaused   = 11 // 已暂停
)

// Campaign 广告活动类型
const (
	CampaignTypeCatalog        = 4 // 目录广告（已废弃）
	CampaignTypeContent        = 5 // 内容广告（已废弃）
	CampaignTypeSearch         = 6 // 搜索广告（已废弃）
	CampaignTypeRecommendation = 7 // 主页推荐广告（已废弃）
	CampaignTypeAutomatic      = 8 // 自动广告
	CampaignTypeAuction        = 9 // 竞价广告
)

// WriteOffType 扣费类型
const (
	WriteOffTypeAccount = 0 // 账户
	WriteOffTypeBalance = 1 // 余额
	WriteOffTypeBonuses = 3 // 奖金
)

// PromotionService 推广服务
type PromotionService struct {
	client      *resty.Client // 独立的HTTP客户端
	rateLimiter *PromotionRateLimiter
}

// newPromotionService 创建新的推广服务
func newPromotionService(c *Client) *PromotionService {
	// 创建独立的HTTP客户端
	httpClient := resty.New()
	httpClient.SetBaseURL(promotionBaseURL)
	// 复制原始客户端的通用设置
	if c.baseClient != nil {
		httpClient.SetTimeout(c.baseClient.GetClient().Timeout)
		// 复制认证头
		for k, v := range c.baseClient.Header {
			httpClient.SetHeader(k, v[0])
		}
	}

	return &PromotionService{
		client:      httpClient,
		rateLimiter: NewPromotionRateLimiter(),
	}
}

// CampaignCount 活动列表响应
type CampaignCount struct {
	Adverts []struct {
		Type       int `json:"type"`
		Status     int `json:"status"`
		Count      int `json:"count"`
		AdvertList []struct {
			AdvertID   int       `json:"advertId"`
			ChangeTime time.Time `json:"changeTime"`
		} `json:"advert_list"`
	} `json:"adverts"`
	All int `json:"all"`
}

// GetCampaignCount 获取活动列表
func (s *PromotionService) GetCampaignCount() (*CampaignCount, error) {
	// 等待限流器许可
	if err := s.rateLimiter.campaignCountLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp := &CampaignCount{}
	_, err := s.client.R().
		SetResult(resp).
		Get("/adv/v1/promotion/count")

	if err != nil {
		return nil, err
	}

	return resp, nil
}

// Config 推广配置响应
type Config struct {
	Categories []struct {
		ID     int    `json:"id"`
		Name   string `json:"name"`
		CPMMin int    `json:"cpm_min"`
	} `json:"categories"`
	Config []struct {
		Description string `json:"description"`
		Name        string `json:"name"`
		Value       string `json:"value"`
	} `json:"config"`
}

// GetConfig 获取推广配置值
func (s *PromotionService) GetConfig() (*Config, error) {
	// 等待限流器许可
	if err := s.rateLimiter.configLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp := &Config{}
	_, err := s.client.R().
		SetResult(resp).
		Get("/adv/v0/config")

	if err != nil {
		return nil, err
	}

	return resp, nil
}

// Subject 商品类目信息
type Subject struct {
	ID    int    `json:"id"`    // 类目ID
	Name  string `json:"name"`  // 类目名称
	Count int    `json:"count"` // 该类目下的商品数量
}

// GetSubjects 获取可用于广告活动的商品类目
func (s *PromotionService) GetSubjects() ([]Subject, error) {
	// 等待限流器许可
	if err := s.rateLimiter.subjectsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result []Subject
	_, err := s.client.R().
		SetResult(&result).
		Get("/adv/v1/supplier/subjects")

	if err != nil {
		return nil, err
	}

	return result, nil
}

// NomenclatureItem 商品信息
type NomenclatureItem struct {
	Title     string `json:"title"`     // 商品名称
	NM        int    `json:"nm"`        // WB商品编号
	SubjectID int    `json:"subjectId"` // 类目ID
}

// GetNomenclatures 获取可用于广告活动的商品列表
func (s *PromotionService) GetNomenclatures(subjectIDs []int) ([]NomenclatureItem, error) {
	// 等待限流器许可
	if err := s.rateLimiter.nomenclaturesLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result []NomenclatureItem
	_, err := s.client.R().
		SetBody(subjectIDs).
		SetResult(&result).
		Post("/adv/v2/supplier/nms")

	if err != nil {
		return nil, err
	}

	return result, nil
}

// AutoCampaignRequest 自动广告活动创建请求
type AutoCampaignRequest struct {
	Type      int    `json:"type"`      // 活动类型，固定为 8
	Name      string `json:"name"`      // 活动名称（最大128字符）
	SubjectID int    `json:"subjectId"` // 创建活动的商品ID
	Sum       int    `json:"sum"`       // 充值金额
	BType     int    `json:"btype"`     // 扣费类型
	OnPause   bool   `json:"on_pause"`  // 创建后是否暂停
	NMS       []int  `json:"nms"`       // WB商品编号数组，最多100个
	CPM       int    `json:"cpm"`       // 出价
}

// CreateAutoCampaign 创建自动广告活动
func (s *PromotionService) CreateAutoCampaign(req *AutoCampaignRequest) (string, error) {
	// 等待限流器许可
	if err := s.rateLimiter.autoCreateLimiter.Wait(context.Background()); err != nil {
		return "", fmt.Errorf("rate limit exceeded: %w", err)
	}

	req.Type = CampaignTypeAutomatic

	var result string
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post("/adv/v1/save-ad")

	if err != nil {
		return "", err
	}

	if resp.IsError() {
		return "", fmt.Errorf("create auto campaign failed: %s", resp.String())
	}

	return result, nil
}

// AuctionCampaignRequest 竞价广告活动创建请求
type AuctionCampaignRequest struct {
	Name          string `json:"name"`          // 活动名称
	SubjectID     int    `json:"subjectId"`     // 商品ID
	Sum           int    `json:"sum"`           // 充值金额
	BType         int    `json:"btype"`         // 扣费类型
	AutoParams    bool   `json:"autoParams"`    // 是否自动设置参数
	SearchPromot  bool   `json:"searchPromot"`  // 是否在搜索结果中推广
	CatalogPromot bool   `json:"catalogPromot"` // 是否在目录中推广
	CardsPromot   bool   `json:"cardsPromot"`   // 是否在商品卡片中推广
	NMS           []int  `json:"nms"`           // WB商品编号数组
	CPM           int    `json:"cpm"`           // 出价
}

// CreateAuctionCampaign 创建竞价广告活动
func (s *PromotionService) CreateAuctionCampaign(req *AuctionCampaignRequest) (string, error) {
	// 等待限流器许可
	if err := s.rateLimiter.auctionCreateLimiter.Wait(context.Background()); err != nil {
		return "", fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result string
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post("/adv/v2/seacat/save-ad")

	if err != nil {
		return "", err
	}

	if resp.IsError() {
		return "", fmt.Errorf("create auction campaign failed: %s", resp.String())
	}

	return result, nil
}

// BidItem 出价信息
type BidItem struct {
	AdvertID int `json:"advert_id"` // 广告活动ID
	NmBids   []struct {
		NM  int `json:"nm"`  // 商品编号
		Bid int `json:"bid"` // 出价
	} `json:"nm_bids"` // 商品出价列表
}

// UpdateBids 批量更新商品出价
func (s *PromotionService) UpdateBids(bids []BidItem) error {
	// 等待限流器许可
	if err := s.rateLimiter.bidsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(map[string]interface{}{
			"bids": bids,
		}).
		Patch("/adv/v0/bids")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("update bids failed: %s", resp.String())
	}

	return nil
}

// DeleteCampaign 删除广告活动
func (s *PromotionService) DeleteCampaign(campaignID int) error {
	// 等待限流器许可
	if err := s.rateLimiter.deleteLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", campaignID)).
		Get("/adv/v0/delete")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("delete campaign failed: %s", resp.String())
	}

	return nil
}

// RenameCampaign 重命名广告活动
func (s *PromotionService) RenameCampaign(campaignID int, newName string) error {
	// 等待限流器许可
	if err := s.rateLimiter.renameLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(map[string]interface{}{
			"advertId": campaignID,
			"name":     newName,
		}).
		Post("/adv/v0/rename")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("rename campaign failed: %s", resp.String())
	}

	return nil
}

// PauseCampaign 暂停广告活动
func (s *PromotionService) PauseCampaign(campaignID int) error {
	// 等待限流器许可
	if err := s.rateLimiter.pauseLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", campaignID)).
		Get("/adv/v0/pause")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("pause campaign failed: %s", resp.String())
	}

	return nil
}

// StopCampaign 停止广告活动
func (s *PromotionService) StopCampaign(campaignID int) error {
	// 等待限流器许可
	if err := s.rateLimiter.stopLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", campaignID)).
		Get("/adv/v0/stop")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("stop campaign failed: %s", resp.String())
	}

	return nil
}

// StartCampaign 启动广告活动
func (s *PromotionService) StartCampaign(campaignID int) error {
	// 等待限流器许可
	if err := s.rateLimiter.startLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", campaignID)).
		Get("/adv/v0/start")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("start campaign failed: %s", resp.String())
	}

	return nil
}

// CampaignQueryParams 活动查询参数
type CampaignQueryParams struct {
	Status    *int    `url:"status,omitempty"`    // 活动状态
	Type      *int    `url:"type,omitempty"`      // 活动类型
	Order     *string `url:"order,omitempty"`     // 排序字段：create(创建时间)、change(修改时间)、id(活动ID)
	Direction *string `url:"direction,omitempty"` // 排序方向：desc(降序)、asc(升序)
}

// CampaignSubject 活动类目信息
type CampaignSubject struct {
	ID          int    `json:"id"`          // 类目ID
	Name        string `json:"name"`        // 类目名称
	Status      int    `json:"status"`      // 状态
	SubjectID   int    `json:"subjectId"`   // 商品类目ID
	SubjectName string `json:"subjectName"` // 商品类目名称
}

// CampaignStatistics 活动统计信息
type CampaignStatistics struct {
	Views    int     `json:"views"`    // 展示量
	Clicks   int     `json:"clicks"`   // 点击量
	CTR      float64 `json:"ctr"`      // 点击率
	Orders   int     `json:"orders"`   // 订单量
	CR       float64 `json:"cr"`       // 转化率
	OrderSum float64 `json:"orderSum"` // 订单金额
	Spending float64 `json:"spending"` // 花费
	ROI      float64 `json:"roi"`      // 投入产出比
	CPM      float64 `json:"cpm"`      // 千次展现成本
	CPC      float64 `json:"cpc"`      // 点击成本
	CPO      float64 `json:"cpo"`      // 订单成本
	DateTo   string  `json:"dateTo"`   // 统计截止日期
	DateFrom string  `json:"dateFrom"` // 统计起始日期
}

// 自动广告参数
type AutoParams struct {
	CPM     int `json:"cpm"` // 基础出价（每千次展现费用）
	Subject struct {
		Name string `json:"name"` // 类目名称
		ID   int    `json:"id"`   // 类目ID
	} `json:"subject"` // 投放类目信息
	NMS    []int `json:"nms"` // 参与推广的商品ID列表
	Active struct {
		Carousel bool `json:"carousel"` // 是否开启轮播推广
		Recom    bool `json:"recom"`    // 是否开启相似推荐
		Booster  bool `json:"booster"`  // 是否开启推广加速
	} `json:"active"` // 推广位置设置
	NMCPM []struct {
		NM  int `json:"nm"`  // 商品ID
		CPM int `json:"cpm"` // 该商品的独立出价
	} `json:"nmCPM"` // 商品单独出价列表
}

// 竞价广告参数
type UnitedParam struct {
	CatalogCPM int `json:"catalogCPM"` // 目录页展示出价
	SearchCPM  int `json:"searchCPM"`  // 搜索页展示出价
	Subject    struct {
		ID   int    `json:"id"`   // 类目ID
		Name string `json:"name"` // 类目名称
	} `json:"subject"` // 投放类目信息
	NMS []int `json:"nms"` // 参与推广的商品ID列表
}

// 竞价出价信息
type AuctionMultibid struct {
	NM  int `json:"nm"`  // 商品ID
	Bid int `json:"bid"` // 商品出价（每千次展现费用）
}

// CampaignInfo 活动详细信息
type CampaignInfo struct {
	AdvertID         int                `json:"advertId"`          // 活动ID
	Name             string             `json:"name"`              // 活动名称
	Status           int                `json:"status"`            // 活动状态
	Type             int                `json:"type"`              // 活动类型
	StartTime        *time.Time         `json:"startTime"`         // 开始时间
	CreateTime       time.Time          `json:"createTime"`        // 创建时间
	ChangeTime       time.Time          `json:"changeTime"`        // 修改时间
	EndTime          *time.Time         `json:"endTime"`           // 结束时间
	Budget           float64            `json:"budget"`            // 预算
	Balance          float64            `json:"balance"`           // 余额
	Subjects         []CampaignSubject  `json:"subjects"`          // 活动类目信息
	Statistics       CampaignStatistics `json:"statistics"`        // 活动统计信息
	DailyBudget      *float64           `json:"dailyBudget"`       // 日预算
	AutoParams       *AutoParams        `json:"autoParams"`        // 自动广告参数
	SearchPromot     *bool              `json:"searchPromot"`      // 是否搜索推广
	CatalogPromot    *bool              `json:"catalogPromot"`     // 是否目录推广
	CardsPromot      *bool              `json:"cardsPromot"`       // 是否卡片推广
	UnitedParams     []UnitedParam      `json:"unitedParams"`      // 竞价广告参数
	AuctionMultibids []AuctionMultibid  `json:"auction_multibids"` // 竞价出价信息
}

// GetCampaignsByParams 通过查询参数获取活动信息
func (s *PromotionService) GetCampaignsByParams(params *CampaignQueryParams) ([]CampaignInfo, error) {
	// 等待限流器许可
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result []CampaignInfo
	req := s.client.R()

	if params != nil {
		if params.Status != nil {
			req.SetQueryParam("status", fmt.Sprintf("%d", *params.Status))
		}
		if params.Type != nil {
			req.SetQueryParam("type", fmt.Sprintf("%d", *params.Type))
		}
		if params.Order != nil {
			req.SetQueryParam("order", *params.Order)
		}
		if params.Direction != nil {
			req.SetQueryParam("direction", *params.Direction)
		}
	}

	// 更新为v2版本的API路径
	resp, err := req.SetResult(&result).Post("/adv/v1/promotion/adverts")
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("get campaigns failed: %s", resp.String())
	}

	return result, nil
}

// GetCampaignsByIDs 通过活动ID列表获取活动信息
func (s *PromotionService) GetCampaignsByIDs(ids []int) ([]CampaignInfo, error) {
	// 使用与 GetCampaignsByParams 相同的限流器
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result []CampaignInfo
	resp, err := s.client.R().
		SetBody(ids).
		SetResult(&result).
		Post("/adv/v1/promotion/adverts")

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, fmt.Errorf("get campaigns failed: %s", resp.String())
	}

	return result, nil
}

// SearchKeywordRequest 搜索关键词请求
type SearchKeywordRequest struct {
	AdvertID int      `json:"advertId"` // 广告活动ID
	Pluse    []string `json:"pluse"`    // 加词列表（最多100个）
	Phrase   []string `json:"phrase"`   // 词组列表（最多1000个）
	Strong   []string `json:"strong"`   // 强匹配词列表（最多1000个）
	Excluded []string `json:"excluded"` // 排除词列表（最多1000个）
}

// SetPlusKeywords 设置加词
func (s *PromotionService) SetPlusKeywords(advertID int, keywords []string) error {
	// 等待限流器许可
	if err := s.rateLimiter.plusKeywordLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", advertID)).
		SetBody(map[string]interface{}{
			"pluse": keywords,
		}).
		Post("/adv/v1/search/set-plus")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("set plus keywords failed: %s", resp.String())
	}

	return nil
}

// SetPhraseKeywords 设置词组
func (s *PromotionService) SetPhraseKeywords(advertID int, keywords []string) error {
	// 等待限流器许可
	if err := s.rateLimiter.phraseKeywordLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", advertID)).
		SetBody(map[string]interface{}{
			"phrase": keywords,
		}).
		Post("/adv/v1/search/set-phrase")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("set phrase keywords failed: %s", resp.String())
	}

	return nil
}

// SetStrongKeywords 设置强匹配词
func (s *PromotionService) SetStrongKeywords(advertID int, keywords []string) error {
	// 等待限流器许可
	if err := s.rateLimiter.strongKeywordLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", advertID)).
		SetBody(map[string]interface{}{
			"strong": keywords,
		}).
		Post("/adv/v1/search/set-strong")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("set strong keywords failed: %s", resp.String())
	}

	return nil
}

// SetExcludedKeywords 设置搜索广告排除词
func (s *PromotionService) SetExcludedKeywords(advertID int, keywords []string) error {
	// 等待限流器许可
	if err := s.rateLimiter.excludedKeywordLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", advertID)).
		SetBody(map[string]interface{}{
			"excluded": keywords,
		}).
		Post("/adv/v1/search/set-excluded")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("set excluded keywords failed: %s", resp.String())
	}

	return nil
}

// AutoExcludeRequest 自动广告排除请求
type AutoExcludeRequest struct {
	AdvertID int   `json:"advertId"` // 广告活动ID
	NMS      []int `json:"nms"`      // 要排除的商品ID列表
}

// SetAutoExcluded 设置自动广告排除商品
func (s *PromotionService) SetAutoExcluded(advertID int, keywords []string) error {
	// 等待限流器许可
	if err := s.rateLimiter.autoExcludeLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", advertID)).
		SetBody(map[string]interface{}{
			"excluded": keywords,
		}).
		Post("/adv/v1/auto/set-excluded")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("set auto excluded failed: %s", resp.String())
	}

	return nil
}

// GetAutoNmsToAdd 获取可添加到自动广告的商品列表
func (s *PromotionService) GetAutoNmsToAdd(advertID int) ([]int, error) {
	// 等待限流器许可
	if err := s.rateLimiter.autoNmsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result []int
	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", advertID)).
		SetResult(&result).
		Get("/adv/v1/auto/getnmtoadd")

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, fmt.Errorf("get auto nms to add failed: %s", resp.String())
	}

	return result, nil
}

// AutoUpdateNmRequest 更新自动广告商品请求
type AutoUpdateNmRequest struct {
	AdvertID int   `json:"advertId"` // 广告活动ID
	AddNms   []int `json:"addNms"`   // 要添加的商品ID列表
	DelNms   []int `json:"delNms"`   // 要删除的商品ID列表
}

// UpdateAutoNms 更新自动广告商品列表
func (s *PromotionService) UpdateAutoNms(req *AutoUpdateNmRequest) error {
	// 等待限流器许可
	if err := s.rateLimiter.autoUpdateLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(req).
		Post("/adv/v1/auto/updatenm")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("update auto nms failed: %s", resp.String())
	}

	return nil
}

// StatsRequest 统计请求参数
type StatsRequest struct {
	AdvertID int      `json:"id"`              // 广告活动ID
	Dates    []string `json:"dates,omitempty"` // 指定具体日期列表，格式：YYYY-MM-DD
	Interval *struct {
		Begin string `json:"begin"` // 开始日期，格式：YYYY-MM-DD
		End   string `json:"end"`   // 结束日期，格式：YYYY-MM-DD
	} `json:"interval,omitempty"` // 时间区间
}

// StatsResponse 统计响应
//type StatsResponse struct {
//	Views    int     `json:"views"`  // 展示量
//	Clicks   int     `json:"clicks"` // 点击量
//	CTR      float64 `json:"ctr"`    // 点击率
//	CPC      float64 `json:"cpc"`    // 点击成本
//	Spending float64 `json:"sum"`    // 花费
//	Date     string  `json:"date"`   // 日期
//}

type StatsResponse struct {
	Views    int        `json:"views"`
	Clicks   int        `json:"clicks"`
	Ctr      float64    `json:"ctr"`
	Cpc      float64    `json:"cpc"`
	Sum      float64    `json:"sum"`
	Atbs     int        `json:"atbs"`
	Orders   int        `json:"orders"`
	Cr       float64    `json:"cr"`
	Shks     int        `json:"shks"`
	SumPrice int        `json:"sum_price"`
	Spending float64    `json:"spending"`
	Dates    []string   `json:"dates"`
	Days     []StatsDay `json:"days"`
	AdvertId int        `json:"advertId"`
}

type StatsDay struct {
	Date     time.Time  `json:"date"`
	Views    int        `json:"views"`
	Clicks   int        `json:"clicks"`
	Ctr      float64    `json:"ctr"`
	Cpc      float64    `json:"cpc"`
	Sum      float64    `json:"sum"`
	Atbs     int        `json:"atbs"`
	Orders   int        `json:"orders"`
	Cr       float64    `json:"cr"`
	Shks     int        `json:"shks"`
	SumPrice int        `json:"sum_price"`
	Apps     []StatsApp `json:"apps"`
}
type StatsApp struct {
	Views    int       `json:"views"`
	Clicks   int       `json:"clicks"`
	Ctr      float64   `json:"ctr"`
	Cpc      float64   `json:"cpc"`
	Sum      float64   `json:"sum"`
	Atbs     int       `json:"atbs"`
	Orders   int       `json:"orders"`
	Cr       float64   `json:"cr"`
	Shks     int       `json:"shks"`
	SumPrice int       `json:"sum_price"`
	Nm       []StatsNm `json:"nm"`
	AppType  int       `json:"appType"`
}

type StatsNm struct {
	Views    int     `json:"views"`
	Clicks   int     `json:"clicks"`
	Ctr      float64 `json:"ctr"`
	Cpc      float64 `json:"cpc"`
	Sum      float64 `json:"sum"`
	Atbs     int     `json:"atbs"`
	Orders   int     `json:"orders"`
	Cr       float64 `json:"cr"`
	Shks     int     `json:"shks"`
	SumPrice int     `json:"sum_price"`
	Name     string  `json:"name"`
	NmId     int     `json:"nmId"`
}

// AutoStatWordsResponse 自动广告词组统计响应
type AutoStatWordsResponse struct {
	Excluded []string `json:"excluded"` // 排除词列表
	Clusters []struct {
		Cluster  string   `json:"cluster"`  // 词组
		Count    int      `json:"count"`    // 展示次数
		Keywords []string `json:"keywords"` // 关键词列表
	} `json:"clusters"` // 词组统计
}

// StatWordsResponse 搜索广告词组统计响应
type StatWordsResponse struct {
	Words struct {
		Phrase   []string `json:"phrase"`   // 词组匹配
		Strong   []string `json:"strong"`   // 强匹配
		Excluded []string `json:"excluded"` // 排除词
		Pluse    []string `json:"pluse"`    // 固定词组
		Keywords []struct {
			Keyword string `json:"keyword"` // 关键词
			Count   int    `json:"count"`   // 展示次数
		} `json:"keywords"` // 关键词统计
		Fixed bool `json:"fixed"` // 是否固定关键词
	} `json:"words"` // 词组信息
	Stat []struct {
		AdvertID     int       `json:"advertId"`     // 广告ID
		Keyword      string    `json:"keyword"`      // 关键词
		CampaignName string    `json:"campaignName"` // 活动名称
		Begin        time.Time `json:"begin"`        // 开始时间
		End          time.Time `json:"end"`          // 结束时间
		Views        int       `json:"views"`        // 展示量
		Clicks       int       `json:"clicks"`       // 点击量
		Frequency    float64   `json:"frq"`          // 频率
		CTR          float64   `json:"ctr"`          // 点击率
		CPC          float64   `json:"cpc"`          // 点击成本
		Duration     int       `json:"duration"`     // 持续时间
		Spending     float64   `json:"sum"`          // 花费
	} `json:"stat"` // 统计数据
}

// KeywordsStatsResponse 关键词统计响应
type KeywordsStatsResponse struct {
	Keywords []struct {
		Date  string `json:"date"` // 日期
		Stats []struct {
			Clicks  int     `json:"clicks"`  // 点击量
			CTR     float64 `json:"ctr"`     // 点击率
			Keyword string  `json:"keyword"` // 关键词
			Sum     float64 `json:"sum"`     // 花费
			Views   int     `json:"views"`   // 展示量
		} `json:"stats"` // 统计数据
	} `json:"keywords"` // 关键词统计
}

// GetFullStats 获取广告活动完整统计数据
func (s *PromotionService) GetFullStats(reqs []*StatsRequest) ([]StatsResponse, error) {
	// 等待限流器许可（每分钟1次请求）
	if err := s.rateLimiter.fullStatsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}
	fmt.Println(gconv.String(reqs))
	var result []StatsResponse
	resp, err := s.client.R().
		SetBody(reqs).
		SetResult(&result).
		Post("/adv/v2/fullstats")

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, fmt.Errorf("get full stats failed: %s", resp.String())
	}

	return result, nil
}

// GetAutoStatWords 获取自动广告词组统计数据
func (s *PromotionService) GetAutoStatWords(advertID int) (*AutoStatWordsResponse, error) {
	// 等待限流器许可（每秒4次请求）
	if err := s.rateLimiter.autoStatWordsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	result := &AutoStatWordsResponse{}
	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", advertID)).
		SetResult(result).
		Get("/adv/v2/auto/stat-words")

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, fmt.Errorf("get auto stat words failed: %s", resp.String())
	}

	return result, nil
}

// GetStatWords 获取搜索广告词组统计数据
func (s *PromotionService) GetStatWords(advertID int) (*StatWordsResponse, error) {
	// 等待限流器许可（每秒4次请求）
	if err := s.rateLimiter.statWordsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	result := &StatWordsResponse{}
	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", advertID)).
		SetResult(result).
		Get("/adv/v1/stat/words")

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, fmt.Errorf("get stat words failed: %s", resp.String())
	}

	return result, nil
}

// GetKeywordsStats 获取关键词统计数据
func (s *PromotionService) GetKeywordsStats(advertID int, from, to time.Time) (*KeywordsStatsResponse, error) {
	// 等待限流器许可（每秒4次请求）
	if err := s.rateLimiter.keywordsStatsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	result := &KeywordsStatsResponse{}
	resp, err := s.client.R().
		SetQueryParams(map[string]string{
			"advert_id": fmt.Sprintf("%d", advertID),
			"from":      from.Format("2006-01-02"),
			"to":        to.Format("2006-01-02"),
		}).
		SetResult(result).
		Get("/adv/v0/stats/keywords")

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, fmt.Errorf("get keywords stats failed: %s", resp.String())
	}

	return result, nil
}

// GetMediaStats 获取媒体广告统计数据
func (s *PromotionService) GetMediaStats(req *StatsRequest) ([]StatsResponse, error) {
	// 等待限流器许可（每秒60次请求）
	if err := s.rateLimiter.mediaStatsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result []StatsResponse
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post("/adv/v1/stats")

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, fmt.Errorf("get media stats failed: %s", resp.String())
	}

	return result, nil
}
