package wbapi

import (
	"context"
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
)

const (
	contentBaseURL = "https://content-api.wildberries.ru" // 内容服务基础 URL
)

// ProductCardsService 商品卡片服务
type ProductCardsService struct {
	client      *resty.Client // 独立的HTTP客户端
	rateLimiter *PromotionRateLimiter
}

// newProductCardsService 创建新的商品卡片服务
func newProductCardsService(c *Client) *ProductCardsService {
	// 创建独立的HTTP客户端
	httpClient := resty.New()
	httpClient.SetBaseURL(contentBaseURL)
	// 复制原始客户端的通用设置
	if c.baseClient != nil {
		httpClient.SetTimeout(c.baseClient.GetClient().Timeout)
		// 复制认证头
		for k, v := range c.baseClient.Header {
			httpClient.SetHeader(k, v[0])
		}
	}

	return &ProductCardsService{
		client:      httpClient,
		rateLimiter: NewPromotionRateLimiter(),
	}
}

// CardListSettings 获取商品卡片列表的设置
type CardListSettings struct {
	Sort struct {
		Ascending bool `json:"ascending"` // 排序方向
	} `json:"sort,omitempty"`
	Filter struct {
		WithPhoto             int      `json:"withPhoto"`             // 图片筛选：-1全部，0无图片，1有图片
		TextSearch            string   `json:"textSearch,omitempty"`  // 搜索文本
		TagIDs                []int    `json:"tagIDs,omitempty"`      // 标签ID
		AllowedCategoriesOnly bool     `json:"allowedCategoriesOnly"` // 是否只显示允许的类目
		ObjectIDs             []int    `json:"objectIDs,omitempty"`   // 商品ID列表
		Brands                []string `json:"brands,omitempty"`      // 品牌列表
		ImtID                 int      `json:"imtID,omitempty"`       // 商品ImtID
	} `json:"filter,omitempty"`
	Cursor struct {
		UpdatedAt string `json:"updatedAt,omitempty"` // 更新时间游标
		NmID      int    `json:"nmID,omitempty"`      // 商品ID游标
		Limit     int    `json:"limit"`               // 返回数量限制
	} `json:"cursor,omitempty"`
}

// CardListRequest 获取商品卡片列表的请求
type CardListRequest struct {
	Settings CardListSettings `json:"settings"`
}

// Photo 商品图片信息
type Photo struct {
	Big      string `json:"big"`      // 900x1200 图片URL
	C246x328 string `json:"c246x328"` // 246x328 图片URL
	C516x688 string `json:"c516x688"` // 516x688 图片URL
	Square   string `json:"square"`   // 600x600 图片URL
	Tm       string `json:"tm"`       // 75x100 图片URL
}

// Dimension 商品尺寸信息
type Dimension struct {
	Length       int     `json:"length"`       // 长度(cm)
	Width        int     `json:"width"`        // 宽度(cm)
	Height       int     `json:"height"`       // 高度(cm)
	WeightBrutto float64 `json:"weightBrutto"` // 重量(kg)
	IsValid      bool    `json:"isValid"`      // 尺寸是否有效
}

// Characteristic 商品特征
type Characteristic struct {
	ID    int         `json:"id"`    // 特征ID
	Name  string      `json:"name"`  // 特征名称
	Value interface{} `json:"value"` // 特征值
}

// Size 商品尺码信息
type Size struct {
	ChrtID   int      `json:"chrtID"`   // 尺码ID
	TechSize string   `json:"techSize"` // 尺码值
	WbSize   string   `json:"wbSize"`   // WB尺码
	Skus     []string `json:"skus"`     // 条形码列表
}

// Tag 商品标签
type Tag struct {
	ID    int    `json:"id"`    // 标签ID
	Name  string `json:"name"`  // 标签名称
	Color string `json:"color"` // 标签颜色
}

// Card 商品卡片信息
type Card struct {
	NmID            int              `json:"nmID"`            // WB商品编号
	ImtID           int              `json:"imtID"`           // 合并商品ID
	NmUUID          string           `json:"nmUUID"`          // 系统UUID
	SubjectID       int              `json:"subjectID"`       // 类目ID
	SubjectName     string           `json:"subjectName"`     // 类目名称
	VendorCode      string           `json:"vendorCode"`      // 商家编号
	Brand           string           `json:"brand"`           // 品牌
	Title           string           `json:"title"`           // 标题
	Description     string           `json:"description"`     // 描述
	NeedKiz         bool             `json:"needKiz"`         // 是否需要标识码
	Photos          []Photo          `json:"photos"`          // 图片列表
	Video           string           `json:"video"`           // 视频URL
	Dimensions      Dimension        `json:"dimensions"`      // 尺寸信息
	Characteristics []Characteristic `json:"characteristics"` // 特征列表
	Sizes           []Size           `json:"sizes"`           // 尺码列表
	Tags            []Tag            `json:"tags"`            // 标签列表
	CreatedAt       time.Time        `json:"createdAt"`       // 创建时间
	UpdatedAt       time.Time        `json:"updatedAt"`       // 更新时间
}

// CardListResponse 获取商品卡片列表的响应
type CardListResponse struct {
	Cards  []Card `json:"cards"` // 商品卡片列表
	Cursor struct {
		UpdatedAt string `json:"updatedAt"` // 下一页更新时间
		NmID      int    `json:"nmID"`      // 下一页商品ID
		Total     int    `json:"total"`     // 总数
	} `json:"cursor"` // 分页信息
}

// GetCardsList 获取商品卡片列表
func (s *ProductCardsService) GetCardsList(req *CardListRequest, locale string) (*CardListResponse, error) {
	// 等待限流器许可
	if err := s.rateLimiter.cardsListLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}
	resp := &CardListResponse{}
	_, err := s.client.R().
		SetQueryParam("locale", locale).
		SetBody(req).
		SetResult(resp).
		Post("/content/v2/get/cards/list")

	if err != nil {
		return nil, err
	}

	return resp, nil
}
